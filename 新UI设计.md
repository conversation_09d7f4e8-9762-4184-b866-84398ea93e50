现在请为 wvCamer 项目开发一套全新的UI，新UI参照附图的设计，对该设计图的说明如下：
1、 UI主体是居中显示的 P2-R1 摄像机预览画面，由于iPad和iPhone有不同的屏幕宽高比，在iPad上显示效果如设计图所示，横向充满屏幕，上下仍有空白区域，空白区域将用于显示与摄像机交互的其他控件，而在iPhone上视频预览区域则纵向充满屏幕，左右留有少量空白，此时主界面顶部的控件将重叠在视频预览区域的顶部。
2、 与摄像机交互的控件主要集中在主界面的顶部，它们分成两行排列，第一行由左向右依次为：
    a、 光圈值按钮，显示当前的光圈值，点击展开可选的光圈列表，用户可从中选择其他可用光圈。
            获取光圈值：http://*************/ctrl/get?k=iris
            设置光圈值：http://*************/ctrl/set?iris=4
    b、 快门值按钮，显示当前的快门速度，点击展开可选的快门速度列表，业户可从中选择其他可用快门值。
            获取快门值：http://*************/ctrl/get?k=shutter_time
            设置快门值：http://*************/ctrl/set?shutter_time=1/50
    c、 ISO数值按钮，显示当前的ISO数值，点击此按钮将在屏幕居中展开ISO菜单，点击屏幕其他区域关闭菜单。
    d、 EV值按钮，显示当前的EV数值，点击展开可选的EV数值列表，用户可从中选择其他可用的EV值。
            获取EV值：http://*************/ctrl/get?k=ev
            设置EV值：http://*************/ctrl/set?ev=0
    e、 对焦按钮，显示当前的对焦模式，点击展开可选的对焦模式列表，用户可从中选择其他可用的对焦模式。
            获取对焦模式：http://*************/ctrl/get?k=focus
            设置对焦模式：http://*************/ctrl/set?focus=af
    f、 图像配置按钮，点击展开图像菜单，点击屏幕其他区域关闭菜单。
    g、 摄像机的IP地址，默认值为0.0.0.0，点击展开设备发现菜单，点击屏幕其他区域关闭菜单。
    h、 断开连接按钮，点击后程序断开与当前摄像机的连接，回归初始状态。
    i、 帧率按钮，显示当前的帧率，点击展开可选的帧率列表，用户可从中选择其他可用的帧率。
            获取帧率：http://*************/ctrl/get?k=project_fps
            设置帧率：http://*************/ctrl/set?project_fps=50
    j、 分辨率按钮，显示当前的分辨率，点击展开可选的分辨率列表，用户可从中选择其他可用的分辨率。
            获取分辨率：http://*************/ctrl/get?k=resolution
            设置分辨率：http://*************/ctrl/set?resolution=1920x1080
3、 视频预览区域下部的两侧为PTZ控制虚拟摇杆，PTZ控制功能及虚拟摇杆组件在当前项目中已经实现，可直接使用，无需额外开发。
4、 ISO设置菜单：
        a、点击ISO数值按钮展开ISO设置菜单，点击屏幕其他区域关闭此菜单
        b、菜单包含ISO、最小ISO和最大ISO共3个选项
        c、ISO选项显示设备当前的ISO数值，用户点击后展开设备支持的ISO列表，用户可从中选择其他可用的ISO值
            获取ISO值：http://*************/ctrl/get?k=iso
            设置ISO值：http://*************/ctrl/set?iso=800
        d、最小ISO选项显示设备支持的最小ISO值，用户点击后展开设备支持的ISO列表，用户可从中选择其他可用的ISO值
            获取最小ISO值：http://*************/ctrl/get?k=min_iso
            设置最小ISO值：http://*************/ctrl/set?min_iso=100
        e、最大ISO选项显示设备支持的最大ISO值，用户点击后展开设备支持的ISO列表，用户可从中选择其他可用的ISO值
            获取最大ISO值：http://*************/ctrl/get?k=max_iso
            设置最大ISO值：http://*************/ctrl/set?max_iso=1600
5、 图像菜单包含饱和度、对比度、亮度和白平衡设置选项：
        a、饱和度滑动条及与滑动条联动的数值编辑框，用户可通过滑动条或数值编辑框设置饱和度值，数值范围和当前值均通过get接口获取。
                http://*************/ctrl/get?k=saturation
                http://*************/ctrl/set?saturation=50
        b、 对比度滑动条及与滑动条联动的数值编辑框，用户可通过滑动条或数值编辑框设置对比度值，数值范围和当前值均通过get接口获取。
                http://*************/ctrl/get?k=contrast
                http://*************/ctrl/set?contrast=50
        c、 亮度滑动条及与滑动条联动的数值编辑框，用户可通过滑动条或数值编辑框设置亮度值，数值范围和当前值均通过get接口获取。
                http://*************/ctrl/get?k=brightness
                http://*************/ctrl/set?brightness=50
        d、 白平衡设置又分为：白平衡模式菜单、色温、色调、红色增益、绿色增益和蓝色增益，其中只有白平衡模式选择Manual时，色温和色调才可编辑，其他模式下色温和色调不可编辑，只有白平衡模式选择Expert时，红色增益、绿色增益和蓝色增益才可编辑。
                a、 白平衡模式菜单，用户点击后展开可选的白平衡模式，数值范围和当前值均通过get接口获取。
                        获取白平衡模式：http://*************/ctrl/get?k=wb
                        设置白平衡模式：http://*************/ctrl/set?wb=Manual
                        注意：通过get接口获取的可选白平衡模式并不包含Expert模式，需手动增加Expert模式选项在菜单中。
                b、 色温滑动条及与滑动条联动的数值编辑框，用户可通过滑动条或数值编辑框设置色温值，数值范围和当前值均通过get接口获取。
                        获取色温值：http://*************/ctrl/get?k=mwb
                        设置色温值：http://*************/ctrl/set?mwb=3000
                c、 色调滑动条及与滑动条联动的数值编辑框，用户可通过滑动条或数值编辑框设置色调值，数值范围和当前值均通过get接口获取。
                        获取色调值：http://*************/ctrl/get?k=tint
                        设置色调值：http://*************/ctrl/set?tint=0
                d、 红色增益滑动条及与滑动条联动的数值编辑框，用户可通过滑动条或数值编辑框设置红色增益值，数值范围和当前值均通过get接口获取。
                        获取红色增益值：http://*************/ctrl/get?k=gain_r
                        设置红色增益值：http://*************/ctrl/set?gain_r=100
                e、 绿色增益滑动条及与滑动条联动的数值编辑框，用户可通过滑动条或数值编辑框设置绿色增益值，数值范围和当前值均通过get接口获取。
                        获取绿色增益值：http://*************/ctrl/get?k=gain_g
                        设置绿色增益值：http://*************/ctrl/set?gain_g=100
                f、 蓝色增益滑动条及与滑动条联动的数值编辑框，用户可通过滑动条或数值编辑框设置蓝色增益值，数值范围和当前值均通过get接口获取。
                        获取蓝色增益值：http://*************/ctrl/get?k=gain_b
                        设置蓝色增益值：http://*************/ctrl/set?gain_b=100
6、 设备发现菜单，包含手动添加和自动扫描按钮，以及一个设备IP列表：
        a、点击手动添加按钮弹出一个输入框，允许用户输入设备的IP地址，在用户确认输入时需检查IP地址的合法性，非法的IP地址应提醒用户检查地址是否输入正确。
        b、点击自动扫描按钮后，设备自动扫描局域网中的 P2-R1 设备，该功能通过mDNS协议实现，当前项目中已经实现该功能，无需额外开发。
        c、自动扫描和手动添加的IP均展示在列表中，用户点击某个IP后关闭菜单，程序启动与该IP设备的连接，包括各项UI控件参数的获取和RTSP视频流的预览，其中RTSP视频流的播放功能已经实现，无需额外开发。
7、 以上所有控件对应的选项数值均可能发生动态的变化，设备将通过WebSocket连接上报各选项的变化，程序应当检测WebSocket的update消息，如果获取到的更新涉及到界面控件，应该及时更新控件的数值或选项。与设备见的WebSocket连接功能已经实现，无需额外开发。

UI风格要求：
        a、所有控件只显示文字，不需要边框和背景色。
        b、主界面顶部控件如光圈、快门等按钮点击后在按钮下方直接展开选项，选单也只显示文本，不需要边框和背景色等。
        c、ISO菜单、设备发现菜单展开后均在主屏幕居中显示，图像菜单在主屏幕底部居中展示
        d、选项不可用时（未连接设备，或设备不支持该选项）控件显示为灰色，且不可交互
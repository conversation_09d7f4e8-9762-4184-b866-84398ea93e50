# AI检测消息解析测试说明

## 测试环境设置

### 1. 模拟摄像机服务器
模拟P2-R1摄像机服务器已启动：
- **地址**: `localhost:8080`
- **协议**: WebSocket
- **心跳间隔**: 11秒
- **AI消息间隔**: 8秒

### 2. 测试步骤

#### 步骤1: 在iOS应用中添加模拟摄像机
1. 打开iOS模拟器中的wbCamer应用
2. 在摄像机列表中添加新摄像机：
   - **名称**: Mock P2-R1 Camera
   - **IP地址**: `127.0.0.1` 或 `localhost`
   - **端口**: `8080`
   - **类型**: P2-R1

#### 步骤2: 连接到模拟摄像机
1. 点击连接到模拟摄像机
2. 应用应该能够成功建立WebSocket连接
3. 观察连接状态变为"已连接"

#### 步骤3: 观察AI检测消息处理
连接成功后，模拟服务器会自动发送以下消息：

1. **心跳消息** (每11秒):
   ```json
   {
     "what": "heartbeat",
     "timestamp": 1234567890
   }
   ```

2. **AI检测消息** (每8秒循环发送):
   
   **消息1 - 单个人脸**:
   ```json
   {
     "what": "AI",
     "pending": 0,
     "num": 1,
     "cols": 8,
     "objs": [1, 101, 1, 0.95, 0.3, 0.2, 0.15, 0.25]
   }
   ```
   
   **消息2 - 人脸和人**:
   ```json
   {
     "what": "AI",
     "pending": 0,
     "num": 2,
     "cols": 8,
     "objs": [1, 201, 1, 0.92, 0.25, 0.15, 0.12, 0.20, 1, 202, 2, 0.88, 0.6, 0.3, 0.18, 0.35]
   }
   ```
   
   **消息3 - 无目标**:
   ```json
   {
     "what": "AI",
     "pending": 0,
     "num": 0,
     "cols": 8,
     "objs": []
   }
   ```
   
   **消息4 - 移动目标**:
   ```json
   {
     "what": "AI",
     "pending": 0,
     "num": 1,
     "cols": 8,
     "objs": [1, 301, 4, 0.82, 0.45, 0.35, 0.25, 0.3]
   }
   ```

### 3. 预期结果

#### 修复前的问题:
- 日志显示"目标数据不完整，跳过"
- 最终创建的有效目标数量为0
- AI检测框不显示

#### 修复后的预期结果:
- 日志显示成功解析AI检测数据
- 正确提取objs数组中的目标信息
- 显示AI检测框覆盖层
- 不同类型的目标显示不同颜色的框

### 4. 日志监控

在Xcode控制台中观察以下日志：

```
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
📊 [Parse AI] 数据列数: 8, objs数组长度: 8
🔍 [Parse AI] 解析目标 0
📍 [Parse AI] 目标 0 原始数据: app=1, id=101, type=1, conf=0.95, x=0.3, y=0.2, w=0.15, h=0.25
📐 [Parse AI] 目标 0 归一化坐标: x=0.300, y=0.200, w=0.150, h=0.250
✅ [Parse AI] 成功创建目标 0: 人脸, 置信度=0.95
🎯 [Parse AI] 解析完成，共创建 1 个有效目标
```

### 5. 目标类型说明

- **类型1**: 人脸 (Face) - 红色框
- **类型2**: 人 (Person) - 绿色框  
- **类型3**: 头部 (Head) - 蓝色框
- **类型4**: 移动目标 (Motion) - 黄色框

### 6. 坐标系统

- **归一化坐标**: objs数组中的x, y, width, height都是0.0-1.0之间的归一化值
- **屏幕坐标**: 应用会将归一化坐标转换为实际的屏幕像素坐标
- **坐标原点**: 左上角为(0,0)

### 7. 故障排除

如果连接失败：
1. 确认模拟服务器正在运行 (检查终端输出)
2. 确认iOS应用中的IP地址和端口设置正确
3. 检查网络权限设置
4. 查看Xcode控制台的错误日志

如果AI检测框不显示：
1. 检查AIDetectionOverlayView是否正确添加到视图层次
2. 确认AI检测数据解析成功
3. 检查坐标转换逻辑
4. 验证绘制逻辑

## 测试完成标准

✅ WebSocket连接成功建立  
✅ 心跳消息正常接收  
✅ AI检测消息成功解析  
✅ 目标数据正确提取  
✅ AI检测框正确显示  
✅ 不同类型目标显示不同颜色  
✅ 坐标转换正确  
✅ 无解析错误日志  

当所有标准都满足时，说明AI检测消息解析功能修复成功。
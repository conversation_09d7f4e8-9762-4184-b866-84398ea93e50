# WebSocket消息处理分析 - websocketMessageHandle方法

## 方法概述
`websocketMessageHandle(e)`是处理WebSocket消息的核心方法，根据消息类型`what`字段进行分发处理，主要涉及配置更新、设备状态、录制控制等功能。

## 消息类型分类及处理逻辑

### 1. 配置变更类消息 (ConfigChanged)
- **触发条件**: `what`字段为"ConfigChanged"
- **参数**: 
  - `key`: 配置项名称
  - `value`: 配置值
- **处理逻辑**:
  - 根据不同的`key`调用相应的配置更新方法
  - 详细配置项分类及处理方法:

#### 1.1 分辨率/帧率相关配置
- **配置项**: `resolution`, `project_fps`, `movvfr`, `movfmt`, `rec_fps`
- **处理方法**:
  - `updateRecordSettings()`
  - `updateStreamingSettings()`
  - `updateCameraConfig("sys_hdmi_res", "hdmi_fmt")`
  - `updateCameraConfig("sys_sht_op", "sht_operation")`
  - `updateCameraConfig("zoom_mode", "zoom_mode")`
  - `updateCameraConfig("eis_on_off", "eis_on_off")`

#### 1.2 录制设置相关配置
- **配置项**: `preroll`, `video_system`, `record_file_format`, `split_duration`, `rec_fps`, `raw_over_hdmi`, `rec_frame_indicator`, `vfr_ctrl`, `preroll_duration`, `rotation`
- **处理方法**: `updateRecordSettings()`

#### 1.3 录制模式相关配置
- **配置项**: `record_mode`
- **处理方法**:
  - `updateMovieSettings()`
  - `updateSliderSpinnerConfig("video_tl_interval")`

#### 1.4 视频编码相关配置
- **配置项**: `video_encoder`, `bitrate_level`, `rec_proxy_file`, `vid_rot`, `compose_mode`, `crop_sensor`, `low_jello`, `photo_q`, `eis_on_off`
- **处理方法**: `updateMovieSettings()`

#### 1.5 曝光相关配置
- **曝光补偿**:
  - **配置项**: `ev`
  - **处理方法**:
    - `updateCameraConfig("exp_ev", "ev_choice")`
    - `updateCameraConfig("exp_ae_speed", "ae_speed")`

- **光圈**:
  - **配置项**: `iris`
  - **处理方法**: 带500ms防抖的`updateCameraConfig("exp_iris", "iris")`

- **ISO/增益**:
  - **配置项**: `iso`, `db`
  - **处理方法**:
    - `updateCameraConfig("exp_ev", "ev_choice")`
    - `updateCameraConfig("exp_iso", "iso")`
    - `updateCameraConfig("exp_min_iso", "min_iso")`
    - `updateCameraConfig("exp_max_iso", "max_iso")`
    - `updateCameraConfig("exp_ae_speed", "ae_speed")`

- **快门时间**:
  - **配置项**: `shutter_time`
  - **处理方法**:
    - `updateCameraConfig("exp_ev", "ev_choice")`
    - `updateCameraConfig("exp_sht_time", "shutter_time")`
    - `updateCameraConfig("max_exp_sht_time", "max_exp_shutter_time")`
    - `updateCameraConfig("exp_ae_speed", "ae_speed")`

- **快门角度**:
  - **配置项**: `shutter_angle`
  - **处理方法**:
    - `updateCameraConfig("exp_ev", "ev_choice")`
    - `updateCameraConfig("exp_sht_angle", "shutter_angle")`
    - `updateCameraConfig("max_exp_sht_time", "max_exp_shutter_angle")`

- **微快门角度**:
  - **配置项**: `micro_shutter_angle`
  - **处理方法**:
    - `updateCameraConfig("exp_m_shutter_angle", "micro_shutter_angle")`
    - `updateCameraConfig("max_exp_sht_time", "max_exp_shutter_angle")`
    - `updateCameraConfig("exp_ev", "ev_choice")`

- **电子ND**:
  - **配置项**: `eND`
  - **处理方法**: `updateCameraConfig("exp_e_nd", "eND")`

- **其他曝光设置**:
  - **配置项**: `flicker`, `meter_mode`, `sht_operation`, `max_exp_shutter_angle`, `max_exp_shutter_time`, `iso_ctrl`, `min_iso`, `max_iso`, `lock_ae_in_rec`, `bl_comp`, `shutter_angle_ctrl`, `ev_choice`, `ae_speed`
  - **处理方法**: `updateExposureSettings()`

#### 1.6 白平衡相关配置
- **配置项**: `wb`, `wb_priority`, `lock_awb_in_rec`
- **处理方法**: `updateWhiteBalanceSettings()`

- **手动白平衡相关**:
  - **配置项**: `mwb`, `tint`, `mwb_r`, `mwb_g`, `mwb_b`
  - **处理方法**: 
    - `intervalUpdateSliderSpinnerConfig(t.key, t)`
    - 对于`mwb`还会调用`updateMwb(t.value)`

#### 1.7 音频相关配置
- **配置项**: `primary_audio`, `audio_channel`, `audio_phantom_power`, `audio_level_display`, `ain_gain_type`, `audio_input_gain`, `audio_noise_reduction`, `audio_input_level`
- **处理方法**: `updateAudioSettings()`

- **音频增益相关**:
  - **配置项**: `audio_in_l_gain`, `audio_in_r_gain`, `audio_output_gain`
  - **处理方法**: `intervalUpdateSliderSpinnerConfig(t.key, t)`

#### 1.8 图像设置相关配置
- **配置项**: `lut`, `sharpness`, `noise_reduction`, `ois_mode`, `vignette`, `luma_level`
- **处理方法**: `updateImageSettings()`

- **图像调整相关**:
  - **配置项**: `brightness`, `contrast`, `saturation`, `hue`
  - **处理方法**: `intervalUpdateSliderSpinnerConfig(t.key, t)`

#### 1.9 对焦设置相关配置
- **配置项**: `focus`, `af_mode`, `caf`, `caf_sens`, `af_area`, `live_caf`, `mf_mag`, `mf_recording`, `af_speed`, `zoom_mode`, `af_adjust_with_ptz`
- **处理方法**: `updateFocusSettings()`

- **镜头对焦位置**:
  - **配置项**: `lens_focus_pos`
  - **处理方法**: `updateLensZoomFocusConfig(t.key)`

#### 1.10 系统设置相关配置
- **配置项**: `hdmi_fmt`, `hdmi_osd`, `osd_layout`, `use_edid`, `led`, `desqueeze`, `tally_on`, `usb_device_role`, `ir`, `sdi`, `3g_sdi_mode`, `ir_id`, `color_bar_enable`, `genlock`, `visca_enable`, `visca_id`, `visca_baud_rate`
- **处理方法**: `updateSystemSettings()`

#### 1.11 维护设置相关配置
- **配置项**: `auto_off`, `auto_standby`
- **处理方法**: `updateMaintanenceSettings()`

#### 1.12 辅助工具设置相关配置
- **配置项**: `assitool_display`, `assitool_scope`, `assitool_peak_onoff`, `assitool_peak_color`, `assitool_frame_line`, `assitool_frame_line_color`, `assitool_center_mark`, `assitool_center_mark_color`, `assitool_exposure`, `hdmi_focus_area`, `assitool_grid_line`, `assitool_safe_area`
- **处理方法**: `updateAssitoolSettings()`

- **辅助工具阈值相关**:
  - **配置项**: `assitool_zera_th1`, `assitool_zera_th2`
  - **处理方法**: `intervalUpdateSliderSpinnerConfig(t.key, t)`

#### 1.13 流媒体设置相关配置
- **配置项**: `stream_video_encoder`, `stream_resolution`, `stream_fps`, `stream_param_save`, `ndi_multicast_enable`
- **处理方法**: `updateStreamingSettings()`

#### 1.14 网络设置相关配置
- **配置项**: `wifi`, `wifi_channel`, `eth_mode`
- **处理方法**: `updateNetworkSettings()`

#### 1.15 时间码相关配置
- **配置项**: `tc_source`, `tc_count_up`, `tc_hdmi_dispaly`, `tc_drop_frame`
- **处理方法**: `updateTimeCodeTab()`

#### 1.16 视频延时摄影相关配置
- **配置项**: `enable_video_tl`
- **处理方法**: `updateVideoTimelapse()`

- **延时摄影间隔**:
  - **配置项**: `video_tl_interval`
  - **处理方法**: `intervalUpdateSliderSpinnerConfig(t.key, t)`

#### 1.17 认证相关配置
- **配置项**: `http_auth`
- **处理方法**: 
  - 当值为"1"时: `updateAuthenticationStatus()`
  - 其他情况: `updateCameraConfig("sys_auth", "http_auth")`

#### 1.18 多相机设置相关配置
- **配置项**: `union_ae`, `union_awb`, `ezlink_mode`, `ezlink_trigger`
- **处理方法**: `updateMultiCameraSettings()`

#### 1.19 云台设置相关配置
- **配置项**: `pt_speedmode`, `ptz_flip`, `ptz_limit`, `ptz_preset_mode`, `ptz_speed_mode`, `freeze_during_preset`, `pt_pwr_pos`, `pt_priv_mode`, `preset_adjust_speed_with_zoom`, `pt_speed_with_zoom_pos`, `ptz_common_speed_unit`
- **处理方法**: `updatePtzSettings()`

- **云台速度相关**:
  - **配置项**: `ptz_common_speed`, `ptz_common_time`
  - **处理方法**: `intervalUpdateSliderSpinnerConfig(t.key, t)`

#### 1.20 HTTPS证书相关配置
- **配置项**: `https_cert_source`
- **处理方法**: `updateCameraConfig("sys_https_cert_source", "https_cert_source")`

#### 1.21 实时曝光信息相关配置
- **配置项**: `live_ae_shutter`
- **处理方法**: `updateSht(t.value)`

- **配置项**: `live_ae_iso`
- **处理方法**: `updateISOText(t.value)`

#### 1.22 电池相关配置
- **配置项**: `battery_voltage`
- **处理方法**: `updateBatteryVoltageLable(t.value)`

- **配置项**: `battery`
- **处理方法**: `updateBatteryPercentLable(t.value)`

#### 1.23 同步锁相位相关配置
- **配置项**: `gl_shf_coarse`, `gl_shf_fine`
- **处理方法**: `intervalUpdateSliderSpinnerConfig(t.key, t)`

#### 1.24 LCD背光相关配置
- **配置项**: `lcd_backlight`
- **处理方法**: `intervalUpdateSliderSpinnerConfig(t.key, t)`

### 2. 设备状态类消息
- **camera_status**: 
  - **处理方法**: `updateCameraStatus()`

- **CardMounted**: 
  - **处理方法**: 
    - `updateCardStatus()`
    - `updateFormatCardBtn()`
    - `checkCardFormat()`

- **CardUnmounted**: 
  - **处理方法**: 
    - `updateCardStatus()`
    - `updateFormatCardBtn()`
    - `closeAbnormalFormatDialog()`

- **CardUnknown**: 
  - **处理方法**: 
    - 设置`camera.unsupport_card = true`
    - `showUnknownFormatDialog()`

- **WebrtcStream**: 
  - **参数**: `value`
  - **处理方法**: 
    - 当值为"connect"时: `webRtcServerConnect()`, `hideStreamStatus()`
    - 其他情况: `webRtcServerDisconnect()`, `showStreamStatus()`

- **LenAttached**: 
  - **处理方法**: 延迟500ms后执行
    - `updateFocusSettings()`
    - `updateLensZoomFocusConfig("lens_zoom_pos")`
    - `updateCameraConfig("exp_iris", "iris")`

- **ZoomUpdated**: 
  - **参数**: `code`
  - **处理方法**: 
    - 当code为0且zoomContrl不为null时: `zoomContrl.intervalUpdate(t)`
    - 当code为0且camera.ezframingCtrl不为null时: `camera.ezframingCtrl.updateFramingRoiRatio(t)`

- **GenlockSyncStatus**: 
  - **处理方法**: `updateCameraStatus()`

- **PresetStatus**: 
  - **参数**: `value`
  - **处理方法**: 当camera.ptCtrl不为null时: `camera.ptCtrl.updatePresetStatus(t.value)`

### 3. 录制控制类消息
- **RecStarted**: 
  - **处理方法**: 
    - 设置`camera.recording = true`
    - `updateRecRemaining()`
    - `updateRecordBtn()`
    - `updateRecordSettings()`
    - `updateMovieSettings()`
    - `updateAudioSettings()`
    - `updateSystemSettings()`
    - `updateVideoTimelapse()`
    - `updateTimeCodeTab()`

- **RecStoped**: 
  - **处理方法**: 
    - 设置`camera.recording = false`
    - 设置`camera.prerolling = false`
    - `updateRecordDuration(camera.recording, 0)`
    - `updateRecordBtn()`
    - `updateRecordSettings()`
    - `updateMovieSettings()`
    - `updateAudioSettings()`
    - `updateSystemSettings()`
    - `updateVideoTimelapse()`
    - `updateTimeCodeTab()`

- **RecUpdateDur**: 
  - **参数**: `index`, `value`
  - **处理方法**: 当index为1时
    - 当value大于0: `updateRecordDuration(true, t.value)`
    - 其他情况: `updateRecordDuration(false, 0)`

- **RecUpdateRemain**: 
  - **参数**: `value`
  - **处理方法**: `updateRemainLable(t.value)`

- **PreRollStarted**: 
  - **处理方法**: 
    - 设置`camera.prerolling = true`
    - `updateCameraConfig("preroll", "preroll")`

### 4. 系统事件类消息
- **TempUpdate**: 
  - **参数**: `value`
  - **处理方法**: `updateTempLable(t.value)`

- **AI**: 
  - **处理方法**: 
    - 当camera.framingCtrl不为null时: `camera.framingCtrl.framingDrawAiDetectionOnCanvas(t)`
    - 当camera.afFaceCtrl不为null时: `camera.afFaceCtrl.drawAiDetectionOnCanvas(t)`

- **ModeChanged**: 
  - **参数**: `value`
  - **处理方法**: 
    - 当值为CAMERA_MODE_MOVIE时: 
      - `updateCameraModeStatus(CAMERA_MODE_MOVIE)`
      - `enableSpinner(false)`
      - `lockCtrlUIForMode(false)`
      - 关闭所有打开的对话框
      - `ensureInRecordingMode()`
    - 当值为CAMERA_MODE_PLAYBACK时: 
      - `lockCtrlUIForMode(true)`
      - `showPlaybackModeDialog()`
      - `stopStreamStatTimer()`
      - `updateRtmpSrtStatus()`
    - 当值为CAMERA_MODE_STANDBY时: 
      - `lockCtrlUIForMode(true)`
      - `stopStreamStatTimer()`
      - `updateRtmpSrtStatus()`
      - `updateCameraModeStatus(CAMERA_MODE_STANDBY)`
    - 其他情况: 
      - `lockCtrlUIForMode(true)`
      - `updateCameraModeStatus(CAMERA_MODE_UNKNOWN)`
      - `showUnknownModeDialog()`
      - `stopStreamStatTimer()`
      - `updateRtmpSrtStatus()`

- **HeadphonePlug**: 
  - **处理方法**: `updateSliderSpinnerConfig("audio_output_gain")`

- **LtcPlug**: 
  - **处理方法**: `updateCameraConfig("tc_source", "tc_source")`

- **UsbPlug**: 
  - **处理方法**: `updateFormatCardBtn()`

- **SystemTimeChange**: 
  - **处理方法**: 
    - `webRtcServerConnect()`
    - `updateDateTime()`

- **PtzTraceSt**: 
  - **参数**: `val`
  - **处理方法**: 当camera.ptCtrl不为null时
    - `camera.ptCtrl.updatePtTraceBtnStatus(t.val)`
    - `updatePtzfCurrentPresetTracePage(false)`

- **hdmi_input**: 
  - **处理方法**: `updateCameraConfig("sys_hdmi_res", "hdmi_fmt")`

### 5. 设置更新类消息
- **UpdateStreamSetting**: 
  - **参数**: `rtmpIsRunning`, `srtIsRunning`(可选)
  - **处理方法**: 
    - `updateStreamingSettings()`
    - 当rtmpIsRunning不为undefined时: `updateRtmpStatTimer(t.rtmpIsRunning)`
    - 当srtIsRunning不为undefined时: `updateSrtStatTimer(t.srtIsRunning)`

- **ClearSetting**: 
  - **处理方法**: 设置`camera.isClearSetting = true`

- **StreamSettingsChanged**: 
  - **处理方法**: `updateCameraConfig("encoder", "video_encoder")`

- **NicknameChange**: 
  - **参数**: `nickname`
  - **处理方法**: `updateCameraName(t.nickname)`

### 6. 对焦相关消息
- **UpdateAfArea**: 
  - **参数**: `roi_x`, `roi_y`, `roi_w`, `roi_h`
  - **处理方法**: `updateAfRoiEvent(t.roi_x, t.roi_y, t.roi_w, t.roi_h)`

- **AfDone**: 
  - **处理方法**: `updateLensZoomFocusConfig("lens_focus_pos")`

### 7. 取景相关消息
- **FramingStatus/FramingStatusChanged/FramingSettingChanged/FramingInfoUpdate/FacedbEvent/FramingEvent**: 
  - **处理方法**: `updateFramingStatus(t)`

- **zoom_stop**: 
  - **参数**: `code`
  - **处理方法**: 
    - 当code为0且zoomContrl和focusContrl不为null时: 
      - `zoomContrl.clearUpdateCount()`
      - `zoomContrl.intervalUpdate(t)`
      - `focusContrl.intervalUpdate()`
    - 当camera.ezframingCtrl不为null时: `camera.ezframingCtrl.updateFramingRoiForZoomStoped(t)`
    - `updateFramingStatus(t)`

### 8. 图像调整相关消息
- **HueSatColorChange**: 
  - **参数**: `type`
  - **处理方法**: 当imageControl不为null时
    - 当type为"MatrixChanged": `imageControl.updateMatrixValue(t)`
    - 当type为"ColorCorrectChanged": `imageControl.updateColorCorrectValue(t)`

- **MasterGammaChange**: 
  - **处理方法**: `updateMasterGammaStatus(t)`

### 9. 预设相关消息
- **PresetChange**: 
  - **参数**: `index`
  - **处理方法**: `updatePtzfCurrentPresetTracePage(true, t.index)`

- **RepairFileDone**: 
  - **处理方法**: 
    - `updateRepairingFileStatus()`
    - `updateCameraConfig("preroll", "preroll")`

### 10. 系统控制类消息
- **hb**: 
  - **处理方法**: `heartBeatHandle()`

- **Shutdown**: 
  - **处理方法**: `websocketCloseHandle()`

## 方法调用关系图
```mermaid
graph TD
  A[websocketMessageHandle] --> B{what类型判断}
  B -->|ConfigChanged| C[根据key调用不同update方法]
  C -->|分辨率/帧率相关| C1[updateRecordSettings/updateStreamingSettings]
  C -->|曝光相关| C2[updateExposureSettings/updateCameraConfig]
  C -->|白平衡相关| C3[updateWhiteBalanceSettings]
  C -->|音频相关| C4[updateAudioSettings]
  C -->|图像相关| C5[updateImageSettings]
  C -->|对焦相关| C6[updateFocusSettings]
  C -->|系统相关| C7[updateSystemSettings]
  
  B -->|设备状态类| D[设备状态更新]
  D -->|camera_status| D1[updateCameraStatus]
  D -->|Card相关| D2[updateCardStatus/updateFormatCardBtn]
  D -->|镜头相关| D3[updateFocusSettings/updateLensZoomFocusConfig]
  
  B -->|录制控制类| E[录制状态更新]
  E -->|RecStarted| E1[设置recording=true/更新UI]
  E -->|RecStoped| E2[设置recording=false/更新UI]
  E -->|RecUpdate相关| E3[updateRecordDuration/updateRemainLable]
  
  B -->|系统事件类| F[系统事件处理]
  F -->|TempUpdate| F1[updateTempLable]
  F -->|ModeChanged| F2[根据模式更新UI]
  F -->|设备插拔| F3[更新相关设置]
  
  B -->|特殊处理| G[其他特殊消息处理]
  G -->|hb| G1[heartBeatHandle]
  G -->|Shutdown| G2[websocketCloseHandle]
  G -->|图像调整| G3[updateMatrixValue/updateColorCorrectValue]
```

## 注意事项
1. 部分配置变更有防抖处理(如iris设置500ms延迟)
2. 状态更新会同步多个相关UI组件
3. 异常情况会记录到控制台日志
4. 部分消息处理会根据当前相机模式执行不同逻辑
5. 心跳消息(hb)用于维持WebSocket连接，超时(11秒)未收到会关闭连接
6. 多个配置项变更可能会触发同一个更新方法，以保证UI状态一致性
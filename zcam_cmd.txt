https://github.com/imaginevision/Z-Camera-Doc/blob/master/E2/protocol/http/http.md
https://github.com/imaginevision/Z-Camera-Doc/blob/master/E2/protocol/http/api.js

���ŽǶȣ�shutter_angle
REQ��http://*************/ctrl/get?k=shutter_angle
RSP��{"code":0,"desc":"string","key":"shutter_angle","type":1,"ro":0,"value":"Auto","opts":["Auto","1��","2��","4��","5��","8��","10��","11.25��","15��","22.5��","30��","37.5��","45��","60��","72��","75��","90��","108��","120��","144��","150��","172.8��","180��","216��","270��","324��","360��"],"all":[]}
REQ: http://*************/ctrl/set?shutter_angle=90��
RSP: {"code":0,"desc":"","msg":""}

�����ٶȣ�shutter_time
REQ: http://*************/ctrl/get?k=shutter_time
RSP: {"code":0,"desc":"string","key":"shutter_time","type":1,"ro":0,"value":"1/30","opts":["Auto","1/25","1/30","1/40","1/48","1/50","1/60","1/80","1/100","1/120","1/125","1/160","1/200","1/240","1/250","1/320","1/400","1/500","1/640","1/800","1/1000","1/1250","1/1600","1/2000","1/2500","1/3200","1/4000","1/5000","1/6400","1/8000"],"all":[]}
REQ: http://*************/ctrl/set?shutter_time=1/50
RSP: {"code":0,"desc":"","msg":""}

����ģʽ�л���sht_operation
REQ: http://*************/ctrl/get?k=sht_operation
RSP: {"code":0,"desc":"string","key":"sht_operation","type":1,"ro":0,"value":"Speed","opts":["Speed","Angle"],"all":[]}
REQ: http://*************/ctrl/set?sht_operation=Speed
RSP: {"code":0,"desc":"","msg":""}

��Ȧ��iris
REQ: http://*************/ctrl/get?k=iris
RSP: {"code":0,"desc":"string","key":"iris","type":1,"ro":0,"value":"2.8","opts":["2.8","3.2","3.5","4","4.5","5","5.6","6.3","7.1","8","9","10","11","13","14","16","18","20","22"],"all":[]}
REQ: http://*************/ctrl/set?iris=4
RSP: {"code":0,"desc":"","msg":""}

�й�ȣ�iso
REQ: http://*************/ctrl/get?k=iso
RSP: {"code":0,"desc":"string","key":"iso","type":1,"ro":0,"value":"Auto","opts":["Auto","800","1000","1250","1600","2000","2500","3200","4000","5000","6400","8000","10000","12800","16000","20000","25600"],"all":[]}
REQ: http://*************/ctrl/set?iso=800
RSP: {"code":0,"desc":"","msg":""}

�عⲹ����ev
REQ��http://*************/ctrl/get?k=ev
RSP��{"code":0,"desc":"string","key":"ev","type":2,"ro":0,"value":0,"min":-96,"max":96,"step":1}
REQ��http://*************/ctrl/set?ev=0
RSP��{"code":0,"desc":"","msg":""}

����Զ�ISO��max_iso
REQ: http://*************/ctrl/get?k=max_iso
RSP: {"code":0,"desc":"string","key":"max_iso","type":1,"ro":1,"value":"12800","opts":["1600","2000","2500","3200","4000","5000","6400","8000","10000","12800","16000","20000","25600"],"all":[]}
REQ: http://*************/ctrl/set?max_iso=10000
RSP: {"code":0,"desc":"","msg":""}

��ƽ��ģʽ��wb
REQ: http://*************/ctrl/get?k=wb
RSP: {"code":0,"desc":"string","key":"wb","type":1,"ro":0,"value":"Auto","opts":["Auto","Incandescent","Cloudy","D10000","Fluorescent","Daylight","Shade","Manual"],"all":[]}
REQ: http://*************/ctrl/set?wb=Manual
RSP: {"code":0,"desc":"","msg":""}

�ֶ���ƽ�⣺mwb
REQ: http://*************/ctrl/get?k=mwb
RSP: {"code":0,"desc":"string","key":"mwb","type":2,"ro":1,"value":5400,"min":2300,"max":7500,"step":100}
REQ: http://*************/ctrl/set?mwb=5000
RSP: {"code":0,"desc":"","msg":""}

ɫ��ƫ�ƣ�tint
REQ: http://*************/ctrl/get?k=tint
RSP: {"code":0,"desc":"string","key":"tint","type":2,"ro":1,"value":0,"min":-100,"max":100,"step":1}
REQ: http://*************/ctrl/set?tint=5
RSP: {"code":0,"desc":"","msg":""}

���Ͷȣ�saturation
REQ: http://*************/ctrl/get?k=saturation
RSP: {"code":0,"desc":"string","key":"saturation","type":2,"ro":0,"value":50,"min":0,"max":100,"step":1}
REQ: http://*************/ctrl/set?saturation=5
RSP: {"code":0,"desc":"","msg":""}

�ֱ��ʣ�resolution
REQ: http://*************/ctrl/get?k=resolution
RSP: {"code":0,"desc":"string","key":"resolution","type":1,"ro":0,"value":"1920x1080","opts":["4K","2880x2880","1920x1080"],"all":[]}
REQ: http://*************/ctrl/set?resolution=4K
RSP: {"code":0,"desc":"","msg":""}

֡�ʣ�project_fps
REQ: http://*************/ctrl/get?k=project_fps
RSP: {"code":0,"desc":"string","key":"project_fps","type":1,"ro":0,"value":"25","opts":["23.98","24","25","29.97"],"all":["23.98","24","25","29.97","50","59.94"]}
REQ: http://*************/ctrl/set?project_fps=25
RSP: {"code":0,"desc":"","msg":""}

�������send_stream
REQ: http://*************/ctrl/get?k=send_stream
RSP: {"code":0,"desc":"string","key":"send_stream","type":1,"ro":0,"value":"Stream1","opts":["Stream0","Stream1"],"all":[]}
REQ: http://*************/ctrl/set?send_stream=Stream0
RSP: {"code":0,"desc":"","msg":""}

�����룺
REQ��http://*************/ctrl/stream_setting?index=stream1&venc=h265&bitwidth=8
RSP��{"code":0,"desc":"","msg":""}

���ֱ��ʣ�
REQ��http://*************/ctrl/stream_setting?index=stream1&width=2880&height=2880
RSP��{"code":0,"desc":"","msg":""}

��״̬��
REQ��http://*************/ctrl/stream_setting?index=stream1&action=query
RSP��{"streamIndex":"stream1","encoderType":"h264","bitwidth":"8bit","width":1080,"height":1080,"fps":25,"bitrate":4000,"gop_n":25,"rotation":0,"splitDuration":300,"status":"streaming"}

������ʣ�bitrate
REQ: http://192.168.10.13/ctrl/stream_setting?index=stream1&bitrate=30000000
RSP: {"code":0,"desc":"","msg":""}

�Խ�λ�ã�lens_focus_pos
REQ: http://*************/ctrl/get?k=lens_focus_pos
RSP: {"code":0,"desc":"string","key":"lens_focus_pos","type":2,"ro":0,"value":2852,"min":2408,"max":3734,"step":1}
REQ: http://*************/ctrl/set?lens_focus_pos=2850
RSP: {"code":0,"desc":"","msg":""}

�Խ��ƶ���mf_drive
REQ: http://*************/ctrl/get?k=mf_drive
RSP: {"code":0,"desc":"string","key":"mf_drive","type":2,"ro":0,"value":0,"min":-3,"max":3,"step":1}
REQ: http://*************/ctrl/set?mf_drive=1
RSP: {"code":0,"desc":"","msg":""}

������reboot
REQ: http://*************/ctrl/reboot
RSP: {"code":0,"desc":"","msg":""}

�ػ���shutdown
REQ: http://*************/ctrl/shutdown
RSP: {"code":0,"desc":"","msg":""}

NTP time��sntp
REQ: http://*************/ctrl/sntp?action=start&ip_addr=*************&port=123
REQ: http://*************/ctrl/sntp?action=stop

RTMP��
REQ: http://*************/ctrl/rtmp?action=start&url=rtmp://vrpushgs.stream.moguv.com/live/1
REQ: http://*************/ctrl/rtmp?action=stop

http://*************/ctrl/stream_setting?index=stream1&width=2880&height=2880&bitrate=30000000&venc=h265&bitwidth=8

http://**********/ctrl/network?action=set&mode=Static&ipaddr=***********&netmask=*************&gateway=**********




B1�Խ����ƣ�
http://**************/ctrl/get?k=lens_focus_pos
http://**************/ctrl/lens?action=focusfar&fspeed=0.1
http://**************/ctrl/lens?action=focusnear&fspeed=0.1
http://**************/ctrl/lens?action=focusstop
http://**************/ctrl/set?lens_focus_pos=200		// ��Ҫ��MFģʽ��

B1�佹���ƣ�
http://**************/ctrl/get?k=lens_zoom_pos
http://**************/ctrl/lens?action=zoomin&fspeed=1
http://**************/ctrl/lens?action=zoomout&fspeed=0.5
http://**************/ctrl/lens?action=zoomstop
http://**************/ctrl/set?lens_zoom_pos=400

R1:
zoom: {
		in: e => this.request("/ctrl/lens?action=zoomin&fspeed=" + e),
		out: e => this.request("/ctrl/lens?action=zoomout&fspeed=" + e),
		stop: () => this.request("/ctrl/lens?action=zoomstop"),
		status: () => this.request("/ctrl/lens?action=z_status"),
		zoomMode: () => this.ctrl.get("zoom_mode"),
		setZoomMode: e => this.ctrl.set("zoom_mode", e)
        },
focus: {
		near: e => this.request("/ctrl/lens?action=focusnear&fspeed=" + e),
		far: e => this.request("/ctrl/lens?action=focusfar&fspeed=" + e),
		stop: () => this.request("/ctrl/lens?action=focusstop"),
            status: () => this.request("/ctrl/lens?action=f_status")
        },
af: {
		onePush: () => this.request("/ctrl/af"),
		afSpeed: () => this.ctrl.get("af_speed"),
		setAfSpeed: e => this.ctrl.set("af_speed", e),
		focusMethod: () => this.ctrl.get("focus"),
		setFocusMethod: e => this.ctrl.set("focus", e),
		roiType: () => this.ctrl.get("af_mode"),
		setRoiType: e => this.ctrl.set("af_mode", e),
		updateRoiCenter: (e, t) => this.request(`/ctrl/af?action=update_roi_center&x=${e}&y=` + t),
		getRoi: () => this.request("/ctrl/af?action=query"),
		flexiableSize: () => this.ctrl.get("af_area"),
		setFlexiableSize: e => this.ctrl.set("af_area", e),
		cafSensitivity: () => this.ctrl.get("caf_sens"),
		setCafSensitivity: e => this.ctrl.set("caf_sens", e),
		afAdjustWithPtz: () => this.ctrl.get("af_adjust_with_ptz"),
		setAfAdjustWithPtz: e => this.ctrl.set("af_adjust_with_ptz", e),
		traceFaceId: e => this.request("/ctrl/af_face/trace_target?id=" + e)
        }
pt = {
        query: () => this.request("/ctrl/pt?action=query"),
        queryDetail: () => this.request("/ctrl/pt?action=query&detail=y"),
        stop: () => this.request("/ctrl/pt?action=stop"),
        stopAll: () => this.request("/ctrl/pt?action=stop_all"),
        directionMove: (e, t) => this.request(`/ctrl/pt?action=${e}&fspeed=` + t),
        ptMove: (e, t) => this.request(`/ctrl/pt?action=pt&pan_speed=${e}&tilt_speed=` + t),
        home: () => this.request("/ctrl/pt?action=home"),
        reset: () => this.request("/ctrl/pt?action=reset"),
        limitUpdate: (e, t, a) => this.request(`/ctrl/pt?action=limit&direct=${e}&pan_pos=${t}&tilt_pos=` + a),
        setLimit: e => this.ctrl.set("ptz_limit", e),
        limit: () => this.ctrl.get("ptz_limit"),
        speedMode: () => this.ctrl.get("pt_speedmode"),
        setSpeedMode: e => this.ctrl.set("pt_speedmode", e),
        speedWithZoomPos: () => this.ctrl.get("pt_speed_with_zoom_pos"),
        setSpeedWithZoomPos: e => this.ctrl.set("pt_speed_with_zoom_pos", e),
        flip: () => this.ctrl.get("ptz_flip"),
        setFlip: e => this.ctrl.set("ptz_flip", e),
        privacyMode: () => this.ctrl.get("pt_priv_mode"),
        setPrivacyMode: e => this.ctrl.set("pt_priv_mode", e),
        powerOnPosition: () => this.ctrl.get("pt_pwr_pos"),
        setPowerOnPosition: e => this.ctrl.set("pt_pwr_pos", e)
    };
preset = {
        recall: e => this.request("/ctrl/preset?action=recall&index=" + e),
        save: e => this.request("/ctrl/preset?action=set&index=" + e),
        delete: e => this.request("/ctrl/preset?action=del&index=" + e),
        getInfo: e => this.request("/ctrl/preset?action=get_info&index=" + e),
        setName: (e, t) => this.request(`/ctrl/preset?action=set_name&index=${e}&new_name=` + encodeURIComponent(t)),
        setSpeedUnit: (e, t) => this.request(`/ctrl/preset?action=preset_speed&index=${e}&preset_speed_unit=` + t),
        setSpeedByDuraton: (e, t) => this.request(`/ctrl/preset?action=preset_speed&index=${e}&preset_time=` + t),
        setSpeedByIdex: (e, t) => this.request(`/ctrl/preset?action=preset_speed&index=${e}&preset_speed=` + t),
        getCommonSpeed: () => this.ctrl.get("ptz_common_speed"),
        setCommonSpeed: e => this.ctrl.set("ptz_common_speed", e),
        getCommonTime: () => this.ctrl.get("ptz_common_time"),
        setCommonTime: e => this.ctrl.set("ptz_common_time", e),
        recallMode: () => this.ctrl.get("ptz_preset_mode"),
        setRecallMode: e => this.ctrl.set("ptz_preset_mode", e),
        recallSpeedMode: () => this.ctrl.get("ptz_speed_mode"),
        setRecallSpeedMode: e => this.ctrl.set("ptz_speed_mode", e),
        freezeDuringRecall: () => this.ctrl.get("freeze_during_preset"),
        setFreezeDuringRecall: e => this.ctrl.set("freeze_during_preset", e),
        ptzCommonSpeedUnit: () => this.ctrl.get("ptz_common_speed_unit"),
        setPtzCommonSpeedUnit: e => this.ctrl.set("ptz_common_speed_unit", e)
    };
	
	http://192.168.214.21/ctrl/get?k=ptz_speed_mode
	{"code":0,"desc":"string","key":"ptz_speed_mode","type":1,"ro":0,"value":"Common","opts":["Common","Separate"],"all":[]}
	http://192.168.214.21/ctrl/set?ptz_speed_mode=Common
	
	http://192.168.214.21/ctrl/get?k=ptz_common_speed_unit
	{"code":0,"desc":"string","key":"ptz_common_speed_unit","type":1,"ro":0,"value":"Time","opts":["Index","Time"],"all":[]}
	http://192.168.214.21/ctrl/set?ptz_common_speed_unit=Time
	
	http://192.168.214.21/ctrl/get?k=ptz_common_time
	{"code":0,"desc":"string","key":"ptz_common_time","type":2,"ro":0,"value":6000,"min":1000,"max":60000,"step":1000}
	http://192.168.214.21/ctrl/set?ptz_common_time=10000
	
framing = {
        enableDetect: e => this.request("/ctrl/framing/detect?enable=" + e),
        enableTrace: e => this.request("/ctrl/framing/trace?enable=" + e),
        setTraceTarget: e => this.request("/ctrl/framing/trace_target?id=" + e),
        setStartPos: e => this.request("/ctrl/framing/start_position?index=" + e),
        setFovType: e => this.request("/ctrl/framing/fov?type=" + e),
        setFovBodySize: e => this.request("/ctrl/framing/fov?body_size=" + e),
        setFovFaceHeight: e => this.request("/ctrl/framing/fov?face_height=" + e),
        setFovPlacement: e => this.request("/ctrl/framing/fov?placement=" + e),
        debugFov: () => this.request("/ctrl/framing/debug_fov"),
        setAutoStart: e => this.request("/ctrl/framing/auto_start?enable=" + e),
        restart: () => this.request("/ctrl/framing/restart"),
        setAutoTarget: e => this.request("/ctrl/framing/auto_target?enable=" + e),
        setTargetLostAction: e => this.request("/ctrl/framing/target_lost?action=" + e),
        setTargetLostTimeout: e => this.request("/ctrl/framing/target_lost?timeout_sec=" + e),
        getTargetLostTimeout: () => this.request("/ctrl/framing/target_lost?action=query"),
        setDetectRestartTime: e => this.request("/ctrl/framing/detect_restart?time=" + e),
        getDetectRestartTime: () => this.request("/ctrl/framing/detect_restart?action=query"),
        setLostActionWait: e => this.request("/ctrl/framing/manual_target?wait_time=" + e),
        getLostActionWait: () => this.request("/ctrl/framing/manual_target?action=query"),
        status: () => this.request("/ctrl/framing/status"),
        allStatus: () => this.request("/ctrl/framing/all_status"),
        info: () => this.request("/ctrl/framing/info"),
        tally: e => this.request("/ctrl/framing/tally?enable=" + e),
        maskEnable: e => this.request("/ctrl/framing/mask_area?enable=" + e),
        maskArea: () => this.request("/ctrl/framing/mask_area"),
        setMaskArea: (e, t, a, i) => this.request(`/ctrl/framing/mask_area?left=${e}&top=${t}&right=${a}&bottom=` + i)
    };
facedb = {
        query: () => this.request("/ctrl/facedb/query"),
        prepare: e => this.request("/ctrl/facedb/prepare?face_id=" + e),
        add: (e, t, a, i) => this.request(`/ctrl/facedb/add?which=${e}&name=${t}&priority=${a}&activate=` + i),
        update: (e, t, a, i) => this.request(`/ctrl/facedb/update?uid=${e}&name=${t}&priority=${a}&activate=` + i),
        delete: e => this.request("/ctrl/facedb/delete?uid=" + e),
        enable: e => this.request("/ctrl/facedb/set?enable=" + e),
        status: () => this.request("/ctrl/facedb/status")
    };
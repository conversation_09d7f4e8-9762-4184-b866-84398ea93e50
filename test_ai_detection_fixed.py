#!/usr/bin/env python3
"""
测试修复后的AI检测消息处理
发送P2-R1格式的AI检测消息到WebSocket服务器
"""

import asyncio
import websockets
import json
import time

async def test_ai_detection():
    uri = "ws://*************:81/"  # 替换为实际的摄像机IP
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ 连接到WebSocket服务器")
            
            # 测试消息1：包含一个人脸目标
            test_message_1 = {
                "what": "AI",
                "pending": 0,
                "num": 1,
                "obj0_x": 640.0,    # 屏幕中心
                "obj0_y": 360.0,
                "obj0_w": 120.0,
                "obj0_h": 160.0,
                "obj0_id": 1,
                "obj0_type": 3,      # 人脸类型
                "obj0_conf": 0.95
            }
            
            print("📤 发送测试消息1（包含人脸目标）")
            await websocket.send(json.dumps(test_message_1))
            await asyncio.sleep(1)
            
            # 测试消息2：包含两个目标（人和头部）
            test_message_2 = {
                "what": "AI",
                "pending": 0,
                "num": 2,
                "obj0_x": 300.0,
                "obj0_y": 200.0,
                "obj0_w": 80.0,
                "obj0_h": 120.0,
                "obj0_id": 1,
                "obj0_type": 1,      # 人类型
                "obj0_conf": 0.88,
                "obj1_x": 800.0,
                "obj1_y": 150.0,
                "obj1_w": 60.0,
                "obj1_h": 80.0,
                "obj1_id": 2,
                "obj1_type": 2,      # 头部类型
                "obj1_conf": 0.92
            }
            
            print("📤 发送测试消息2（包含两个目标）")
            await websocket.send(json.dumps(test_message_2))
            await asyncio.sleep(1)
            
            # 测试消息3：无目标
            test_message_3 = {
                "what": "AI",
                "pending": 0,
                "num": 0
            }
            
            print("📤 发送测试消息3（无目标）")
            await websocket.send(json.dumps(test_message_3))
            await asyncio.sleep(1)
            
            # 测试消息4：移动的目标
            for i in range(5):
                x_pos = 400 + i * 50
                y_pos = 300 + i * 20
                
                test_message_moving = {
                    "what": "AI",
                    "pending": 0,
                    "num": 1,
                    "obj0_x": float(x_pos),
                    "obj0_y": float(y_pos),
                    "obj0_w": 100.0,
                    "obj0_h": 140.0,
                    "obj0_id": 1,
                    "obj0_type": 3,      # 人脸类型
                    "obj0_conf": 0.90
                }
                
                print(f"📤 发送移动目标消息 {i+1}/5 (位置: {x_pos}, {y_pos})")
                await websocket.send(json.dumps(test_message_moving))
                await asyncio.sleep(0.5)
            
            print("✅ 所有测试消息发送完成")
            print("请检查iOS应用中的AI检测框是否正确显示")
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("请确保：")
        print("1. 摄像机IP地址正确")
        print("2. 摄像机WebSocket服务正在运行")
        print("3. 网络连接正常")

if __name__ == "__main__":
    print("🧪 开始测试修复后的AI检测消息处理")
    print("📝 测试内容：")
    print("   - 单个人脸目标")
    print("   - 多个目标（人+头部）")
    print("   - 无目标情况")
    print("   - 移动目标序列")
    print()
    
    asyncio.run(test_ai_detection())
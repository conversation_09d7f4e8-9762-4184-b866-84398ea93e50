#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI检测消息测试脚本
用于模拟发送AI检测消息到WebSocket服务器
"""

import json
import asyncio
import websockets
import time

# 模拟AI检测消息
def create_ai_detection_message():
    return {
        "what": "AI",
        "pending": 0,
        "num": 2,
        "obj0_x": 100.0,
        "obj0_y": 200.0,
        "obj0_w": 50.0,
        "obj0_h": 80.0,
        "obj0_id": 1,
        "obj0_type": 3,  # face
        "obj0_conf": 0.95,
        "obj1_x": 300.0,
        "obj1_y": 150.0,
        "obj1_w": 60.0,
        "obj1_h": 90.0,
        "obj1_id": 2,
        "obj1_type": 1,  # person
        "obj1_conf": 0.88
    }

async def test_ai_detection():
    # 这里需要替换为实际的WebSocket服务器地址
    # 由于我们没有实际的服务器，这个脚本主要用于演示消息格式
    
    print("🧪 AI检测消息测试")
    print("=" * 50)
    
    # 创建测试消息
    ai_message = create_ai_detection_message()
    
    print("📦 生成的AI检测消息:")
    print(json.dumps(ai_message, indent=2, ensure_ascii=False))
    
    print("\n✅ 消息格式验证:")
    print(f"   - 消息类型: {ai_message['what']}")
    print(f"   - 目标数量: {ai_message['num']}")
    
    for i in range(ai_message['num']):
        print(f"   - 目标{i}: ID={ai_message[f'obj{i}_id']}, 类型={ai_message[f'obj{i}_type']}, 置信度={ai_message[f'obj{i}_conf']}")
        print(f"     位置: ({ai_message[f'obj{i}_x']}, {ai_message[f'obj{i}_y']})")
        print(f"     尺寸: {ai_message[f'obj{i}_w']} x {ai_message[f'obj{i}_h']}")
    
    print("\n💡 使用说明:")
    print("   1. 确保应用已连接到WebSocket服务器")
    print("   2. 服务器需要发送类似上述格式的AI检测消息")
    print("   3. 检查应用日志中的AI检测处理流程")
    
if __name__ == "__main__":
    asyncio.run(test_ai_detection())
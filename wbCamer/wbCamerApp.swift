//
//  wbCamerApp.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import SwiftUI

@main
struct wbCamerApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        WindowGroup {
            LandscapeMainView()
                .environment(\.deviceType, DeviceTypeDetector.shared.deviceType)
        }
    }
}

struct RootView: View {
    @StateObject private var cameraManager = CameraManager.shared

    var body: some View {
        let _ = print("[RootView] Body refresh - isConnected: \(cameraManager.isConnected)")

        if cameraManager.isConnected {
            let _ = print("[RootView] ✅ Camera connected, showing MainTabView")
            MainTabView()
        } else {
            let _ = print("[RootView] ❌ Camera not connected, showing ContentView")
            ContentView()
        }
    }
}

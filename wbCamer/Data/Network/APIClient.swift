//
//  APIClient.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine

// MARK: - API Error Types

enum APIError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case networkError(Error)
    case serverError(Int, String?)
    case unauthorized
    case forbidden
    case notFound
    case timeout
    case sslError
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .noData:
            return "No data received"
        case .decodingError(let error):
            return "Decoding error: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .serverError(let code, let message):
            return "Server error (\(code)): \(message ?? "Unknown error")"
        case .unauthorized:
            return "Unauthorized access"
        case .forbidden:
            return "Access forbidden"
        case .notFound:
            return "Resource not found"
        case .timeout:
            return "Request timeout"
        case .sslError:
            return "SSL certificate error"
        }
    }
}

// MARK: - HTTP Method

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

// MARK: - API Request Protocol

protocol APIRequest {
    associatedtype Response: Codable
    
    var path: String { get }
    var method: HTTPMethod { get }
    var headers: [String: String]? { get }
    var parameters: [String: Any]? { get }
    var body: Data? { get }
    var timeout: TimeInterval { get }
}

extension APIRequest {
    var headers: [String: String]? { nil }
    var parameters: [String: Any]? { nil }
    var body: Data? { nil }
    var timeout: TimeInterval { 30.0 }
}

// MARK: - Request Interceptor Protocol

protocol RequestInterceptor {
    func intercept(request: URLRequest) -> URLRequest
}

// MARK: - Response Interceptor Protocol

protocol ResponseInterceptor {
    func intercept(data: Data?, response: URLResponse?, error: Error?) -> Result<Data, APIError>
}

// MARK: - API Client

class APIClient: ObservableObject {
    static let shared = APIClient()

    private let session: URLSession
    private var baseURL: URL
    private var requestInterceptors: [RequestInterceptor] = []
    private var responseInterceptors: [ResponseInterceptor] = []
    private let cache: URLCache
    
    // MARK: - Initialization
    
    init(baseURL: String = "", 
         configuration: URLSessionConfiguration = .default) {
        
        // 如果baseURL为空，使用临时占位符，实际使用时会通过updateBaseURL更新
        let urlString = baseURL.isEmpty ? "http://localhost" : baseURL
        guard let url = URL(string: urlString) else {
            fatalError("Invalid base URL: \(baseURL)")
        }
        
        self.baseURL = url
        
        // Configure cache
        self.cache = URLCache(memoryCapacity: 50 * 1024 * 1024, // 50MB
                             diskCapacity: 100 * 1024 * 1024,   // 100MB
                             diskPath: "api_cache")
        
        // Configure session
        configuration.urlCache = cache
        configuration.requestCachePolicy = .useProtocolCachePolicy
        configuration.timeoutIntervalForRequest = 30.0
        configuration.timeoutIntervalForResource = 60.0
        
        // SSL Configuration
        configuration.tlsMinimumSupportedProtocolVersion = .TLSv12
        
        self.session = URLSession(configuration: configuration)
        
        // Add default interceptors
        addDefaultInterceptors()
    }
    
    // MARK: - Configuration Management

    func updateBaseURL(to urlString: String) {
        guard let url = URL(string: urlString) else {
            print("Invalid base URL: \(urlString)")
            return
        }
        self.baseURL = url
        print("APIClient base URL updated to: \(urlString)")
    }

    // MARK: - Interceptor Management

    func addRequestInterceptor(_ interceptor: RequestInterceptor) {
        requestInterceptors.append(interceptor)
    }

    func addResponseInterceptor(_ interceptor: ResponseInterceptor) {
        responseInterceptors.append(interceptor)
    }
    
    private func addDefaultInterceptors() {
        // Add logging interceptor
        addRequestInterceptor(LoggingRequestInterceptor())
        addResponseInterceptor(LoggingResponseInterceptor())
        
        // Add authentication interceptor
        addRequestInterceptor(AuthenticationInterceptor())
        
        // Add error handling interceptor
        addResponseInterceptor(ErrorHandlingInterceptor())
    }
    
    // MARK: - Request Methods
    
    func request<T: APIRequest>(_ request: T) -> AnyPublisher<T.Response, APIError> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(.networkError(NSError(domain: "APIClient", code: -1, userInfo: [NSLocalizedDescriptionKey: "APIClient deallocated"]))))
                return
            }
            
            do {
                let urlRequest = try self.buildURLRequest(from: request)
                let finalRequest = self.applyRequestInterceptors(to: urlRequest)
                
                let task = self.session.dataTask(with: finalRequest) { data, response, error in
                    let result = self.applyResponseInterceptors(data: data, response: response, error: error)
                    
                    switch result {
                    case .success(let data):
                        do {
                            let decodedResponse = try JSONDecoder().decode(T.Response.self, from: data)
                            promise(.success(decodedResponse))
                        } catch {
                            promise(.failure(.decodingError(error)))
                        }
                    case .failure(let apiError):
                        promise(.failure(apiError))
                    }
                }
                
                task.resume()
                
            } catch {
                promise(.failure(.networkError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Request Building
    
    private func buildURLRequest<T: APIRequest>(from request: T) throws -> URLRequest {
        var components = URLComponents(url: baseURL.appendingPathComponent(request.path), resolvingAgainstBaseURL: true)
        
        // Add query parameters for GET requests
        if request.method == .GET, let parameters = request.parameters {
            components?.queryItems = parameters.map { URLQueryItem(name: $0.key, value: "\($0.value)") }
        }
        
        guard let url = components?.url else {
            throw APIError.invalidURL
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = request.method.rawValue
        urlRequest.timeoutInterval = request.timeout
        
        // Set headers
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Accept")
        
        request.headers?.forEach { key, value in
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }
        
        // Set body for non-GET requests
        if request.method != .GET {
            if let body = request.body {
                urlRequest.httpBody = body
            } else if let parameters = request.parameters {
                urlRequest.httpBody = try JSONSerialization.data(withJSONObject: parameters)
            }
        }
        
        return urlRequest
    }
    
    // MARK: - Interceptor Application
    
    private func applyRequestInterceptors(to request: URLRequest) -> URLRequest {
        return requestInterceptors.reduce(request) { currentRequest, interceptor in
            interceptor.intercept(request: currentRequest)
        }
    }
    
    private func applyResponseInterceptors(data: Data?, response: URLResponse?, error: Error?) -> Result<Data, APIError> {
        return responseInterceptors.reduce(.success(data ?? Data())) { currentResult, interceptor in
            switch currentResult {
            case .success(let data):
                return interceptor.intercept(data: data, response: response, error: error)
            case .failure:
                return currentResult
            }
        }
    }
}

// MARK: - Cache Management

extension APIClient {
    func clearCache() {
        cache.removeAllCachedResponses()
    }
    
    func getCacheSize() -> Int {
        return cache.currentMemoryUsage + cache.currentDiskUsage
    }
}
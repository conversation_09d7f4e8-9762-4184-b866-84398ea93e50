//
//  NetworkMonitor.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Network
import Combine
import os.log

// MARK: - Network Status Types

enum NetworkStatus: Equatable {
    case unknown
    case notReachable
    case reachable(NetworkType)
}

enum NetworkType: Equatable {
    case wifi
    case cellular
    case wiredEthernet
    case other
}

enum NetworkQuality: String, CaseIterable {
    case poor = "poor"
    case fair = "fair"
    case good = "good"
    case excellent = "excellent"
    case unknown = "unknown"

    var displayName: String {
        switch self {
        case .poor: return "差"
        case .fair: return "一般"
        case .good: return "良好"
        case .excellent: return "优秀"
        case .unknown: return "未知"
        }
    }

    var color: String {
        switch self {
        case .poor: return "red"
        case .fair: return "orange"
        case .good: return "yellow"
        case .excellent: return "green"
        case .unknown: return "gray"
        }
    }
}

// MARK: - Network Info

struct NetworkInfo {
    let status: NetworkStatus
    let quality: NetworkQuality
    let isExpensive: Bool
    let isConstrained: Bool
    let supportsIPv4: Bool
    let supportsIPv6: Bool
    let interfaceName: String?
    let timestamp: Date
    
    var isConnected: Bool {
        switch status {
        case .reachable:
            return true
        default:
            return false
        }
    }
    
    var connectionType: String {
        switch status {
        case .reachable(let type):
            switch type {
            case .wifi:
                return "WiFi"
            case .cellular:
                return "Cellular"
            case .wiredEthernet:
                return "Ethernet"
            case .other:
                return "Other"
            }
        case .notReachable:
            return "Not Connected"
        case .unknown:
            return "Unknown"
        }
    }
}

// MARK: - Network Monitor

class NetworkMonitor: ObservableObject {
    static let shared = NetworkMonitor()
    
    // MARK: - Published Properties
    @Published var networkInfo: NetworkInfo
    @Published var isConnected: Bool = false
    @Published var connectionType: NetworkType = .wifi
    @Published var isExpensive: Bool = false
    @Published var isConstrained: Bool = false
    
    // MARK: - Private Properties
    private let monitor: NWPathMonitor
    private let queue = DispatchQueue(label: "NetworkMonitor", qos: .utility)
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "NetworkMonitor")
    
    // Combine
    private let networkStatusSubject = CurrentValueSubject<NetworkInfo, Never>(
        NetworkInfo(
            status: .unknown,
            quality: .unknown,
            isExpensive: false,
            isConstrained: false,
            supportsIPv4: false,
            supportsIPv6: false,
            interfaceName: nil,
            timestamp: Date()
        )
    )
    
    var networkStatusPublisher: AnyPublisher<NetworkInfo, Never> {
        networkStatusSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    
    init() {
        self.monitor = NWPathMonitor()
        self.networkInfo = NetworkInfo(
            status: .unknown,
            quality: .unknown,
            isExpensive: false,
            isConstrained: false,
            supportsIPv4: false,
            supportsIPv6: false,
            interfaceName: nil,
            timestamp: Date()
        )
        
        setupMonitoring()
    }
    
    // MARK: - Monitoring Setup
    
    private func setupMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            self?.handlePathUpdate(path)
        }
        
        monitor.start(queue: queue)
        logger.info("Network monitoring started")
    }
    
    private func handlePathUpdate(_ path: NWPath) {
        let networkInfo = analyzeNetworkPath(path)
        
        DispatchQueue.main.async {
            self.updateNetworkInfo(networkInfo)
        }
    }
    
    private func analyzeNetworkPath(_ path: NWPath) -> NetworkInfo {
        let status = determineNetworkStatus(path)
        let quality = determineNetworkQuality(path)
        let interfaceName = getInterfaceName(path)
        
        return NetworkInfo(
            status: status,
            quality: quality,
            isExpensive: path.isExpensive,
            isConstrained: path.isConstrained,
            supportsIPv4: path.supportsIPv4,
            supportsIPv6: path.supportsIPv6,
            interfaceName: interfaceName,
            timestamp: Date()
        )
    }
    
    private func determineNetworkStatus(_ path: NWPath) -> NetworkStatus {
        guard path.status == .satisfied else {
            return .notReachable
        }
        
        if path.usesInterfaceType(.wifi) {
            return .reachable(.wifi)
        } else if path.usesInterfaceType(.cellular) {
            return .reachable(.cellular)
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .reachable(.wiredEthernet)
        } else {
            return .reachable(.other)
        }
    }
    
    private func determineNetworkQuality(_ path: NWPath) -> NetworkQuality {
        // This is a simplified quality assessment
        // In a real implementation, you might want to perform actual network tests
        
        if path.status != .satisfied {
            return .unknown
        }
        
        if path.isExpensive {
            return .fair // Cellular connections are often more limited
        }
        
        if path.isConstrained {
            return .poor
        }
        
        if path.usesInterfaceType(.wifi) {
            return .good // Assume WiFi is generally good quality
        }
        
        return .fair
    }
    
    private func getInterfaceName(_ path: NWPath) -> String? {
        // Get the primary interface name
        for interface in path.availableInterfaces {
            if path.usesInterfaceType(interface.type) {
                return interface.name
            }
        }
        return nil
    }
    
    private func updateNetworkInfo(_ info: NetworkInfo) {
        self.networkInfo = info
        self.isConnected = info.isConnected
        self.isExpensive = info.isExpensive
        self.isConstrained = info.isConstrained
        
        if case .reachable(let type) = info.status {
            self.connectionType = type
        }
        
        networkStatusSubject.send(info)
        
        logger.info("Network status updated: \(info.connectionType), Quality: \(String(describing: info.quality))")
    }
    
    // MARK: - Public Methods
    
    func startMonitoring() {
        guard monitor.queue == nil else {
            logger.info("Network monitoring already started")
            return
        }
        
        monitor.start(queue: queue)
        logger.info("Network monitoring started")
    }
    
    func stopMonitoring() {
        monitor.cancel()
        logger.info("Network monitoring stopped")
    }
    
    // MARK: - Network Testing
    
    func testNetworkLatency(to host: String = "*******", completion: @escaping (TimeInterval?) -> Void) {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        guard let url = URL(string: "http://\(host)") else {
            completion(nil)
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "HEAD"
        request.timeoutInterval = 5.0
        
        URLSession.shared.dataTask(with: request) { _, response, error in
            let latency = CFAbsoluteTimeGetCurrent() - startTime
            
            if error == nil && response != nil {
                completion(latency)
            } else {
                completion(nil)
            }
        }.resume()
    }
    
    func testDownloadSpeed(completion: @escaping (Double?) -> Void) {
        // Test download speed with a small file
        guard let url = URL(string: "http://httpbin.org/bytes/1024") else {
            completion(nil)
            return
        }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        URLSession.shared.dataTask(with: url) { data, response, error in
            let endTime = CFAbsoluteTimeGetCurrent()
            let duration = endTime - startTime
            
            if let data = data, error == nil, duration > 0 {
                let bytesPerSecond = Double(data.count) / duration
                let mbps = (bytesPerSecond * 8) / 1_000_000 // Convert to Mbps
                completion(mbps)
            } else {
                completion(nil)
            }
        }.resume()
    }
    
    // MARK: - Camera Network Specific
    
    func testCameraConnectivity(to ipAddress: String, port: Int = 80, completion: @escaping (Bool, TimeInterval?) -> Void) {
        let host = NWEndpoint.Host(ipAddress)
        let port = NWEndpoint.Port(integerLiteral: UInt16(port))
        let endpoint = NWEndpoint.hostPort(host: host, port: port)
        
        let connection = NWConnection(to: endpoint, using: .tcp)
        let startTime = CFAbsoluteTimeGetCurrent()
        
        connection.stateUpdateHandler = { state in
            let latency = CFAbsoluteTimeGetCurrent() - startTime
            
            switch state {
            case .ready:
                connection.cancel()
                completion(true, latency)
                
            case .failed(let error):
                self.logger.error("Camera connectivity test failed: \(error.localizedDescription)")
                connection.cancel()
                completion(false, nil)
                
            case .cancelled:
                break
                
            default:
                break
            }
        }
        
        connection.start(queue: queue)
        
        // Timeout after 10 seconds
        DispatchQueue.global().asyncAfter(deadline: .now() + 10) {
            if connection.state != .ready && connection.state != .cancelled {
                connection.cancel()
                completion(false, nil)
            }
        }
    }
    
    func scanLocalNetwork(completion: @escaping ([String]) -> Void) {
        // Simple local network scan for camera devices
        // This is a basic implementation - in production you might want to use more sophisticated discovery
        
        guard isConnected else {
            completion([])
            return
        }
        
        let dispatchGroup = DispatchGroup()
        var discoveredHosts: [String] = []
        let queue = DispatchQueue.concurrent(label: "network.scan")
        
        // Scan common camera IP ranges
        let baseIP = "192.168.1" // This should be determined from current network
        
        for i in 1...254 {
            dispatchGroup.enter()
            
            queue.async {
                let ip = "\(baseIP).\(i)"
                
                self.testCameraConnectivity(to: ip, port: 80) { isReachable, _ in
                    if isReachable {
                        discoveredHosts.append(ip)
                    }
                    dispatchGroup.leave()
                }
            }
        }
        
        dispatchGroup.notify(queue: .main) {
            completion(discoveredHosts.sorted())
        }
    }
}

// MARK: - Network Quality Assessment

extension NetworkMonitor {
    func assessNetworkQualityForStreaming(completion: @escaping (NetworkQuality) -> Void) {
        guard isConnected else {
            completion(.unknown)
            return
        }
        
        let group = DispatchGroup()
        var latency: TimeInterval?
        var downloadSpeed: Double?
        
        // Test latency
        group.enter()
        testNetworkLatency { result in
            latency = result
            group.leave()
        }
        
        // Test download speed
        group.enter()
        testDownloadSpeed { result in
            downloadSpeed = result
            group.leave()
        }
        
        group.notify(queue: .main) {
            let quality = self.calculateQualityScore(latency: latency, downloadSpeed: downloadSpeed)
            completion(quality)
        }
    }
    
    private func calculateQualityScore(latency: TimeInterval?, downloadSpeed: Double?) -> NetworkQuality {
        var score = 0
        
        // Latency scoring (0-50 points)
        if let latency = latency {
            if latency < 0.05 { // < 50ms
                score += 50
            } else if latency < 0.1 { // < 100ms
                score += 40
            } else if latency < 0.2 { // < 200ms
                score += 30
            } else if latency < 0.5 { // < 500ms
                score += 20
            } else {
                score += 10
            }
        }
        
        // Download speed scoring (0-50 points)
        if let speed = downloadSpeed {
            if speed > 50 { // > 50 Mbps
                score += 50
            } else if speed > 25 { // > 25 Mbps
                score += 40
            } else if speed > 10 { // > 10 Mbps
                score += 30
            } else if speed > 5 { // > 5 Mbps
                score += 20
            } else if speed > 1 { // > 1 Mbps
                score += 10
            }
        }
        
        // Convert score to quality
        switch score {
        case 80...100:
            return .excellent
        case 60...79:
            return .good
        case 40...59:
            return .fair
        case 20...39:
            return .poor
        default:
            return .poor
        }
    }
}

// MARK: - Convenience Extensions

extension DispatchQueue {
    static func concurrent(label: String, qos: DispatchQoS = .default) -> DispatchQueue {
        return DispatchQueue(label: label, qos: qos, attributes: .concurrent)
    }
}
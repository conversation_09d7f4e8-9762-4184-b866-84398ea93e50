//
//  OptimizedWebRTCClient.swift
//  wbCamer
//
//  Created by Assistant on 2024-12-19.
//  优化版本的WebRTC客户端，专注于减少延迟
//

import Foundation
@preconcurrency import WebRTC
import Combine

// MARK: - Signaling Message Types

struct SignalingMessage: Codable {
    let type: SignalingType
    let sdp: String?
    let candidate: ICECandidateData?
}

enum SignalingType: String, Codable {
    case offer = "offer"
    case answer = "answer"
    case iceCandidate = "ice-candidate"
}

struct ICECandidateData: Codable {
    let candidate: String
    let sdpMLineIndex: Int32
    let sdpMid: String?
}

/// 优化的WebRTC客户端，专注于低延迟视频流
@MainActor
class OptimizedWebRTCClient: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var connectionState: RTCPeerConnectionState = .new
    @Published var iceConnectionState: RTCIceConnectionState = .new
    @Published var isConnected: Bool = false
    @Published var remoteVideoTrack: RTCVideoTrack?
    @Published var localVideoTrack: RTCVideoTrack?
    
    // MARK: - Private Properties
    private var peerConnection: RTCPeerConnection?
    private var peerConnectionFactory: RTCPeerConnectionFactory
    private var iceServers: [RTCIceServer] = []
    private var localDataChannel: RTCDataChannel?
    private var remoteDataChannel: RTCDataChannel?
    
    // WebSocket for signaling
    private var signalingClient: WebSocketManager?

    // Combine
    private var cancellables = Set<AnyCancellable>()

    // 低延迟配置
    private let lowLatencyConfig: LowLatencyWebRTCConfig
    
    // MARK: - Initialization
    override init() {
        // 使用硬件加速解码器工厂
        let decoderFactory = HardwareAcceleratedDecoderFactory()
        let encoderFactory = HardwareAcceleratedEncoderFactory()
        
        self.peerConnectionFactory = RTCPeerConnectionFactory(
            encoderFactory: encoderFactory,
            decoderFactory: decoderFactory
        )
        
        // 初始化低延迟配置
        self.lowLatencyConfig = LowLatencyWebRTCConfig()
        
        super.init()
        
        setupOptimizedConfiguration()
    }
    
    // MARK: - Public Methods
    
    /// 连接到指定的摄像机（优化版本）
    func connect(to cameraIP: String, port: Int = 8000) async throws {
        print("[OptimizedWebRTCClient] Connecting to camera at \(cameraIP):\(port)")
        
        // 并行执行ICE服务器获取和PeerConnection创建
        async let iceTask: Void = fetchICEServers(from: cameraIP, port: port)
        async let peerConnectionTask: Void = createOptimizedPeerConnection()
        
        // 等待两个任务完成
        try await iceTask
        try await peerConnectionTask
        
        // 建立WebSocket信令连接（减少超时时间）
        try await setupOptimizedSignalingConnection(cameraIP: cameraIP, port: port)
        
        // 开始WebRTC握手
        try await startOptimizedWebRTCHandshake()
    }
    
    /// 断开连接
    func disconnect() {
        print("[OptimizedWebRTCClient] Disconnecting...")
        
        // 关闭数据通道
        localDataChannel?.close()
        remoteDataChannel = nil
        
        // 关闭PeerConnection
        peerConnection?.close()
        peerConnection = nil
        
        // 关闭信令连接
        signalingClient?.disconnect()
        signalingClient = nil
        
        // 重置状态
        connectionState = .new
        iceConnectionState = .new
        isConnected = false
        remoteVideoTrack = nil
        localVideoTrack = nil
    }
    
    /// 发送数据通道消息
    func sendDataChannelMessage(_ message: String) {
        guard let dataChannel = localDataChannel,
              dataChannel.readyState == .open else {
            return
        }
        
        let buffer = RTCDataBuffer(
            data: message.data(using: .utf8)!,
            isBinary: false
        )
        
        dataChannel.sendData(buffer)
    }
    
    // MARK: - Private Optimized Methods
    
    private func setupOptimizedConfiguration() {
        // 使用低延迟配置
        let config = LowLatencyWebRTCConfig.createLowLatencyConfig()
        
        // 设置默认ICE服务器
        let defaultIceServer = RTCIceServer(urlStrings: ["stun:stun.l.google.com:19302"])
        iceServers = [defaultIceServer]
        config.iceServers = iceServers
    }
    
    private func fetchICEServers(from cameraIP: String, port: Int) async throws {
        let url = URL(string: "http://\(cameraIP):\(port)/api/webrtc/ice-servers")!
        
        do {
            // 减少网络超时时间
            var request = URLRequest(url: url)
            request.timeoutInterval = 3.0 // 从默认60秒减少到3秒
            
            let (data, _) = try await URLSession.shared.data(for: request)
            let iceConfig = try JSONDecoder().decode(ICEServerConfig.self, from: data)
            
            var servers: [RTCIceServer] = []
            
            // 添加STUN服务器
            for stunUrl in iceConfig.stunServers {
                let server = RTCIceServer(urlStrings: [stunUrl])
                servers.append(server)
            }
            
            // 添加TURN服务器
            for turnConfig in iceConfig.turnServers {
                let server = RTCIceServer(
                    urlStrings: [turnConfig.url],
                    username: turnConfig.username,
                    credential: turnConfig.credential
                )
                servers.append(server)
            }
            
            self.iceServers = servers
            
            print("[OptimizedWebRTCClient] Loaded \(servers.count) ICE servers")
        } catch {
            print("[OptimizedWebRTCClient] Failed to fetch ICE servers: \(error)")
            // 使用默认配置继续
        }
    }
    
    private func createOptimizedPeerConnection() async throws {
        guard peerConnection == nil else {
            throw WebRTCError.connectionAlreadyExists
        }
        
        // 使用低延迟配置和约束
        let config = LowLatencyWebRTCConfig.createLowLatencyConfig()
        config.iceServers = iceServers
        
        let constraints = LowLatencyWebRTCConfig.createLowLatencyConstraints()
        
        peerConnection = peerConnectionFactory.peerConnection(
            with: config,
            constraints: constraints,
            delegate: self
        )
        
        guard peerConnection != nil else {
            throw WebRTCError.failedToCreatePeerConnection
        }
        
        // 创建数据通道
        createOptimizedDataChannel()
        
        print("[OptimizedWebRTCClient] Optimized PeerConnection created successfully")
    }
    
    private func createOptimizedDataChannel() {
        let dataChannelConfig = RTCDataChannelConfiguration()
        dataChannelConfig.channelId = 0
        dataChannelConfig.isOrdered = false // 减少延迟，允许无序传输
        dataChannelConfig.maxRetransmits = 0 // 不重传，减少延迟
        
        localDataChannel = peerConnection?.dataChannel(
            forLabel: "control",
            configuration: dataChannelConfig
        )
        
        localDataChannel?.delegate = self
    }
    
    private func setupOptimizedSignalingConnection(cameraIP: String, port: Int) async throws {
        signalingClient = WebSocketManager(baseURL: "ws://\(cameraIP):\(port)")

        // 订阅 WebSocket 消息
        signalingClient?.messagePublisher
            .sink { [weak self] message in
                Task {
                    await self?.handleWebSocketMessage(message)
                }
            }
            .store(in: &cancellables)

        // 订阅 WebSocket 错误
        signalingClient?.errorPublisher
            .sink { error in
                print("[OptimizedWebRTCClient] WebSocket error: \(error)")
            }
            .store(in: &cancellables)

        // 连接 WebSocket
        signalingClient?.connect()

        // 等待连接建立（减少超时时间）
        try await waitForOptimizedWebSocketConnection()

        print("[OptimizedWebRTCClient] Optimized signaling connection established")
    }
    
    private func startOptimizedWebRTCHandshake() async throws {
        guard let peerConnection = peerConnection else {
            throw WebRTCError.noPeerConnection
        }
        
        // 使用低延迟约束创建Offer
        let constraints = LowLatencyWebRTCConfig.createLowLatencyConstraints()
        let offer = try await peerConnection.offer(for: constraints)
        try await peerConnection.setLocalDescription(offer)
        
        // 通过信令发送Offer
        let offerMessage = SignalingMessage(
            type: .offer,
            sdp: offer.sdp,
            candidate: nil
        )
        
        try await sendSignalingMessage(offerMessage)
        
        print("[OptimizedWebRTCClient] Optimized WebRTC handshake started")
    }
    
    private func sendSignalingMessage(_ message: SignalingMessage) async throws {
        guard let signalingClient = signalingClient else {
            throw WebRTCError.noSignalingConnection
        }

        let data = try JSONEncoder().encode(message)

        let wsMessage = WebSocketMessage(
            type: .statusUpdate,
            data: data,
            timestamp: Date()
        )

        signalingClient.send(wsMessage)
    }
    
    private func handleSignalingMessage(_ message: SignalingMessage) async {
        switch message.type {
        case .offer:
            await handleOffer(message)
        case .answer:
            await handleAnswer(message)
        case .iceCandidate:
            await handleICECandidate(message)
        }
    }
    
    private func handleOffer(_ message: SignalingMessage) async {
        guard let peerConnection = peerConnection,
              let sdp = message.sdp else { return }
        
        let sessionDescription = RTCSessionDescription(type: .offer, sdp: sdp)
        
        do {
            try await peerConnection.setRemoteDescription(sessionDescription)
            
            // 使用低延迟约束创建Answer
            let constraints = LowLatencyWebRTCConfig.createLowLatencyConstraints()
            let answer = try await peerConnection.answer(for: constraints)
            try await peerConnection.setLocalDescription(answer)
            
            // 发送Answer
            let answerMessage = SignalingMessage(
                type: .answer,
                sdp: answer.sdp,
                candidate: nil
            )
            
            try await sendSignalingMessage(answerMessage)
            
        } catch {
            print("[OptimizedWebRTCClient] Failed to handle offer: \(error)")
        }
    }
    
    private func handleAnswer(_ message: SignalingMessage) async {
        guard let peerConnection = peerConnection,
              let sdp = message.sdp else { return }
        
        let sessionDescription = RTCSessionDescription(type: .answer, sdp: sdp)
        
        do {
            try await peerConnection.setRemoteDescription(sessionDescription)
        } catch {
            print("[OptimizedWebRTCClient] Failed to handle answer: \(error)")
        }
    }
    
    private func handleICECandidate(_ message: SignalingMessage) async {
        guard let peerConnection = peerConnection,
              let candidate = message.candidate else { return }
        
        let iceCandidate = RTCIceCandidate(
            sdp: candidate.candidate,
            sdpMLineIndex: candidate.sdpMLineIndex,
            sdpMid: candidate.sdpMid
        )
        
        do {
            try await peerConnection.add(iceCandidate)
        } catch {
            print("[OptimizedWebRTCClient] Failed to add ICE candidate: \(error)")
        }
    }
    
    private func waitForOptimizedWebSocketConnection() async throws {
        guard let signalingClient = signalingClient else {
            throw WebRTCError.noSignalingConnection
        }

        // 减少超时时间从10秒到5秒
        let timeout: TimeInterval = 5.0
        let startTime = Date()

        while !signalingClient.isConnected {
            if Date().timeIntervalSince(startTime) > timeout {
                throw WebRTCError.noSignalingConnection
            }

            try await Task.sleep(nanoseconds: 50_000_000) // 减少到50ms检查间隔
        }
    }
}

// MARK: - RTCPeerConnectionDelegate
extension OptimizedWebRTCClient: RTCPeerConnectionDelegate {
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange stateChanged: RTCSignalingState) {
        // 最小化日志输出以减少开销
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didAdd stream: RTCMediaStream) {
        if let videoTrack = stream.videoTracks.first {
            DispatchQueue.main.async {
                self.remoteVideoTrack = videoTrack
            }
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didRemove stream: RTCMediaStream) {
        DispatchQueue.main.async {
            self.remoteVideoTrack = nil
        }
    }
    
    nonisolated func peerConnectionShouldNegotiate(_ peerConnection: RTCPeerConnection) {
        // 最小化处理
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceConnectionState) {
        DispatchQueue.main.async {
            self.iceConnectionState = newState
            self.isConnected = (newState == .connected || newState == .completed)
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceGatheringState) {
        // 最小化处理
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didGenerate candidate: RTCIceCandidate) {
        let candidateMessage = SignalingMessage(
            type: .iceCandidate,
            sdp: nil,
            candidate: ICECandidateData(
                candidate: candidate.sdp,
                sdpMLineIndex: candidate.sdpMLineIndex,
                sdpMid: candidate.sdpMid
            )
        )
        
        Task {
            do {
                try await sendSignalingMessage(candidateMessage)
            } catch {
                // 最小化错误处理开销
            }
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didRemove candidates: [RTCIceCandidate]) {
        // 最小化处理
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didOpen dataChannel: RTCDataChannel) {
        DispatchQueue.main.async {
            self.remoteDataChannel = dataChannel
            dataChannel.delegate = self
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCPeerConnectionState) {
        DispatchQueue.main.async {
            self.connectionState = newState
        }
    }
}

// MARK: - RTCDataChannelDelegate
extension OptimizedWebRTCClient: RTCDataChannelDelegate {
    
    nonisolated func dataChannelDidChangeState(_ dataChannel: RTCDataChannel) {
        // 最小化处理
    }

    nonisolated func dataChannel(_ dataChannel: RTCDataChannel, didReceiveMessageWith buffer: RTCDataBuffer) {
        // 最小化处理，仅在需要时处理消息
    }
}

// MARK: - WebSocket Message Handling
extension OptimizedWebRTCClient {

    private func handleWebSocketMessage(_ message: WebSocketMessage) async {
        guard let data = message.data else { return }

        do {
            let signalingMessage = try JSONDecoder().decode(SignalingMessage.self, from: data)
            await handleSignalingMessage(signalingMessage)
        } catch {
            // 最小化错误处理开销
        }
    }
}
//
//  HardwareAcceleratedDecoderFactory.swift
//  wbCamer
//
//  硬件加速解码器工厂
//

import Foundation
import WebRTC
import VideoToolbox

class HardwareAcceleratedDecoderFactory: NSObject, RTCVideoDecoderFactory {
    
    private let defaultFactory = RTCDefaultVideoDecoderFactory()
    
    func supportedCodecs() -> [RTCVideoCodecInfo] {
        var codecs = defaultFactory.supportedCodecs()
        
        // 优先H264硬件解码
        codecs.sort { codec1, codec2 in
            if codec1.name.lowercased() == "h264" && codec2.name.lowercased() != "h264" {
                return true
            }
            return false
        }
        
        return codecs
    }
    
    func createDecoder(_ info: RTCVideoCodecInfo) -> RTCVideoDecoder? {
        // 优先使用硬件解码器
        if info.name.lowercased() == "h264" {
            // 检查是否支持硬件解码
            if VTIsHardwareDecodeSupported(kCMVideoCodecType_H264) {
                print("[HardwareDecoder] Using hardware H264 decoder")
                return createHardwareH264Decoder()
            }
        }
        
        return defaultFactory.createDecoder(info)
    }
    
    private func createHardwareH264Decoder() -> RTCVideoDecoder? {
        // 使用默认工厂创建解码器，WebRTC会自动选择最佳实现
        return defaultFactory.createDecoder(RTCVideoCodecInfo(name: "H264"))
    }
}

class HardwareAcceleratedEncoderFactory: NSObject, RTCVideoEncoderFactory {
    
    private let defaultFactory = RTCDefaultVideoEncoderFactory()
    
    func supportedCodecs() -> [RTCVideoCodecInfo] {
        return defaultFactory.supportedCodecs()
    }
    
    func createEncoder(_ info: RTCVideoCodecInfo) -> RTCVideoEncoder? {
        if info.name.lowercased() == "h264" {
            if VTIsHardwareDecodeSupported(kCMVideoCodecType_H264) {
                print("[HardwareEncoder] Using hardware H264 encoder")
                // 使用默认工厂，WebRTC会自动选择硬件编码器
                return defaultFactory.createEncoder(info)
            }
        }
        
        return defaultFactory.createEncoder(info)
    }
}
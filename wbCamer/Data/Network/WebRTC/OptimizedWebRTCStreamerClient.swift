//
//  OptimizedWebRTCStreamerClient.swift
//  wbCamer
//
//  优化的WebRTC Streamer客户端 - 减少延迟版本
//

import Foundation
import WebRTC
import Combine

/// 优化的P2-R1 WebRTC Streamer 客户端
/// 集成硬件加速解码器和低延迟配置
@MainActor
class OptimizedWebRTCStreamerClient: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var connectionState: RTCPeerConnectionState = .new
    @Published var iceConnectionState: RTCIceConnectionState = .new
    @Published var isConnected: Bool = false
    @Published var remoteVideoTrack: RTCVideoTrack?
    
    // MARK: - Private Properties
    private var peerConnection: RTCPeerConnection?
    private var peerConnectionFactory: RTCPeerConnectionFactory
    private var config: RTCConfiguration
    private var constraints: RTCMediaConstraints
    private var currentCameraIP: String?
    
    // 性能监控
    private var connectionStartTime: Date?
    private var firstFrameTime: Date?
    
    override init() {
        // 使用硬件加速的编解码器工厂
        let decoderFactory = HardwareAcceleratedDecoderFactory()
        let encoderFactory = HardwareAcceleratedEncoderFactory()
        
        peerConnectionFactory = RTCPeerConnectionFactory(
            encoderFactory: encoderFactory,
            decoderFactory: decoderFactory
        )
        
        // 使用低延迟配置
        config = LowLatencyWebRTCConfig.createLowLatencyConfig()
        constraints = LowLatencyWebRTCConfig.createLowLatencyConstraints()
        
        super.init()
        
        RTCInitializeSSL()
    }
    
    deinit {
        // 在 deinit 中同步清理，避免异步操作
        if let pc = peerConnection {
            pc.close()
        }
        
        peerConnection = nil
        currentCameraIP = nil
        // 注意：主线程隔离的属性不能在 deinit 中直接修改
        // remoteVideoTrack, isConnected, connectionState, iceConnectionState 将由系统自动清理
        
        RTCCleanupSSL()
    }
    
    /// 优化的连接方法
    func connect(to cameraIP: String, port: Int = 8000) async throws {
        connectionStartTime = Date()
        
        // 检查是否已连接到相同地址
        if isConnected && currentCameraIP == cameraIP {
            return
        }
        
        // 快速断开现有连接（减少等待时间）
        if peerConnection != nil {
            disconnect()
            try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒而不是0.5秒
        }
        
        currentCameraIP = cameraIP
        
        do {
            // 并行执行ICE配置和PeerConnection创建
            async let iceTask: () = setupICEServersOptimized(cameraIP: cameraIP, port: port)
            async let peerTask: () = createPeerConnectionOptimized()
            
            _ = try await (iceTask, peerTask)
            
            // 请求RTSP流
            try await requestRTSPStreamOptimized(cameraIP: cameraIP, port: port)
            
        } catch {
            disconnect()
            throw error
        }
    }
    
    /// 优化的ICE服务器设置
    private func setupICEServersOptimized(cameraIP: String, port: Int) async {
        let possiblePorts = [8000, 8443, port]
        
        // 并行尝试多个端口
        await withTaskGroup(of: [RTCIceServer]?.self) { group in
            for testPort in possiblePorts {
                group.addTask {
                    await self.fetchICEServers(cameraIP: cameraIP, port: testPort)
                }
            }
            
            // 使用第一个成功的结果
            for await servers in group {
                if let servers = servers, !servers.isEmpty {
                    self.config.iceServers = servers
                    break
                }
            }
        }
    }
    
    private func fetchICEServers(cameraIP: String, port: Int) async -> [RTCIceServer]? {
        let url = URL(string: "http://\(cameraIP):\(port)/api/getIceServers")
        guard let url = url else { return nil }
        
        do {
            let request = URLRequest(url: url, timeoutInterval: 2.0) // 减少超时时间
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                return nil
            }
            
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let iceServers = json["iceServers"] as? [[String: Any]] {
                return parseICEServers(iceServers)
            }
        } catch {
            // 静默失败，使用默认配置
        }
        
        return nil
    }
    
    private func parseICEServers(_ iceServers: [[String: Any]]) -> [RTCIceServer] {
        var rtcIceServers: [RTCIceServer] = []
        
        for serverConfig in iceServers {
            if let urls = serverConfig["urls"] as? [String] {
                let iceServer = RTCIceServer(urlStrings: urls)
                rtcIceServers.append(iceServer)
            } else if let url = serverConfig["url"] as? String {
                let iceServer = RTCIceServer(urlStrings: [url])
                rtcIceServers.append(iceServer)
            }
        }
        
        return rtcIceServers
    }
    
    private func createPeerConnectionOptimized() throws {
        guard peerConnection == nil else { return }
        
        peerConnection = peerConnectionFactory.peerConnection(
            with: config,
            constraints: RTCMediaConstraints(mandatoryConstraints: nil, optionalConstraints: nil),
            delegate: self
        )
        
        guard peerConnection != nil else {
            throw WebRTCStreamerError.failedToCreatePeerConnection
        }
    }
    
    private func requestRTSPStreamOptimized(cameraIP: String, port: Int) async throws {
        guard let peerConnection = peerConnection else {
            throw WebRTCStreamerError.failedToCreatePeerConnection
        }
        
        // 创建offer
        let offer = try await peerConnection.offer(for: constraints)
        try await peerConnection.setLocalDescription(offer)
        
        // 尝试WebRTC Streamer API
        let success = await tryWebRTCStreamerAPIOptimized(cameraIP: cameraIP, port: port, offer: offer)
        
        if !success {
            throw WebRTCStreamerError.connectionTimeout
        }
    }
    
    private func tryWebRTCStreamerAPIOptimized(cameraIP: String, port: Int, offer: RTCSessionDescription) async -> Bool {
        let peerID = UUID().uuidString.lowercased()
        let rtspURL = "rtsp://127.0.0.1/live_h264"
        let options = "timeout=1"
        
        let possiblePorts = [8000, 8443, port]
        
        for testPort in possiblePorts {
            let callURL = "http://\(cameraIP):\(testPort)/api/call?peerid=\(peerID)&url=\(rtspURL.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")&options=\(options.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")"
            
            guard let url = URL(string: callURL) else { continue }
            
            do {
                var request = URLRequest(url: url)
                request.httpMethod = "POST"
                request.setValue("application/json", forHTTPHeaderField: "Content-Type")
                request.timeoutInterval = 3.0 // 减少超时时间
                
                request.httpBody = try JSONSerialization.data(withJSONObject: [
                    "type": offer.type.rawValue,
                    "sdp": offer.sdp
                ])
                
                let (data, response) = try await URLSession.shared.data(for: request)
                
                if let httpResponse = response as? HTTPURLResponse,
                   httpResponse.statusCode == 200 {
                    
                    if let responseDict = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let type = responseDict["type"] as? String,
                       let sdp = responseDict["sdp"] as? String,
                       type == "answer" {
                        
                        let answer = RTCSessionDescription(type: .answer, sdp: sdp)
                        try await peerConnection?.setRemoteDescription(answer)
                        
                        // 异步获取ICE candidates，不阻塞连接
                        Task {
                            await getICECandidatesOptimized(cameraIP: cameraIP, port: testPort, peerID: peerID)
                        }
                        
                        return true
                    }
                }
            } catch {
                continue
            }
        }
        
        return false
    }
    
    private func getICECandidatesOptimized(cameraIP: String, port: Int, peerID: String) async {
        let iceCandidatesURL = "http://\(cameraIP):\(port)/api/getIceCandidate?peerid=\(peerID)"
        guard let url = URL(string: iceCandidatesURL) else { return }
        
        do {
            let request = URLRequest(url: url, timeoutInterval: 2.0)
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200,
               let candidatesArray = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
                
                for candidateDict in candidatesArray {
                    if let candidate = candidateDict["candidate"] as? String,
                       let sdpMLineIndex = candidateDict["sdpMLineIndex"] as? Int32,
                       let sdpMid = candidateDict["sdpMid"] as? String {
                        
                        let iceCandidate = RTCIceCandidate(
                            sdp: candidate,
                            sdpMLineIndex: sdpMLineIndex,
                            sdpMid: sdpMid
                        )
                        
                        try await peerConnection?.add(iceCandidate)
                    }
                }
            }
        } catch {
            // 静默处理错误
        }
    }
    
    @MainActor
    func disconnect() {
        if let pc = peerConnection {
            pc.close()
        }
        
        peerConnection = nil
        currentCameraIP = nil
        remoteVideoTrack = nil
        isConnected = false
        connectionState = .closed
        iceConnectionState = .closed
    }
    
    // MARK: - 性能监控
    
    func getConnectionLatency() -> TimeInterval? {
        guard let start = connectionStartTime,
              let end = firstFrameTime else { return nil }
        return end.timeIntervalSince(start)
    }
}

// MARK: - RTCPeerConnectionDelegate

extension OptimizedWebRTCStreamerClient: RTCPeerConnectionDelegate {
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didAdd stream: RTCMediaStream) {
        if let videoTrack = stream.videoTracks.first {
            Task { @MainActor in
                self.remoteVideoTrack = videoTrack
                if self.firstFrameTime == nil {
                    self.firstFrameTime = Date()
                }
            }
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceConnectionState) {
        Task { @MainActor in
            self.iceConnectionState = newState
            
            switch newState {
            case .connected, .completed:
                self.isConnected = true
            case .failed, .closed:
                self.isConnected = false
            default:
                break
            }
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCPeerConnectionState) {
        Task { @MainActor in
            self.connectionState = newState
        }
    }
    
    // 其他必需的delegate方法的最小化实现
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange stateChanged: RTCSignalingState) {}
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didRemove stream: RTCMediaStream) {
        Task { @MainActor in
            self.remoteVideoTrack = nil
        }
    }
    nonisolated func peerConnectionShouldNegotiate(_ peerConnection: RTCPeerConnection) {}
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceGatheringState) {}
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didGenerate candidate: RTCIceCandidate) {}
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didRemove candidates: [RTCIceCandidate]) {}
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didOpen dataChannel: RTCDataChannel) {}
}
//
//  LowLatencyWebRTCConfig.swift
//  wbCamer
//
//  低延迟WebRTC配置类
//

import Foundation
import WebRTC

class LowLatencyWebRTCConfig {
    
    /// 创建低延迟的RTCConfiguration
    static func createLowLatencyConfig() -> RTCConfiguration {
        let config = RTCConfiguration()
        
        // 优化的ICE配置
        config.iceTransportPolicy = .all
        config.bundlePolicy = .maxBundle  // 最大化bundle以减少延迟
        config.rtcpMuxPolicy = .require
        config.continualGatheringPolicy = .gatherContinually
        config.keyType = .ECDSA  // 使用更快的密钥类型
        
        // 设置默认的低延迟ICE服务器
        let stunServer = RTCIceServer(urlStrings: [
            "stun:stun.l.google.com:19302",
            "stun:stun1.l.google.com:19302"
        ])
        config.iceServers = [stunServer]
        
        return config
    }
    
    /// 创建低延迟的媒体约束
    static func createLowLatencyConstraints() -> RTCMediaConstraints {
        return RTCMediaConstraints(
            mandatoryConstraints: [
                "OfferToReceiveAudio": "true",
                "OfferToReceiveVideo": "true",
                // 禁用可能增加延迟的功能
                "googCpuOveruseDetection": "false",
                "googHighpassFilter": "false",
                "googEchoCancellation": "false",
                "googNoiseSuppression": "false",
                "googTypingNoiseDetection": "false"
            ],
            optionalConstraints: [
                "googImprovedWifiBwe": "true",  // 改进的WiFi带宽估计
                "googScreencastMinBitrate": "1000",  // 最小码率
                "googCombinedAudioVideoBwe": "true"  // 组合音视频带宽估计
            ]
        )
    }
}
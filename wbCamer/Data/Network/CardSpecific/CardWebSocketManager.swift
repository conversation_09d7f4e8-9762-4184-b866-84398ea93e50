//
//  CardWebSocketManager.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import Network
import Combine

// MARK: - Card WebSocket State

enum CardWebSocketState: Equatable {
    case disconnected
    case connecting
    case connected
    case failed(Error)

    static func == (lhs: CardWebSocketState, rhs: CardWebSocketState) -> Bool {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected),
             (.connecting, .connecting),
             (.connected, .connected):
            return true
        case (.failed(let lhsError), .failed(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

// MARK: - Card WebSocket Manager

/// 卡片专用WebSocket管理器 - 每个摄像机卡片拥有独立的WebSocket连接
class CardWebSocketManager: NSObject, ObservableObject {
    let cardId: UUID
    let ipAddress: String
    
    @Published var connectionState: CardWebSocketState = .disconnected
    @Published var isConnected: Bool = false
    @Published var lastMessage: String?
    
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private let messageQueue = DispatchQueue(label: "card.websocket.message.queue", qos: .userInitiated)
    private let heartbeatQueue = DispatchQueue(label: "card.websocket.heartbeat.queue")
    
    // WebSocket配置
    private let wsPort: UInt16 = 81  // P2-R1 WebSocket端口
    private let heartbeatInterval: TimeInterval = 30.0
    private var heartbeatTimer: Timer?
    
    // 重连机制
    private var reconnectAttempts = 0
    private let maxReconnectAttempts = 5
    private var reconnectTimer: Timer?
    
    // MARK: - Initialization
    
    init(cardId: UUID, ipAddress: String) {
        self.cardId = cardId
        self.ipAddress = ipAddress
        super.init()
        
        setupURLSession()
        print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) initialized for \(ipAddress):\(wsPort)")
    }
    
    deinit {
        // 同步清理资源
        webSocket?.disconnect()
        webSocket = nil
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
    }
    
    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 10.0
        config.timeoutIntervalForResource = 30.0
        
        urlSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }
    
    // MARK: - Connection Management
    
    @MainActor
    func connect() async -> Bool {
        guard connectionState != .connected && connectionState != .connecting else {
            print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) already connected or connecting")
            return isConnected
        }
        
        let wsURL = "ws://\(ipAddress):\(wsPort)"
        
        guard let url = URL(string: wsURL) else {
            print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) invalid WebSocket URL: \(wsURL)")
            updateConnectionState(.failed(WebSocketError.invalidURL))
            return false
        }
        
        print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) connecting to \(wsURL)")
        updateConnectionState(.connecting)
        
        var request = URLRequest(url: url)
        request.setValue("13", forHTTPHeaderField: "Sec-WebSocket-Version")
        request.setValue("websocket", forHTTPHeaderField: "Upgrade")
        request.setValue("Upgrade", forHTTPHeaderField: "Connection")
        request.setValue(generateWebSocketKey(), forHTTPHeaderField: "Sec-WebSocket-Key")
        
        webSocketTask = urlSession?.webSocketTask(with: request)
        webSocketTask?.resume()
        
        // 开始接收消息
        receiveMessage()
        
        // 等待连接建立（最多10秒）
        for _ in 0..<100 {
            if isConnected {
                return true
            }
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        }
        
        return isConnected
    }
    
    @MainActor
    func disconnect() async {
        print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) disconnecting")
        
        stopHeartbeat()
        stopReconnectTimer()
        
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        
        updateConnectionState(.disconnected)
        
        print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) disconnected")
    }
    
    private func updateConnectionState(_ state: CardWebSocketState) {
        DispatchQueue.main.async {
            self.connectionState = state
            self.isConnected = {
                if case .connected = state {
                    return true
                }
                return false
            }()
        }
    }
    
    private func generateWebSocketKey() -> String {
        let keyData = Data((0..<16).map { _ in UInt8.random(in: 0...255) })
        return keyData.base64EncodedString()
    }
    
    // MARK: - Message Handling
    
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                self?.handleReceivedMessage(message)
                self?.receiveMessage() // 继续接收下一条消息
            case .failure(let error):
                self?.handleReceiveError(error)
            }
        }
    }
    
    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message) {
        switch message {
        case .string(let text):
            DispatchQueue.main.async {
                self.lastMessage = text
                // 减少日志输出以避免控制台刷屏
                if !text.contains("heartbeat") && !text.contains("ping") {
                    print("[CardWebSocketManager] \(self.cardId.uuidString.prefix(8)) received: \(text.prefix(100))")
                }
            }
        case .data(let data):
            if let text = String(data: data, encoding: .utf8) {
                DispatchQueue.main.async {
                    self.lastMessage = text
                }
            }
        @unknown default:
            break
        }
    }
    
    private func handleReceiveError(_ error: Error) {
        print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) receive error: \(error)")
        
        DispatchQueue.main.async {
            self.updateConnectionState(.failed(error))
            self.attemptReconnect()
        }
    }
    
    // MARK: - Message Sending

    func sendMessage(_ message: String) {
        guard isConnected else {
            print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) not connected, message not sent")
            return
        }

        let wsMessage = URLSessionWebSocketTask.Message.string(message)

        webSocketTask?.send(wsMessage) { [weak self] error in
            if let error = error {
                print("[CardWebSocketManager] \(self?.cardId.uuidString.prefix(8) ?? "unknown") send error: \(error)")
            }
        }
    }
    
    func sendData(_ data: Data) {
        guard isConnected else {
            print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) not connected, data not sent")
            return
        }
        
        let wsMessage = URLSessionWebSocketTask.Message.data(data)
        
        webSocketTask?.send(wsMessage) { [weak self] error in
            if let error = error {
                print("[CardWebSocketManager] \(self?.cardId.uuidString.prefix(8) ?? "unknown") send error: \(error)")
            }
        }
    }
    
    // MARK: - Heartbeat
    
    private func startHeartbeat() {
        stopHeartbeat()
        
        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: heartbeatInterval, repeats: true) { [weak self] _ in
            self?.sendHeartbeat()
        }
    }
    
    private func stopHeartbeat() {
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
    }
    
    private func sendHeartbeat() {
        let heartbeatMessage = """
        {
            "type": "heartbeat",
            "timestamp": \(Date().timeIntervalSince1970),
            "cardId": "\(cardId.uuidString)"
        }
        """
        sendMessage(heartbeatMessage)
    }
    
    // MARK: - Reconnection
    
    private func attemptReconnect() {
        guard reconnectAttempts < maxReconnectAttempts else {
            print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) max reconnect attempts reached")
            return
        }
        
        reconnectAttempts += 1
        let delay = min(pow(2.0, Double(reconnectAttempts)), 30.0) // 指数退避，最大30秒
        
        print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) attempting reconnect in \(delay) seconds (attempt \(reconnectAttempts))")
        
        reconnectTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] _ in
            Task { [weak self] in
                await self?.connect()
            }
        }
    }
    
    private func stopReconnectTimer() {
        reconnectTimer?.invalidate()
        reconnectTimer = nil
        reconnectAttempts = 0
    }
}

// MARK: - URLSessionWebSocketDelegate

extension CardWebSocketManager: URLSessionWebSocketDelegate {
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) WebSocket connected")
        
        DispatchQueue.main.async {
            self.updateConnectionState(.connected)
            self.stopReconnectTimer()
            self.startHeartbeat()
        }
    }
    
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        let reasonString = reason.flatMap { String(data: $0, encoding: .utf8) } ?? "Unknown"
        print("[CardWebSocketManager] \(cardId.uuidString.prefix(8)) WebSocket closed with code: \(closeCode.rawValue), reason: \(reasonString)")
        
        DispatchQueue.main.async {
            self.updateConnectionState(.disconnected)
            self.stopHeartbeat()
            
            // 如果不是主动断开，尝试重连
            if closeCode != .goingAway {
                self.attemptReconnect()
            }
        }
    }
}

// MARK: - WebSocket Error

enum CardWebSocketError: Error, LocalizedError {
    case invalidURL
    case connectionFailed
    case sendFailed
    case receiveFailed

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid WebSocket URL"
        case .connectionFailed:
            return "WebSocket connection failed"
        case .sendFailed:
            return "Failed to send WebSocket message"
        case .receiveFailed:
            return "Failed to receive WebSocket message"
        }
    }
}

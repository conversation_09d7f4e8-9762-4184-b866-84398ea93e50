//
//  CardPTZManager.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import Combine

// MARK: - Card PTZ Manager

/// 卡片专用PTZ管理器 - 每个摄像机卡片拥有独立的PTZ控制
class CardPTZManager: ObservableObject {
    let cardId: UUID
    private let apiClient: CardAPIClient
    
    @Published var currentPTZState: PTZState = .idle
    @Published var lastPTZCommand: Date?
    
    // PTZ去重机制
    private var lastPTZMoveRequest: PTZMoveRequest?
    private var lastPTZDirectionRequest: PTZDirectionRequest?
    private let ptzDeduplicationInterval: TimeInterval = 0.1
    
    // PTZ定时器
    private var ptzTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(cardId: UUID, apiClient: CardAPIClient) {
        self.cardId = cardId
        self.apiClient = apiClient
        
        print("[CardPTZManager] \(cardId.uuidString.prefix(8)) initialized")
    }
    
    deinit {
        ptzTimer?.invalidate()
    }
    
    // MARK: - PTZ Control Methods
    
    /// 同时控制pan和tilt (ptMove) - 主要的摇杆控制方法
    func executePTZMove(panSpeed: Float, tiltSpeed: Float) {
        // 停止之前的PTZ定时器
        ptzTimer?.invalidate()
        
        // 如果速度都为0，发送停止命令
        if abs(panSpeed) < 0.01 && abs(tiltSpeed) < 0.01 {
            sendPTZStopCommand()
            currentPTZState = .idle
            lastPTZMoveRequest = nil
            return
        }
        
        // 去重检查
        let currentRequest = PTZMoveRequest(panSpeed: panSpeed, tiltSpeed: tiltSpeed, timestamp: Date())
        if shouldSkipPTZMoveRequest(currentRequest) {
            return
        }
        
        // 记录请求并发送命令
        lastPTZMoveRequest = currentRequest
        sendPTZMoveCommand(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
        
        // 更新状态
        let direction = determinePTZDirection(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
        currentPTZState = .moving(direction: direction)
        lastPTZCommand = Date()
        
        // 设置定时器，如果一段时间没有新命令则自动停止
        ptzTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { [weak self] _ in
            self?.sendPTZStopCommand()
            self?.currentPTZState = .idle
        }
    }
    
    /// 单方向PTZ控制 (directionMove)
    func executePTZAction(_ action: PTZAction, fspeed: Float = 0.5) {
        // 停止之前的PTZ定时器
        ptzTimer?.invalidate()
        
        // 对于停止操作，允许重复发送
        if action == .stop {
            sendPTZStopCommand()
            currentPTZState = .idle
            lastPTZDirectionRequest = nil
            return
        }
        
        // 去重检查
        let currentRequest = PTZDirectionRequest(action: action, fspeed: fspeed, timestamp: Date())
        if shouldSkipPTZDirectionRequest(currentRequest) {
            return
        }
        
        // 记录请求并发送命令
        lastPTZDirectionRequest = currentRequest
        sendPTZDirectionCommand(action, fspeed: fspeed)
        
        // 更新状态
        let direction = action.toPTZDirection()
        currentPTZState = .moving(direction: direction)
        lastPTZCommand = Date()
        
        // 设置定时器，如果一段时间没有新命令则自动停止
        ptzTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { [weak self] _ in
            self?.sendPTZStopCommand()
            self?.currentPTZState = .idle
        }
    }
    
    /// 变焦控制
    func executeZoomAction(_ direction: ZoomDirection, speed: Float = 0.5) {
        Task {
            do {
                switch direction {
                case .zoomIn:
                    try await apiClient.setZoom(level: speed)
                    currentPTZState = .zooming(direction: .zoomIn)
                case .zoomOut:
                    try await apiClient.setZoom(level: -speed)
                    currentPTZState = .zooming(direction: .zoomOut)
                }
                lastPTZCommand = Date()
            } catch {
                print("[CardPTZManager] \(cardId.uuidString.prefix(8)) zoom command failed: \(error)")
                currentPTZState = .error("Zoom failed: \(error.localizedDescription)")
            }
        }
    }
    
    /// 停止所有PTZ运动
    func stopMovement() {
        ptzTimer?.invalidate()
        sendPTZStopCommand()
        currentPTZState = .idle
        lastPTZMoveRequest = nil
        lastPTZDirectionRequest = nil
    }
    
    // MARK: - Private Methods
    
    private func sendPTZMoveCommand(panSpeed: Float, tiltSpeed: Float) {
        Task {
            do {
                try await apiClient.setPTZPosition(pan: panSpeed, tilt: tiltSpeed)
            } catch {
                print("[CardPTZManager] \(cardId.uuidString.prefix(8)) PTZ move command failed: \(error)")
                DispatchQueue.main.async {
                    self.currentPTZState = .error("PTZ move failed: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func sendPTZDirectionCommand(_ action: PTZAction, fspeed: Float) {
        Task {
            do {
                let (panSpeed, tiltSpeed) = action.toSpeedValues(fspeed: fspeed)
                try await apiClient.setPTZPosition(pan: panSpeed, tilt: tiltSpeed)
            } catch {
                print("[CardPTZManager] \(cardId.uuidString.prefix(8)) PTZ direction command failed: \(error)")
                DispatchQueue.main.async {
                    self.currentPTZState = .error("PTZ direction failed: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func sendPTZStopCommand() {
        Task {
            do {
                try await apiClient.setPTZPosition(pan: 0, tilt: 0)
            } catch {
                print("[CardPTZManager] \(cardId.uuidString.prefix(8)) PTZ stop command failed: \(error)")
            }
        }
    }
    
    // MARK: - Deduplication Logic
    
    private func shouldSkipPTZMoveRequest(_ request: PTZMoveRequest) -> Bool {
        guard let lastRequest = lastPTZMoveRequest else { return false }
        
        let timeDiff = request.timestamp.timeIntervalSince(lastRequest.timestamp)
        if timeDiff < ptzDeduplicationInterval {
            let panDiff = abs(request.panSpeed - lastRequest.panSpeed)
            let tiltDiff = abs(request.tiltSpeed - lastRequest.tiltSpeed)
            
            // 如果时间间隔很短且速度变化很小，则跳过
            if panDiff < 0.05 && tiltDiff < 0.05 {
                return true
            }
        }
        
        return false
    }
    
    private func shouldSkipPTZDirectionRequest(_ request: PTZDirectionRequest) -> Bool {
        guard let lastRequest = lastPTZDirectionRequest else { return false }
        
        let timeDiff = request.timestamp.timeIntervalSince(lastRequest.timestamp)
        if timeDiff < ptzDeduplicationInterval {
            let speedDiff = abs(request.fspeed - lastRequest.fspeed)
            
            // 如果时间间隔很短且动作和速度都相同，则跳过
            if request.action == lastRequest.action && speedDiff < 0.05 {
                return true
            }
        }
        
        return false
    }
    
    private func determinePTZDirection(panSpeed: Float, tiltSpeed: Float) -> PTZDirection {
        if abs(panSpeed) > abs(tiltSpeed) {
            return panSpeed > 0 ? .right : .left
        } else {
            return tiltSpeed > 0 ? .up : .down
        }
    }
}

// MARK: - Supporting Types

/// PTZ移动请求（用于去重）
private struct PTZMoveRequest {
    let panSpeed: Float
    let tiltSpeed: Float
    let timestamp: Date
}

/// PTZ方向请求（用于去重）
private struct PTZDirectionRequest {
    let action: PTZAction
    let fspeed: Float
    let timestamp: Date
}

/// PTZ动作枚举
enum PTZAction: String, CaseIterable {
    case up = "up"
    case down = "down"
    case left = "left"
    case right = "right"
    case upLeft = "up_left"
    case upRight = "up_right"
    case downLeft = "down_left"
    case downRight = "down_right"
    case stop = "stop"
    
    func toPTZDirection() -> PTZDirection {
        switch self {
        case .up: return .up
        case .down: return .down
        case .left: return .left
        case .right: return .right
        case .upLeft: return .upLeft
        case .upRight: return .upRight
        case .downLeft: return .downLeft
        case .downRight: return .downRight
        case .stop: return .up // 默认值，实际不会使用
        }
    }
    
    func toSpeedValues(fspeed: Float) -> (pan: Float, tilt: Float) {
        switch self {
        case .up: return (0, fspeed)
        case .down: return (0, -fspeed)
        case .left: return (-fspeed, 0)
        case .right: return (fspeed, 0)
        case .upLeft: return (-fspeed * 0.707, fspeed * 0.707)
        case .upRight: return (fspeed * 0.707, fspeed * 0.707)
        case .downLeft: return (-fspeed * 0.707, -fspeed * 0.707)
        case .downRight: return (fspeed * 0.707, -fspeed * 0.707)
        case .stop: return (0, 0)
        }
    }
}

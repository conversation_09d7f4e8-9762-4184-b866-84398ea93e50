//
//  CardWebRTCClient.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import WebRTC
import SwiftUI
import Combine

// MARK: - Card WebRTC State

enum CardWebRTCState: Equatable {
    case disconnected
    case connecting
    case connected
    case failed(Error)

    var isConnected: Bool {
        if case .connected = self {
            return true
        }
        return false
    }

    static func == (lhs: CardWebRTCState, rhs: CardWebRTCState) -> Bool {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected),
             (.connecting, .connecting),
             (.connected, .connected):
            return true
        case (.failed(let lhsError), .failed(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}

// MARK: - Card WebRTC Client

/// 卡片专用WebRTC客户端 - 每个摄像机卡片拥有独立的WebRTC连接
class CardWebRTCClient: NSObject, ObservableObject {
    let cardId: UUID
    let ipAddress: String
    
    @Published var connectionState: CardWebRTCState = .disconnected
    @Published var isConnected: Bool = false
    @Published var videoTrack: RTCVideoTrack?
    @Published var audioTrack: RTCAudioTrack?
    
    // WebRTC组件
    private var peerConnection: RTCPeerConnection?
    private var peerConnectionFactory: RTCPeerConnectionFactory?
    private var videoCapturer: RTCVideoCapturer?
    private var localVideoTrack: RTCVideoTrack?
    private var localAudioTrack: RTCAudioTrack?
    
    // 配置
    private let webRTCPort: UInt16 = 8000  // P2-R1 WebRTC端口
    private var iceServers: [RTCIceServer] = []
    
    // 统计信息
    @Published var connectionStats: WebRTCStats = WebRTCStats()
    private var statsTimer: Timer?
    
    // MARK: - Initialization
    
    init(cardId: UUID, ipAddress: String) {
        self.cardId = cardId
        self.ipAddress = ipAddress
        super.init()
        
        setupWebRTC()
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) initialized for \(ipAddress):\(webRTCPort)")
    }
    
    deinit {
        // 同步清理资源
        stopStatsMonitoring()
        peerConnection?.close()
        peerConnection = nil
        videoTrack = nil
        audioTrack = nil
        localVideoTrack = nil
        localAudioTrack = nil
    }
    
    private func setupWebRTC() {
        // 初始化WebRTC工厂
        let decoderFactory = RTCDefaultVideoDecoderFactory()
        let encoderFactory = RTCDefaultVideoEncoderFactory()
        
        peerConnectionFactory = RTCPeerConnectionFactory(
            encoderFactory: encoderFactory,
            decoderFactory: decoderFactory
        )
        
        // 配置ICE服务器
        setupICEServers()
    }
    
    private func setupICEServers() {
        // 使用Google的公共STUN服务器
        let stunServer = RTCIceServer(urlStrings: ["stun:stun.l.google.com:19302"])
        iceServers = [stunServer]
    }
    
    // MARK: - Connection Management
    
    @MainActor
    func connect() async -> Bool {
        guard connectionState != .connected && connectionState != .connecting else {
            print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) already connected or connecting")
            return isConnected
        }
        
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) connecting to \(ipAddress):\(webRTCPort)")
        updateConnectionState(.connecting)
        
        do {
            // 创建PeerConnection
            try createPeerConnection()
            
            // 创建Offer
            let offer = try await createOffer()
            
            // 设置本地描述
            try await setLocalDescription(offer)
            
            // 发送Offer到远端（这里需要根据实际的信令服务器实现）
            let success = await sendOfferToRemote(offer)
            
            if success {
                updateConnectionState(.connected)
                startStatsMonitoring()
                print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) connected successfully")
                return true
            } else {
                updateConnectionState(.failed(CardWebRTCError.connectionFailed))
                return false
            }
            
        } catch {
            updateConnectionState(.failed(error))
            print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) connection failed: \(error)")
            return false
        }
    }
    
    @MainActor
    func disconnect() async {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) disconnecting")
        
        stopStatsMonitoring()
        
        peerConnection?.close()
        peerConnection = nil
        
        videoTrack = nil
        audioTrack = nil
        localVideoTrack = nil
        localAudioTrack = nil
        
        updateConnectionState(.disconnected)
        
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) disconnected")
    }
    
    private func updateConnectionState(_ state: CardWebRTCState) {
        DispatchQueue.main.async {
            self.connectionState = state
            self.isConnected = state.isConnected
        }
    }
    
    // MARK: - WebRTC Setup
    
    private func createPeerConnection() throws {
        guard let factory = peerConnectionFactory else {
            throw CardWebRTCError.factoryNotInitialized
        }

        let config = RTCConfiguration()
        config.iceServers = iceServers
        config.sdpSemantics = .unifiedPlan
        config.continualGatheringPolicy = .gatherContinually

        let constraints = RTCMediaConstraints(
            mandatoryConstraints: nil,
            optionalConstraints: ["DtlsSrtpKeyAgreement": "true"]
        )

        peerConnection = factory.peerConnection(with: config, constraints: constraints, delegate: self)

        guard peerConnection != nil else {
            throw CardWebRTCError.peerConnectionCreationFailed
        }
    }
    
    private func createOffer() async throws -> RTCSessionDescription {
        guard let peerConnection = peerConnection else {
            throw CardWebRTCError.peerConnectionNotFound
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            let constraints = RTCMediaConstraints(
                mandatoryConstraints: [
                    "OfferToReceiveAudio": "true",
                    "OfferToReceiveVideo": "true"
                ],
                optionalConstraints: nil
            )
            
            peerConnection.offer(for: constraints) { offer, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let offer = offer {
                    continuation.resume(returning: offer)
                } else {
                    continuation.resume(throwing: CardWebRTCError.offerCreationFailed)
                }
            }
        }
    }
    
    private func setLocalDescription(_ description: RTCSessionDescription) async throws {
        guard let peerConnection = peerConnection else {
            throw CardWebRTCError.peerConnectionNotFound
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            peerConnection.setLocalDescription(description) { error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume()
                }
            }
        }
    }
    
    private func sendOfferToRemote(_ offer: RTCSessionDescription) async -> Bool {
        // 这里应该实现与P2-R1设备的信令交换
        // 由于P2-R1可能使用HTTP API或WebSocket进行信令交换
        // 这里简化处理，实际实现需要根据设备的具体协议
        
        do {
            let offerData = [
                "type": "offer",
                "sdp": offer.sdp
            ]
            
            let jsonData = try JSONSerialization.data(withJSONObject: offerData)
            
            // 发送到设备的WebRTC端点
            let url = URL(string: "http://\(ipAddress):\(webRTCPort)/offer")!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                return false
            }
            
            // 处理远端的Answer
            if let answerData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let answerSdp = answerData["sdp"] as? String {
                let answer = RTCSessionDescription(type: .answer, sdp: answerSdp)
                try await setRemoteDescription(answer)
                return true
            }
            
            return false
            
        } catch {
            print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) failed to send offer: \(error)")
            return false
        }
    }
    
    private func setRemoteDescription(_ description: RTCSessionDescription) async throws {
        guard let peerConnection = peerConnection else {
            throw CardWebRTCError.peerConnectionNotFound
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            peerConnection.setRemoteDescription(description) { error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - Video View
    
    func getVideoView() -> AnyView? {
        guard let videoTrack = videoTrack else {
            return nil
        }
        
        return AnyView(CardVideoView(videoTrack: videoTrack, cardId: cardId))
    }
    
    // MARK: - Statistics
    
    private func startStatsMonitoring() {
        stopStatsMonitoring()
        
        statsTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            self?.updateConnectionStats()
        }
    }
    
    private func stopStatsMonitoring() {
        statsTimer?.invalidate()
        statsTimer = nil
    }
    
    private func updateConnectionStats() {
        guard let peerConnection = peerConnection else { return }
        
        peerConnection.statistics { [weak self] report in
            DispatchQueue.main.async {
                self?.processStatsReport(report)
            }
        }
    }
    
    private func processStatsReport(_ report: RTCStatisticsReport) {
        var stats = WebRTCStats()
        
        for (_, statistic) in report.statistics {
            if statistic.type == "inbound-rtp" && statistic.values["mediaType"] as? String == "video" {
                stats.videoBytesReceived = statistic.values["bytesReceived"] as? Int64 ?? 0
                stats.videoPacketsReceived = statistic.values["packetsReceived"] as? Int64 ?? 0
                stats.videoFramesDecoded = statistic.values["framesDecoded"] as? Int64 ?? 0
            }
            
            if statistic.type == "candidate-pair" && statistic.values["state"] as? String == "succeeded" {
                stats.currentRoundTripTime = statistic.values["currentRoundTripTime"] as? Double ?? 0
            }
        }
        
        connectionStats = stats
    }
}

// MARK: - RTCPeerConnectionDelegate

extension CardWebRTCClient: RTCPeerConnectionDelegate {
    func peerConnection(_ peerConnection: RTCPeerConnection, didChange stateChanged: RTCSignalingState) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) signaling state changed: \(stateChanged)")
    }
    
    func peerConnection(_ peerConnection: RTCPeerConnection, didAdd stream: RTCMediaStream) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) stream added")
        
        DispatchQueue.main.async {
            if let videoTrack = stream.videoTracks.first {
                self.videoTrack = videoTrack
            }
            
            if let audioTrack = stream.audioTracks.first {
                self.audioTrack = audioTrack
            }
        }
    }
    
    func peerConnection(_ peerConnection: RTCPeerConnection, didRemove stream: RTCMediaStream) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) stream removed")
        
        DispatchQueue.main.async {
            self.videoTrack = nil
            self.audioTrack = nil
        }
    }
    
    func peerConnectionShouldNegotiate(_ peerConnection: RTCPeerConnection) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) should negotiate")
    }
    
    func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceConnectionState) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) ICE connection state: \(newState)")
        
        DispatchQueue.main.async {
            switch newState {
            case .connected, .completed:
                self.updateConnectionState(.connected)
            case .disconnected, .failed, .closed:
                self.updateConnectionState(.disconnected)
            default:
                break
            }
        }
    }
    
    func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceGatheringState) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) ICE gathering state: \(newState)")
    }
    
    func peerConnection(_ peerConnection: RTCPeerConnection, didGenerate candidate: RTCIceCandidate) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) ICE candidate generated")
        // 这里应该发送ICE候选到远端
    }
    
    func peerConnection(_ peerConnection: RTCPeerConnection, didRemove candidates: [RTCIceCandidate]) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) ICE candidates removed")
    }
    
    func peerConnection(_ peerConnection: RTCPeerConnection, didOpen dataChannel: RTCDataChannel) {
        print("[CardWebRTCClient] \(cardId.uuidString.prefix(8)) data channel opened")
    }
}

// MARK: - Supporting Types

struct WebRTCStats {
    var videoBytesReceived: Int64 = 0
    var videoPacketsReceived: Int64 = 0
    var videoFramesDecoded: Int64 = 0
    var currentRoundTripTime: Double = 0
}

enum CardWebRTCError: Error, LocalizedError {
    case factoryNotInitialized
    case peerConnectionCreationFailed
    case peerConnectionNotFound
    case offerCreationFailed
    case connectionFailed

    var errorDescription: String? {
        switch self {
        case .factoryNotInitialized:
            return "WebRTC factory not initialized"
        case .peerConnectionCreationFailed:
            return "Failed to create peer connection"
        case .peerConnectionNotFound:
            return "Peer connection not found"
        case .offerCreationFailed:
            return "Failed to create offer"
        case .connectionFailed:
            return "WebRTC connection failed"
        }
    }
}

//
//  CardAPIClient.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import Combine

// MARK: - Card API Client

/// 卡片专用API客户端 - 每个摄像机卡片拥有独立的API客户端实例
class CardAPIClient: ObservableObject {
    let cardId: UUID
    let ipAddress: String
    let port: UInt16
    
    @Published var isConnected: Bool = false
    @Published var lastError: Error?
    
    private let baseURL: String
    private let session: URLSession
    private var cancellables = Set<AnyCancellable>()
    
    // 连接测试相关
    private var connectionTestTimer: Timer?
    private let connectionTestInterval: TimeInterval = 30.0
    
    // MARK: - Initialization
    
    init(cardId: UUID, ipAddress: String, port: UInt16 = 80) {
        self.cardId = cardId
        self.ipAddress = ipAddress
        self.port = port
        
        // 构建基础URL，不包含默认端口80
        if port == 80 {
            self.baseURL = "http://\(ipAddress)"
        } else {
            self.baseURL = "http://\(ipAddress):\(port)"
        }
        
        // 配置URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 10.0
        config.timeoutIntervalForResource = 30.0
        self.session = URLSession(configuration: config)
        
        print("[CardAPIClient] \(cardId.uuidString.prefix(8)) initialized for \(baseURL)")
    }
    
    deinit {
        connectionTestTimer?.invalidate()
        session.invalidateAndCancel()
    }
    
    // MARK: - Connection Management
    
    @MainActor
    func connect() async -> Bool {
        print("[CardAPIClient] \(cardId.uuidString.prefix(8)) connecting to \(baseURL)")
        
        do {
            // 测试基本连接
            let isReachable = await testConnection()
            
            if isReachable {
                isConnected = true
                lastError = nil
                startConnectionMonitoring()
                print("[CardAPIClient] \(cardId.uuidString.prefix(8)) connected successfully")
                return true
            } else {
                isConnected = false
                print("[CardAPIClient] \(cardId.uuidString.prefix(8)) connection failed")
                return false
            }
        } catch {
            isConnected = false
            lastError = error
            print("[CardAPIClient] \(cardId.uuidString.prefix(8)) connection error: \(error)")
            return false
        }
    }
    
    @MainActor
    func disconnect() async {
        print("[CardAPIClient] \(cardId.uuidString.prefix(8)) disconnecting")
        
        isConnected = false
        stopConnectionMonitoring()
        session.invalidateAndCancel()
        
        print("[CardAPIClient] \(cardId.uuidString.prefix(8)) disconnected")
    }
    
    private func testConnection() async -> Bool {
        do {
            // 尝试获取设备信息来测试连接
            let _ = try await getDeviceInfo()
            return true
        } catch {
            print("[CardAPIClient] \(cardId.uuidString.prefix(8)) connection test failed: \(error)")
            return false
        }
    }
    
    private func startConnectionMonitoring() {
        stopConnectionMonitoring()
        
        connectionTestTimer = Timer.scheduledTimer(withTimeInterval: connectionTestInterval, repeats: true) { [weak self] _ in
            Task { [weak self] in
                await self?.performConnectionCheck()
            }
        }
    }
    
    private func stopConnectionMonitoring() {
        connectionTestTimer?.invalidate()
        connectionTestTimer = nil
    }
    
    @MainActor
    private func performConnectionCheck() async {
        let isReachable = await testConnection()
        
        if !isReachable && isConnected {
            isConnected = false
            print("[CardAPIClient] \(cardId.uuidString.prefix(8)) connection lost")
        }
    }
    
    // MARK: - API Methods
    
    func getDeviceInfo() async throws -> DeviceInfo {
        let url = URL(string: "\(baseURL)/info")!
        
        let (data, response) = try await session.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        let deviceInfo = try JSONDecoder().decode(DeviceInfo.self, from: data)
        return deviceInfo
    }
    
    func setPTZPosition(pan: Float, tilt: Float) async throws {
        let url = URL(string: "\(baseURL)/ctrl/ptz")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let ptzRequest = PTZRequest(pan: pan, tilt: tilt)
        request.httpBody = try JSONEncoder().encode(ptzRequest)
        
        let (_, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
    }
    
    func setZoom(level: Float) async throws {
        let url = URL(string: "\(baseURL)/ctrl/zoom")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let zoomRequest = CardZoomRequest(zoom: level)
        request.httpBody = try JSONEncoder().encode(zoomRequest)
        
        let (_, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
    }
    
    func getVideoStreamInfo() async throws -> StreamInfo {
        let url = URL(string: "\(baseURL)/stream/info")!
        
        let (data, response) = try await session.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
        
        let streamInfo = try JSONDecoder().decode(StreamInfo.self, from: data)
        return streamInfo
    }
    
    func startRecording() async throws {
        let url = URL(string: "\(baseURL)/ctrl/rec")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let recordRequest = RecordRequest(action: "start")
        request.httpBody = try JSONEncoder().encode(recordRequest)
        
        let (_, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
    }
    
    func stopRecording() async throws {
        let url = URL(string: "\(baseURL)/ctrl/rec")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let recordRequest = RecordRequest(action: "stop")
        request.httpBody = try JSONEncoder().encode(recordRequest)
        
        let (_, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw APIError.invalidResponse
        }
    }
}

// MARK: - API Models

struct DeviceInfo: Codable {
    let model: String
    let firmware: String
    let serialNumber: String
    let status: String
}

struct PTZRequest: Codable {
    let pan: Float
    let tilt: Float
}

struct CardZoomRequest: Codable {
    let zoom: Float
}

struct StreamInfo: Codable {
    let width: Int
    let height: Int
    let fps: Int
    let bitrate: Int
    let codec: String
}

struct RecordRequest: Codable {
    let action: String
}

// MARK: - API Error

enum CardAPIError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case networkError(Error)
    case decodingError(Error)

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .invalidResponse:
            return "Invalid response"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .decodingError(let error):
            return "Decoding error: \(error.localizedDescription)"
        }
    }
}

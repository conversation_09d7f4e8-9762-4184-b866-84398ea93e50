//
//  WebRTCClient.swift
//  wbCamer
//
//  Created by Assistant on 2024-12-19.
//

import Foundation
@preconcurrency import WebRTC
import Combine

/// WebRTC客户端，负责管理与摄像机的WebRTC连接
@MainActor
class WebRTCClient: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var connectionState: RTCPeerConnectionState = .new
    @Published var iceConnectionState: RTCIceConnectionState = .new
    @Published var isConnected: Bool = false
    @Published var remoteVideoTrack: RTCVideoTrack?
    @Published var localVideoTrack: RTCVideoTrack?
    
    // MARK: - Private Properties
    private var peerConnection: RTCPeerConnection?
    private var peerConnectionFactory: RTCPeerConnectionFactory
    private var iceServers: [RTCIceServer] = []
    private var localDataChannel: RTCDataChannel?
    private var remoteDataChannel: RTCDataChannel?
    
    // WebSocket for signaling
    private var signalingClient: WebSocketManager?

    // Combine
    private var cancellables = Set<AnyCancellable>()

    // Configuration
    private let config = RTCConfiguration()
    private let constraints = RTCMediaConstraints(mandatoryConstraints: nil, optionalConstraints: nil)
    
    // MARK: - Initialization
    override init() {
        // Initialize WebRTC factory
        let decoderFactory = RTCDefaultVideoDecoderFactory()
        let encoderFactory = RTCDefaultVideoEncoderFactory()
        
        self.peerConnectionFactory = RTCPeerConnectionFactory(
            encoderFactory: encoderFactory,
            decoderFactory: decoderFactory
        )
        
        super.init()
        
        setupConfiguration()
    }
    
    // MARK: - Public Methods
    
    /// 连接到指定的摄像机
    func connect(to cameraIP: String, port: Int = 8000) async throws {
        print("[WebRTCClient] Connecting to camera at \(cameraIP):\(port)")
        
        // 1. 获取ICE服务器配置
        try await fetchICEServers(from: cameraIP, port: port)
        
        // 2. 创建PeerConnection
        try createPeerConnection()
        
        // 3. 建立WebSocket信令连接
        try await setupSignalingConnection(cameraIP: cameraIP, port: port)
        
        // 4. 开始WebRTC握手
        try await startWebRTCHandshake()
    }
    
    /// 断开连接
    func disconnect() {
        print("[WebRTCClient] Disconnecting...")
        
        // 关闭数据通道
        localDataChannel?.close()
        remoteDataChannel = nil
        
        // 关闭PeerConnection
        peerConnection?.close()
        peerConnection = nil
        
        // 关闭信令连接
        signalingClient?.disconnect()
        signalingClient = nil
        
        // 重置状态
        connectionState = .new
        iceConnectionState = .new
        isConnected = false
        remoteVideoTrack = nil
        localVideoTrack = nil
    }
    
    /// 发送数据通道消息
    func sendDataChannelMessage(_ message: String) {
        guard let dataChannel = localDataChannel,
              dataChannel.readyState == .open else {
            print("[WebRTCClient] Data channel not ready")
            return
        }
        
        let buffer = RTCDataBuffer(
            data: message.data(using: .utf8)!,
            isBinary: false
        )
        
        dataChannel.sendData(buffer)
    }
    
    // MARK: - Private Methods
    
    private func setupConfiguration() {
        // 默认ICE服务器（STUN）
        let defaultIceServer = RTCIceServer(urlStrings: ["stun:stun.l.google.com:19302"])
        iceServers = [defaultIceServer]
        
        config.iceServers = iceServers
        config.sdpSemantics = .unifiedPlan
        config.continualGatheringPolicy = .gatherContinually
        
        // ICE传输策略
        config.iceTransportPolicy = .all
        config.bundlePolicy = .balanced
        config.rtcpMuxPolicy = .require
    }
    
    private func fetchICEServers(from cameraIP: String, port: Int) async throws {
        // 从摄像机获取ICE服务器配置
        let url = URL(string: "http://\(cameraIP):\(port)/api/webrtc/ice-servers")!
        
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            let iceConfig = try JSONDecoder().decode(ICEServerConfig.self, from: data)
            
            var servers: [RTCIceServer] = []
            
            // 添加STUN服务器
            for stunUrl in iceConfig.stunServers {
                let server = RTCIceServer(urlStrings: [stunUrl])
                servers.append(server)
            }
            
            // 添加TURN服务器
            for turnConfig in iceConfig.turnServers {
                let server = RTCIceServer(
                    urlStrings: [turnConfig.url],
                    username: turnConfig.username,
                    credential: turnConfig.credential
                )
                servers.append(server)
            }
            
            self.iceServers = servers
            self.config.iceServers = servers
            
            print("[WebRTCClient] Loaded \(servers.count) ICE servers")
        } catch {
            print("[WebRTCClient] Failed to fetch ICE servers: \(error)")
            // 使用默认配置
        }
    }
    
    private func createPeerConnection() throws {
        guard peerConnection == nil else {
            throw WebRTCError.connectionAlreadyExists
        }
        
        peerConnection = peerConnectionFactory.peerConnection(
            with: config,
            constraints: constraints,
            delegate: self
        )
        
        guard peerConnection != nil else {
            throw WebRTCError.failedToCreatePeerConnection
        }
        
        // 创建数据通道
        createDataChannel()
        
        print("[WebRTCClient] PeerConnection created successfully")
    }
    
    private func createDataChannel() {
        let dataChannelConfig = RTCDataChannelConfiguration()
        dataChannelConfig.channelId = 0
        dataChannelConfig.isOrdered = true
        
        localDataChannel = peerConnection?.dataChannel(
            forLabel: "control",
            configuration: dataChannelConfig
        )
        
        localDataChannel?.delegate = self
    }
    
    private func setupSignalingConnection(cameraIP: String, port: Int) async throws {
        signalingClient = WebSocketManager(baseURL: "ws://\(cameraIP):\(port)")

        // 订阅 WebSocket 消息
        signalingClient?.messagePublisher
            .sink { [weak self] message in
                Task {
                    await self?.handleWebSocketMessage(message)
                }
            }
            .store(in: &cancellables)

        // 订阅 WebSocket 错误
        signalingClient?.errorPublisher
            .sink { error in
                print("[WebRTCClient] WebSocket error: \(error)")
            }
            .store(in: &cancellables)

        // 连接 WebSocket
        signalingClient?.connect()

        // 等待连接建立
        try await waitForWebSocketConnection()

        print("[WebRTCClient] Signaling connection established")
    }
    
    private func startWebRTCHandshake() async throws {
        guard let peerConnection = peerConnection else {
            throw WebRTCError.noPeerConnection
        }
        
        // 创建Offer
        let offer = try await peerConnection.offer(for: constraints)
        try await peerConnection.setLocalDescription(offer)
        
        // 通过信令发送Offer
        let offerMessage = SignalingMessage(
            type: .offer,
            sdp: offer.sdp,
            candidate: nil
        )
        
        try await sendSignalingMessage(offerMessage)
        
        print("[WebRTCClient] WebRTC handshake started")
    }
    
    private func sendSignalingMessage(_ message: SignalingMessage) async throws {
        guard let signalingClient = signalingClient else {
            throw WebRTCError.noSignalingConnection
        }

        let data = try JSONEncoder().encode(message)

        // 创建 WebSocketMessage 来发送
        let wsMessage = WebSocketMessage(
            type: .statusUpdate, // 使用适当的类型
            data: data,
            timestamp: Date()
        )

        signalingClient.send(wsMessage)
    }
    
    private func handleSignalingMessage(_ message: SignalingMessage) async {
        switch message.type {
        case .offer:
            await handleOffer(message)
        case .answer:
            await handleAnswer(message)
        case .iceCandidate:
            await handleICECandidate(message)
        }
    }
    
    private func handleOffer(_ message: SignalingMessage) async {
        guard let peerConnection = peerConnection,
              let sdp = message.sdp else { return }
        
        let sessionDescription = RTCSessionDescription(type: .offer, sdp: sdp)
        
        do {
            try await peerConnection.setRemoteDescription(sessionDescription)
            
            // 创建Answer
            let answer = try await peerConnection.answer(for: constraints)
            try await peerConnection.setLocalDescription(answer)
            
            // 发送Answer
            let answerMessage = SignalingMessage(
                type: .answer,
                sdp: answer.sdp,
                candidate: nil
            )
            
            try await sendSignalingMessage(answerMessage)
            
        } catch {
            print("[WebRTCClient] Failed to handle offer: \(error)")
        }
    }
    
    private func handleAnswer(_ message: SignalingMessage) async {
        guard let peerConnection = peerConnection,
              let sdp = message.sdp else { return }
        
        let sessionDescription = RTCSessionDescription(type: .answer, sdp: sdp)
        
        do {
            try await peerConnection.setRemoteDescription(sessionDescription)
        } catch {
            print("[WebRTCClient] Failed to handle answer: \(error)")
        }
    }
    
    private func handleICECandidate(_ message: SignalingMessage) async {
        guard let peerConnection = peerConnection,
              let candidate = message.candidate else { return }
        
        let iceCandidate = RTCIceCandidate(
            sdp: candidate.candidate,
            sdpMLineIndex: candidate.sdpMLineIndex,
            sdpMid: candidate.sdpMid
        )
        
        do {
            try await peerConnection.add(iceCandidate)
        } catch {
            print("[WebRTCClient] Failed to add ICE candidate: \(error)")
        }
    }
}

// MARK: - RTCPeerConnectionDelegate
extension WebRTCClient: RTCPeerConnectionDelegate {
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange stateChanged: RTCSignalingState) {
        print("[WebRTCClient] Signaling state changed: \(stateChanged)")
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didAdd stream: RTCMediaStream) {
        print("[WebRTCClient] Added remote stream")
        
        if let videoTrack = stream.videoTracks.first {
            DispatchQueue.main.async {
                self.remoteVideoTrack = videoTrack
            }
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didRemove stream: RTCMediaStream) {
        print("[WebRTCClient] Removed remote stream")
        
        DispatchQueue.main.async {
            self.remoteVideoTrack = nil
        }
    }
    
    nonisolated func peerConnectionShouldNegotiate(_ peerConnection: RTCPeerConnection) {
        print("[WebRTCClient] Should negotiate")
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceConnectionState) {
        print("[WebRTCClient] ICE connection state changed: \(newState)")
        
        DispatchQueue.main.async {
            self.iceConnectionState = newState
            self.isConnected = (newState == .connected || newState == .completed)
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceGatheringState) {
        print("[WebRTCClient] ICE gathering state changed: \(newState)")
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didGenerate candidate: RTCIceCandidate) {
        print("[WebRTCClient] Generated ICE candidate")
        
        let candidateMessage = SignalingMessage(
            type: .iceCandidate,
            sdp: nil,
            candidate: ICECandidateData(
                candidate: candidate.sdp,
                sdpMLineIndex: candidate.sdpMLineIndex,
                sdpMid: candidate.sdpMid
            )
        )
        
        Task {
            do {
                try await sendSignalingMessage(candidateMessage)
            } catch {
                print("[WebRTCClient] Failed to send ICE candidate: \(error)")
            }
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didRemove candidates: [RTCIceCandidate]) {
        print("[WebRTCClient] Removed ICE candidates")
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didOpen dataChannel: RTCDataChannel) {
        print("[WebRTCClient] Data channel opened: \(dataChannel.label)")

        DispatchQueue.main.async {
            self.remoteDataChannel = dataChannel
            dataChannel.delegate = self
        }
    }
    
    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCPeerConnectionState) {
        print("[WebRTCClient] Peer connection state changed: \(newState)")
        
        DispatchQueue.main.async {
            self.connectionState = newState
        }
    }
}

// MARK: - RTCDataChannelDelegate
extension WebRTCClient: RTCDataChannelDelegate {
    
    nonisolated func dataChannelDidChangeState(_ dataChannel: RTCDataChannel) {
        print("[WebRTCClient] Data channel state changed: \(dataChannel.readyState)")
    }

    nonisolated func dataChannel(_ dataChannel: RTCDataChannel, didReceiveMessageWith buffer: RTCDataBuffer) {
        if let message = String(data: buffer.data, encoding: .utf8) {
            print("[WebRTCClient] Received data channel message: \(message)")
        }
    }
}

// MARK: - WebSocket Message Handling
extension WebRTCClient {

    private func handleWebSocketMessage(_ message: WebSocketMessage) async {
        // 处理从 WebSocket 接收到的消息
        guard let data = message.data else { return }

        do {
            let signalingMessage = try JSONDecoder().decode(SignalingMessage.self, from: data)
            await handleSignalingMessage(signalingMessage)
        } catch {
            print("[WebRTCClient] Failed to decode signaling message: \(error)")
        }
    }

    private func waitForWebSocketConnection() async throws {
        guard let signalingClient = signalingClient else {
            throw WebRTCError.noSignalingConnection
        }

        // 等待连接建立，最多等待10秒
        let timeout: TimeInterval = 10.0
        let startTime = Date()

        while !signalingClient.isConnected {
            if Date().timeIntervalSince(startTime) > timeout {
                throw WebRTCError.noSignalingConnection
            }

            try await Task.sleep(nanoseconds: 100_000_000) // 100ms
        }
    }
}

// MARK: - Supporting Types

struct ICEServerConfig: Codable {
    let stunServers: [String]
    let turnServers: [TURNServerConfig]
}

struct TURNServerConfig: Codable {
    let url: String
    let username: String
    let credential: String
}

struct SignalingMessage: Codable {
    let type: MessageType
    let sdp: String?
    let candidate: ICECandidateData?
    
    enum MessageType: String, Codable {
        case offer
        case answer
        case iceCandidate = "ice-candidate"
    }
}

struct ICECandidateData: Codable {
    let candidate: String
    let sdpMLineIndex: Int32
    let sdpMid: String?
}

enum WebRTCError: Error {
    case connectionAlreadyExists
    case failedToCreatePeerConnection
    case noPeerConnection
    case noSignalingConnection
    
    var localizedDescription: String {
        switch self {
        case .connectionAlreadyExists:
            return "WebRTC connection already exists"
        case .failedToCreatePeerConnection:
            return "Failed to create peer connection"
        case .noPeerConnection:
            return "No peer connection available"
        case .noSignalingConnection:
            return "No signaling connection available"
        }
    }
}

// MARK: - Extensions

extension RTCPeerConnectionState {
    var description: String {
        switch self {
        case .new:
            return "New"
        case .connecting:
            return "Connecting"
        case .connected:
            return "Connected"
        case .disconnected:
            return "Disconnected"
        case .failed:
            return "Failed"
        case .closed:
            return "Closed"
        @unknown default:
            return "Unknown"
        }
    }
}
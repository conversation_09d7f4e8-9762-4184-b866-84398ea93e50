//
//  APIModels.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation

// MARK: - Base Response Models

struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let message: String?
    let code: Int?
    
    enum CodingKeys: String, CodingKey {
        case success
        case data
        case message
        case code
    }
}

struct EmptyResponse: Codable {
    // Empty response for requests that don't return data
}

struct ErrorResponse: Codable {
    let error: String
    let code: Int?
    let details: String?
}

// MARK: - Camera API Models

// Camera Status Response
struct CameraStatusResponse: Codable {
    let battery: Int
    let mode: String
    let recording: Bool
    let temperature: Double?
    let storage: CameraStorageInfo
    let network: CameraNetworkInfo

    struct CameraStorageInfo: Codable {
        let total: Int64
        let used: Int64
        let available: Int64
        let cardType: String?
        
        enum CodingKeys: String, CodingKey {
            case total
            case used
            case available
            case cardType = "card_type"
        }
    }
    
    struct CameraNetworkInfo: Codable {
        let ssid: String?
        let signalStrength: Int?
        let ipAddress: String?
        
        enum CodingKeys: String, CodingKey {
            case ssid
            case signalStrength = "signal_strength"
            case ipAddress = "ip_address"
        }
    }
}

// Camera Settings Response
struct CameraSettingsResponse: Codable {
    let resolution: String
    let frameRate: Int
    let iso: Int
    let shutterSpeed: String
    let whiteBalance: String
    let exposureMode: String
    let focusMode: String
    let imageStabilization: Bool
    
    enum CodingKeys: String, CodingKey {
        case resolution
        case frameRate = "frame_rate"
        case iso
        case shutterSpeed = "shutter_speed"
        case whiteBalance = "white_balance"
        case exposureMode = "exposure_mode"
        case focusMode = "focus_mode"
        case imageStabilization = "image_stabilization"
    }
}

// File List Response
struct FileListResponse: Codable {
    let files: [CameraFile]
    let totalCount: Int
    let totalSize: Int64
    
    enum CodingKeys: String, CodingKey {
        case files
        case totalCount = "total_count"
        case totalSize = "total_size"
    }
}

struct CameraFile: Codable, Identifiable {
    let id: String
    let name: String
    let path: String
    let size: Int64
    let type: FileType
    let createdAt: Date
    let duration: Double?
    let thumbnail: String?
    let resolution: Resolution?

    enum FileType: String, Codable {
        case video = "video"
        case photo = "photo"
        case raw = "raw"
    }

    struct Resolution: Codable {
        let width: Int
        let height: Int
    }

    enum CodingKeys: String, CodingKey {
        case id
        case name
        case path
        case size
        case type
        case createdAt = "created_at"
        case duration
        case thumbnail
        case resolution
    }

    // Convenience initializer for creating sample files
    init(id: UUID, name: String, type: FileType, size: Int64, createdAt: Date, path: String, duration: Double? = nil, resolution: Resolution? = nil) {
        self.id = id.uuidString
        self.name = name
        self.path = path
        self.size = size
        self.type = type
        self.createdAt = createdAt
        self.duration = duration
        self.thumbnail = nil
        self.resolution = resolution
    }
}

// Live Stream Info Response
struct LiveStreamResponse: Codable {
    let streamUrl: String
    let streamProtocol: StreamProtocol
    let resolution: String
    let frameRate: Int
    let bitrate: Int

    enum StreamProtocol: String, Codable {
        case rtmp = "rtmp"
        case rtsp = "rtsp"
        case webrtc = "webrtc"
        case hls = "hls"
    }

    enum CodingKeys: String, CodingKey {
        case streamUrl = "stream_url"
        case streamProtocol = "protocol"
        case resolution
        case frameRate = "frame_rate"
        case bitrate
    }
}

// MARK: - Request Models

// Get Parameter Request (for /ctrl/get?k=parameter_name)
struct GetParameterRequest: APIRequest {
    typealias Response = ParameterResponse

    let path = "/ctrl/get"
    let method = HTTPMethod.GET
    let parameters: [String: Any]?

    init(key: String) {
        self.parameters = ["k": key]
    }
}

// Parameter Response (for /ctrl/get responses)
struct ParameterResponse: Codable {
    let code: Int
    let desc: String
    let key: String
    let type: Int
    let ro: Int
    let value: String
    let opts: [String]?
    let all: [String]?
}

// Camera Settings Update Request
struct UpdateCameraSettingsRequest: Codable {
    let resolution: String?
    let frameRate: Int?
    let iso: Int?
    let shutterSpeed: String?
    let whiteBalance: String?
    let exposureMode: String?
    let focusMode: String?
    let imageStabilization: Bool?
    
    enum CodingKeys: String, CodingKey {
        case resolution
        case frameRate = "frame_rate"
        case iso
        case shutterSpeed = "shutter_speed"
        case whiteBalance = "white_balance"
        case exposureMode = "exposure_mode"
        case focusMode = "focus_mode"
        case imageStabilization = "image_stabilization"
    }
}

// Recording Control Request
struct RecordingControlRequest: Codable {
    let action: RecordingAction
    let preset: String?
    
    enum RecordingAction: String, Codable {
        case start = "start"
        case stop = "stop"
        case pause = "pause"
        case resume = "resume"
    }
}

// File Operation Request
struct FileOperationRequest: Codable {
    let action: FileAction
    let files: [String]
    
    enum FileAction: String, Codable {
        case delete = "delete"
        case download = "download"
        case move = "move"
        case copy = "copy"
    }
}

// MARK: - API Request Implementations

// Get Camera Info (替代原来的GetCameraStatusRequest)
struct GetCameraInfoRequest: APIRequest {
    typealias Response = CameraInfoResponse

    let path = "/info"
    let method = HTTPMethod.GET
}

// GetCameraSettingsRequest已移除 - 不再需要查询录制格式

// Set Camera Settings
struct SetCameraSettingsRequest: APIRequest {
    typealias Response = BasicResponse

    let path: String
    let method = HTTPMethod.GET
    let parameters: [String: Any]?

    init(key: String, value: String) {
        self.path = "/ctrl/set"
        self.parameters = [key: value]
    }
}

// Start/Stop Recording
struct RecordingControlAPIRequest: APIRequest {
    typealias Response = BasicResponse

    let path: String
    let method = HTTPMethod.GET
    let parameters: [String: Any]?

    init(action: RecordingAction) {
        self.path = "/ctrl/rec"
        self.parameters = ["action": action.rawValue]
    }

    enum RecordingAction: String {
        case start = "start"
        case stop = "stop"
        case remain = "remain"
    }
}

// Get File List (使用官方DCIM目录结构)
struct GetFileListRequest: APIRequest {
    typealias Response = DCIMResponse

    let path: String
    let method = HTTPMethod.GET

    init(folder: String = "") {
        if folder.isEmpty {
            self.path = "/DCIM/"
        } else {
            self.path = "/DCIM/\(folder)"
        }
    }
}

// Delete Files (使用官方文件删除方式)
struct DeleteFileRequest: APIRequest {
    typealias Response = BasicResponse

    let path: String
    let method = HTTPMethod.GET

    init(folder: String, filename: String) {
        self.path = "/DCIM/\(folder)/\(filename)"
        self.parameters = ["act": "rm"]
    }

    let parameters: [String: Any]?
}

// Get Stream Setting (替代GetLiveStreamRequest)
struct GetStreamSettingRequest: APIRequest {
    typealias Response = StreamSettingResponse

    let path = "/ctrl/stream_setting"
    let method = HTTPMethod.GET
    let parameters: [String: Any]?

    init(index: String = "stream1") {
        self.parameters = ["action": "query", "index": index]
    }
}

// 注意：官方文档中没有snapshot API，这可能是P2-R1特有的
// 保留原有实现，但标记为可能不支持
struct CapturePhotoRequest: APIRequest {
    typealias Response = BasicResponse

    let path = "/ctrl/snapshot"  // ⚠️ 可能不被官方设备支持
    let method = HTTPMethod.GET
}

// Download File (使用官方DCIM路径)
struct DownloadFileRequest: APIRequest {
    typealias Response = Data

    let path: String
    let method = HTTPMethod.GET

    init(folder: String, filename: String) {
        self.path = "/DCIM/\(folder)/\(filename)"
    }
}

// Get Thumbnail (使用官方方式)
struct GetThumbnailRequest: APIRequest {
    typealias Response = Data

    let path: String
    let method = HTTPMethod.GET

    init(folder: String, filename: String) {
        self.path = "/DCIM/\(folder)/\(filename)"
        self.parameters = ["act": "thm"]
    }

    let parameters: [String: Any]?
}

// Get File Info (官方API)
struct GetFileInfoRequest: APIRequest {
    typealias Response = FileInfoResponse

    let path: String
    let method = HTTPMethod.GET

    init(folder: String, filename: String) {
        self.path = "/DCIM/\(folder)/\(filename)"
        self.parameters = ["act": "info"]
    }

    let parameters: [String: Any]?
}

// Session Management (官方API)
// Session control removed - not required for camera parameter queries, WebSocket, or WebRTC connections

// Work Mode Control (官方API)
struct WorkModeRequest: APIRequest {
    typealias Response = BasicResponse

    let path = "/ctrl/mode"
    let method = HTTPMethod.GET
    let parameters: [String: Any]?

    init(action: WorkModeAction) {
        self.parameters = ["action": action.rawValue]
    }

    enum WorkModeAction: String {
        case query = "query"
        case toRec = "to_rec"
        case toPb = "to_pb"
        case toStandby = "to_standby"
        case exitStandby = "exit_standby"
    }
}

// System Control (官方API)
struct SystemControlRequest: APIRequest {
    typealias Response = BasicResponse

    let path: String
    let method = HTTPMethod.GET

    init(action: SystemAction) {
        switch action {
        case .shutdown:
            self.path = "/ctrl/shutdown"
        case .reboot:
            self.path = "/ctrl/reboot"
        }
    }

    enum SystemAction {
        case shutdown
        case reboot
    }
}



// PTZ Control (基于P2-R1源码API)
struct PTZControlRequest: APIRequest {
    typealias Response = BasicResponse

    let path = "/ctrl/pt"
    let method = HTTPMethod.GET
    let parameters: [String: Any]?

    // 单方向控制 (directionMove)
    init(action: PTZAction, fspeed: Float) {
        switch action {
        case .left:
            self.parameters = ["action": "left", "fspeed": fspeed]
        case .right:
            self.parameters = ["action": "right", "fspeed": fspeed]
        case .up:
            self.parameters = ["action": "up", "fspeed": fspeed]
        case .down:
            self.parameters = ["action": "down", "fspeed": fspeed]
        case .stop:
            self.parameters = ["action": "stop"]
        }
    }

    // 同时控制pan和tilt (ptMove)
    init(panSpeed: Float, tiltSpeed: Float) {
        self.parameters = [
            "action": "pt",
            "pan_speed": panSpeed,
            "tilt_speed": tiltSpeed
        ]
    }

    // 停止所有PTZ动作
    init(stopAll: Bool = false) {
        if stopAll {
            self.parameters = ["action": "stop_all"]
        } else {
            self.parameters = ["action": "stop"]
        }
    }

    enum PTZAction {
        case left
        case right
        case up
        case down
        case stop
    }
}

// Zoom Control (基于P2-R1源码API)
struct ZoomControlRequest: APIRequest {
    typealias Response = BasicResponse

    let path = "/ctrl/lens"
    let method = HTTPMethod.GET
    let parameters: [String: Any]?

    init(action: ZoomAction, fspeed: Float = 0.5) {
        switch action {
        case .zoomIn:
            self.parameters = ["action": "zoomin", "fspeed": fspeed]
        case .zoomOut:
            self.parameters = ["action": "zoomout", "fspeed": fspeed]
        case .stop:
            self.parameters = ["action": "zoomstop"]
        case .status:
            self.parameters = ["action": "z_status"]
        case .position(let pos):
            self.parameters = ["lens_zoom_pos": pos]
        }
    }

    enum ZoomAction {
        case zoomIn
        case zoomOut
        case stop
        case status
        case position(Int)
    }
}

// Focus Control (官方API)
struct FocusControlRequest: APIRequest {
    typealias Response = BasicResponse

    let path: String
    let method = HTTPMethod.GET
    let parameters: [String: Any]?

    init(action: FocusAction) {
        switch action {
        case .autoFocus:
            self.path = "/ctrl/af"
            self.parameters = nil
        case .manualFocus(let drive):
            self.path = "/ctrl/set"
            self.parameters = ["mf_drive": drive]
        case .setPosition(let pos):
            self.path = "/ctrl/set"
            self.parameters = ["lens_focus_pos": pos]
        case .updateROI(let x, let y, let w, let h):
            self.path = "/ctrl/af"
            self.parameters = ["action": "update_roi", "x": x, "y": y, "w": w, "h": h]
        case .updateROICenter(let x, let y):
            self.path = "/ctrl/af"
            self.parameters = ["action": "update_roi_center", "x": x, "y": y]
        }
    }

    enum FocusAction {
        case autoFocus
        case manualFocus(Int)  // 正值向远处，负值向近处
        case setPosition(Int)
        case updateROI(x: Int, y: Int, w: Int, h: Int)
        case updateROICenter(x: Int, y: Int)
    }
}

// MARK: - Official API Response Models

// 官方API基本响应格式
struct BasicResponse: Codable {
    let code: Int       // 0 表示成功，1 表示失败
    let desc: String?
    let msg: String?
}

// 摄像机信息响应 (对应 /info API)
struct CameraInfoResponse: Codable {
    let model: String?
    let sw: String?      // 软件版本
    let mac: String?     // MAC地址
    let eth_ip: String?  // 以太网IP
    let sn: String?      // 序列号
}

// CameraSettingResponse已移除 - 不再需要处理录制格式查询响应

// DCIM目录响应
struct DCIMResponse: Codable {
    let folders: [String]?  // 文件夹列表
    let files: [String]?    // 文件列表
}

// 流设置响应
struct StreamSettingResponse: Codable {
    let code: Int
    let desc: String?
    let msg: String?
    let width: Int?
    let height: Int?
    let bitrate: Int?
    let fps: Int?
}

// 文件信息响应
struct FileInfoResponse: Codable {
    let size: Int?
    let duration: String?
    let resolution: String?
    let created: String?
}

// MARK: - WebSocket Message Models

struct WebSocketMessage: Codable {
    let type: MessageType
    let data: Data?
    let timestamp: Date

    enum MessageType: String, Codable {
        case statusUpdate = "status_update"
        case recordingStarted = "recording_started"
        case recordingStopped = "recording_stopped"
        case fileCreated = "file_created"
        case error = "error"
        case heartbeat = "heartbeat"
        case configChanged = "config_changed"
        case systemTimeChange = "system_time_change"
        case streamSettingUpdate = "stream_setting_update"
        case zoomUpdated = "zoom_updated"
        case afAreaUpdate = "af_area_update"
        case ptzTraceStatus = "ptz_trace_status"
        case framingUpdate = "framing_update"
        case afDone = "af_done"
        case genlockSyncStatus = "genlock_sync_status"
        case presetChange = "preset_change"
        case repairFileDone = "repair_file_done"
        case streamSettingsChanged = "stream_settings_changed"
        case presetStatus = "preset_status"
        case nicknameChange = "nickname_change"
        case preRollStarted = "pre_roll_started"
        case clearSetting = "clear_setting"
        case shutdown = "shutdown"
        case aiDetection = "ai_detection"  // AI检测消息
    }
}

// P2-R1 WebSocket消息类型枚举
enum P2R1MessageType: String, CaseIterable {
    case heartbeat = "hb"
    case configChanged = "ConfigChanged"
    case shutdown = "Shutdown"
    case systemTimeChange = "SystemTimeChange"
    case updateStreamSetting = "UpdateStreamSetting"
    case recordingStarted = "RecStart"
    case recordingStopped = "RecStop"
    case zoomUpdated = "ZoomUpdated"
    case updateAfArea = "UpdateAfArea"
    case ptzTraceSt = "PtzTraceSt"
    case hdmiInput = "hdmi_input"
    case clearSetting = "ClearSetting"
    case framingStatus = "FramingStatus"
    case framingStatusChanged = "FramingStatusChanged"
    case framingSettingChanged = "FramingSettingChanged"
    case framingInfoUpdate = "FramingInfoUpdate"
    case afDone = "AfDone"
    case hueSatColorChange = "HueSatColorChange"
    case masterGammaChange = "MasterGammaChange"
    case genlockSyncStatus = "GenlockSyncStatus"
    case presetChange = "PresetChange"
    case repairFileDone = "RepairFileDone"
    case streamSettingsChanged = "StreamSettingsChanged"
    case presetStatus = "PresetStatus"
    case nicknameChange = "NicknameChange"
    case preRollStarted = "PreRollStarted"
    case framingEvent = "FramingEvent"
    case framingRestart = "FramingRestart"
    case facedbEvent = "FacedbEvent"
    case zoomStop = "zoom_stop"
    case aiDetection = "AI"  // AI检测消息
}

// P2-R1 WebSocket消息结构
struct P2R1WebSocketMessage: Codable {
    let what: String
    let code: Int?
    let key: String?
    let value: String?
    let msg: String?
    let desc: String?
    let nickname: String?
    let index: Int?
    let val: String?
    let type: String?

    // 其他可能的字段根据实际消息内容添加
    let roi_x: Double?
    let roi_y: Double?
    let roi_w: Double?
    let roi_h: Double?
    let currentFocalLength: Double?
    let minFocalLength: Double?
}

struct HeartbeatMessage: Codable {
    let timestamp: Date
    let clientId: String
}

struct StatusUpdateMessage: Codable {
    let battery: Int?
    let recording: Bool?
    let temperature: Double?
    let storage: CameraStatusResponse.CameraStorageInfo?
}

// MARK: - AI Detection Models

// AI检测目标类型
enum AIDetectionTargetType: Int, Codable, CaseIterable {
    case unknown = 0
    case person = 1      // 人
    case head = 2        // 头部
    case face = 3        // 人脸
    case lostTarget = 4  // 丢失的目标
    
    var displayName: String {
        switch self {
        case .unknown: return "未知"
        case .person: return "人"
        case .head: return "头部"
        case .face: return "人脸"
        case .lostTarget: return "丢失目标"
        }
    }
    
    var color: String {
        switch self {
        case .unknown: return "#808080"     // 灰色
        case .person: return "#00FF00"      // 绿色
        case .head: return "#0080FF"        // 蓝色
        case .face: return "#FF8000"        // 橙色
        case .lostTarget: return "#FF0000"  // 红色
        }
    }
}

// AI检测目标
struct AIDetectionTarget: Codable, Identifiable {
    let id: Int           // 目标ID
    let x: Double         // 归一化坐标 (0.0-1.0)
    let y: Double         // 归一化坐标 (0.0-1.0)
    let width: Double     // 归一化宽度 (0.0-1.0)
    let height: Double    // 归一化高度 (0.0-1.0)
    let type: AIDetectionTargetType  // 目标类型
    let confidence: Double           // 置信度 (0.0-1.0)
    let isHighlighted: Bool         // 是否高亮显示
    let timestamp: Date             // 检测时间戳
    
    init(id: Int, x: Double, y: Double, width: Double, height: Double, 
         type: AIDetectionTargetType, confidence: Double, isHighlighted: Bool = false) {
        self.id = id
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.type = type
        self.confidence = confidence
        self.isHighlighted = isHighlighted
        self.timestamp = Date()
    }
}

// AI检测消息
struct AIDetectionMessage: Codable {
    let targets: [AIDetectionTarget]  // 检测到的目标列表
    let frameWidth: Int              // 视频帧宽度
    let frameHeight: Int             // 视频帧高度
    let timestamp: Date              // 消息时间戳
    
    init(targets: [AIDetectionTarget], frameWidth: Int = 1280, frameHeight: Int = 720) {
        self.targets = targets
        self.frameWidth = frameWidth
        self.frameHeight = frameHeight
        self.timestamp = Date()
    }
}
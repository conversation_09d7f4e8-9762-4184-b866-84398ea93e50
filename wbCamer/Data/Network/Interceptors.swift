//
//  Interceptors.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import UIKit
import os.log

// MARK: - Logging Request Interceptor

class LoggingRequestInterceptor: RequestInterceptor {
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "Network")
    
    func intercept(request: URLRequest) -> URLRequest {
        // 屏蔽PTZ相关的高频日志输出
        let urlString = request.url?.absoluteString ?? "Unknown URL"
        let isPTZRequest = urlString.contains("/ctrl/pt") || urlString.contains("/ctrl/lens")
        
        if !isPTZRequest {
            logger.info("🚀 Request: \(request.httpMethod ?? "UNKNOWN") \(urlString)")
            
            if let headers = request.allHTTPHeaderFields, !headers.isEmpty {
                logger.debug("📋 Headers: \(headers)")
            }
            
            if let body = request.httpBody {
                if let bodyString = String(data: body, encoding: .utf8) {
                    logger.debug("📦 Body: \(bodyString)")
                } else {
                    logger.debug("📦 Body: Binary data (\(body.count) bytes)")
                }
            }
        }
        
        return request
    }
}

// MARK: - Logging Response Interceptor

class LoggingResponseInterceptor: ResponseInterceptor {
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "Network")
    
    func intercept(data: Data?, response: URLResponse?, error: Error?) -> Result<Data, APIError> {
        if let error = error {
            logger.error("❌ Network Error: \(error.localizedDescription)")
            return .failure(.networkError(error))
        }
        
        guard let httpResponse = response as? HTTPURLResponse else {
            logger.error("❌ Invalid response type")
            return .failure(.networkError(NSError(domain: "InvalidResponse", code: -1)))
        }
        
        // 屏蔽PTZ相关的高频日志输出
        let urlString = httpResponse.url?.absoluteString ?? "Unknown URL"
        let isPTZResponse = urlString.contains("/ctrl/pt") || urlString.contains("/ctrl/lens")
        
        if !isPTZResponse {
            logger.info("✅ Response: \(httpResponse.statusCode) from \(urlString)")
            
            if let data = data {
                logger.debug("📥 Response Data: \(data.count) bytes")
                
                // Log response body for debugging (only in debug builds)
                #if DEBUG
                if let responseString = String(data: data, encoding: .utf8) {
                    logger.debug("📄 Response Body: \(responseString)")
                }
                #endif
            }
        }
        
        return .success(data ?? Data())
    }
}

// MARK: - Authentication Interceptor

class AuthenticationInterceptor: RequestInterceptor {
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "Auth")
    
    func intercept(request: URLRequest) -> URLRequest {
        var mutableRequest = request
        
        // Add API key or authentication token if available
        if let apiKey = getAPIKey() {
            mutableRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
            logger.debug("🔐 Added authentication header")
        }
        
        // Add device identifier
        if let deviceId = getDeviceIdentifier() {
            mutableRequest.setValue(deviceId, forHTTPHeaderField: "X-Device-ID")
        }
        
        // Add app version
        if let appVersion = getAppVersion() {
            mutableRequest.setValue(appVersion, forHTTPHeaderField: "X-App-Version")
        }
        
        return mutableRequest
    }
    
    private func getAPIKey() -> String? {
        // Retrieve API key from Keychain or UserDefaults
        return UserDefaults.standard.string(forKey: "api_key")
    }
    
    private func getDeviceIdentifier() -> String? {
        return UIDevice.current.identifierForVendor?.uuidString
    }
    
    private func getAppVersion() -> String? {
        return Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
    }
}

// MARK: - Error Handling Interceptor

class ErrorHandlingInterceptor: ResponseInterceptor {
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "ErrorHandling")
    
    func intercept(data: Data?, response: URLResponse?, error: Error?) -> Result<Data, APIError> {
        // Handle network errors first
        if let error = error {
            return handleNetworkError(error)
        }
        
        // Handle HTTP response errors
        guard let httpResponse = response as? HTTPURLResponse else {
            logger.error("Invalid HTTP response")
            return .failure(.networkError(NSError(domain: "InvalidHTTPResponse", code: -1)))
        }
        
        return handleHTTPResponse(httpResponse, data: data)
    }
    
    private func handleNetworkError(_ error: Error) -> Result<Data, APIError> {
        let nsError = error as NSError
        
        switch nsError.code {
        case NSURLErrorTimedOut:
            logger.error("Request timeout")
            return .failure(.timeout)
            
        case NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost:
            logger.error("Network connection lost")
            return .failure(.networkError(error))
            
        case NSURLErrorServerCertificateUntrusted, NSURLErrorSecureConnectionFailed:
            logger.error("SSL certificate error")
            return .failure(.sslError)
            
        case NSURLErrorCannotFindHost, NSURLErrorCannotConnectToHost:
            logger.error("Cannot connect to host")
            return .failure(.networkError(error))
            
        default:
            logger.error("Network error: \(error.localizedDescription)")
            return .failure(.networkError(error))
        }
    }
    
    private func handleHTTPResponse(_ response: HTTPURLResponse, data: Data?) -> Result<Data, APIError> {
        switch response.statusCode {
        case 200...299:
            // Success
            return .success(data ?? Data())
            
        case 400:
            logger.error("Bad Request (400)")
            let message = extractErrorMessage(from: data)
            return .failure(.serverError(400, message))
            
        case 401:
            logger.error("Unauthorized (401)")
            return .failure(.unauthorized)
            
        case 403:
            logger.error("Forbidden (403)")
            return .failure(.forbidden)
            
        case 404:
            logger.error("Not Found (404)")
            return .failure(.notFound)
            
        case 408:
            logger.error("Request Timeout (408)")
            return .failure(.timeout)
            
        case 500...599:
            logger.error("Server Error (\(response.statusCode))")
            let message = extractErrorMessage(from: data)
            return .failure(.serverError(response.statusCode, message))
            
        default:
            logger.error("Unexpected status code: \(response.statusCode)")
            let message = extractErrorMessage(from: data)
            return .failure(.serverError(response.statusCode, message))
        }
    }
    
    private func extractErrorMessage(from data: Data?) -> String? {
        guard let data = data else { return nil }
        
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                // Try common error message keys
                if let message = json["message"] as? String {
                    return message
                }
                if let error = json["error"] as? String {
                    return error
                }
                if let detail = json["detail"] as? String {
                    return detail
                }
            }
        } catch {
            logger.debug("Failed to parse error response as JSON")
        }
        
        // Fallback to raw string
        return String(data: data, encoding: .utf8)
    }
}

// MARK: - Retry Interceptor

class RetryInterceptor: ResponseInterceptor {
    private let maxRetries: Int
    private let retryDelay: TimeInterval
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "Retry")
    
    init(maxRetries: Int = 3, retryDelay: TimeInterval = 1.0) {
        self.maxRetries = maxRetries
        self.retryDelay = retryDelay
    }
    
    func intercept(data: Data?, response: URLResponse?, error: Error?) -> Result<Data, APIError> {
        // This is a simplified version - in a real implementation,
        // you would need to store retry count and implement actual retry logic
        
        if let error = error {
            let nsError = error as NSError
            
            // Only retry on specific network errors
            switch nsError.code {
            case NSURLErrorTimedOut, NSURLErrorNetworkConnectionLost:
                logger.info("Network error detected, retry might be needed")
                return .failure(.networkError(error))
            default:
                return .failure(.networkError(error))
            }
        }
        
        return .success(data ?? Data())
    }
}

// MARK: - Cache Control Interceptor

class CacheControlInterceptor: RequestInterceptor {
    private let cachePolicy: URLRequest.CachePolicy
    
    init(cachePolicy: URLRequest.CachePolicy = .useProtocolCachePolicy) {
        self.cachePolicy = cachePolicy
    }
    
    func intercept(request: URLRequest) -> URLRequest {
        var mutableRequest = request
        mutableRequest.cachePolicy = cachePolicy
        
        // Add cache control headers
        switch cachePolicy {
        case .reloadIgnoringLocalCacheData:
            mutableRequest.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
        case .returnCacheDataElseLoad:
            mutableRequest.setValue("max-age=3600", forHTTPHeaderField: "Cache-Control")
        default:
            break
        }
        
        return mutableRequest
    }
}
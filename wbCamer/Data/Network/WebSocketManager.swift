//
//  WebSocketManager.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine
import Network
import UIKit
import os.log

// MARK: - DateFormatter Extension for Timestamps
extension DateFormatter {
    static let iso8601Full: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        formatter.timeZone = TimeZone.current
        return formatter
    }()
}

// MARK: - WebSocket Connection State

enum WebSocketState: Equatable {
    case disconnected
    case connecting
    case connected
    case reconnecting
    case failed(Error)

    static func == (lhs: WebSocketState, rhs: WebSocketState) -> Bool {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected),
             (.connecting, .connecting),
             (.connected, .connected),
             (.reconnecting, .reconnecting):
            return true
        case (.failed, .failed):
            return true
        default:
            return false
        }
    }
}

// MARK: - WebSocket Error Types

enum WebSocketError: Error, LocalizedError {
    case connectionFailed
    case invalidURL
    case tlsHandshakeFailed
    case messageSerializationFailed
    case heartbeatTimeout
    case maxReconnectAttemptsReached
    
    var errorDescription: String? {
        switch self {
        case .connectionFailed:
            return "WebSocket connection failed"
        case .invalidURL:
            return "Invalid WebSocket URL"
        case .tlsHandshakeFailed:
            return "TLS handshake failed"
        case .messageSerializationFailed:
            return "Failed to serialize message"
        case .heartbeatTimeout:
            return "Heartbeat timeout"
        case .maxReconnectAttemptsReached:
            return "Maximum reconnection attempts reached"
        }
    }
}

// MARK: - WebSocket Manager

class WebSocketManager: NSObject, ObservableObject {
    static let shared = WebSocketManager()
    
    // MARK: - Published Properties
    @Published var connectionState: WebSocketState = .disconnected
    @Published var isConnected: Bool = false
    
    // MARK: - Private Properties
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private let messageQueue = DispatchQueue(label: "websocket.message.queue", qos: .userInitiated)
    private let heartbeatQueue = DispatchQueue(label: "websocket.heartbeat.queue")
    
    // Configuration
    private var baseURL: String
    private var heartbeatInterval: TimeInterval = 11.0 // P2-R1设备心跳间隔为11秒
    private var maxReconnectAttempts = 8
    private var baseReconnectDelay: TimeInterval = 1.0
    private var currentReconnectDelay: TimeInterval = 1.0
    
    // State management
    private var reconnectAttempts = 0
    private var heartbeatTimeoutTimer: Timer?  // 改为超时检测定时器
    //private var lastHeartbeatReceived: Date?   // 最后收到心跳的时间
    private var connectionStartTime: Date?     // 连接建立时间，用于监控连接持续时间
    private var messageBuffer: [WebSocketMessage] = []
    private var isReconnecting = false
    
    // Network monitoring
    private var networkMonitor: NWPathMonitor?
    private let networkQueue = DispatchQueue(label: "WebSocketNetworkMonitor")
    private var lastNetworkPath: NWPath?
    
    // Combine
    private var cancellables = Set<AnyCancellable>()
    private let messageSubject = PassthroughSubject<WebSocketMessage, Never>()
    private let errorSubject = PassthroughSubject<WebSocketError, Never>()
    
    // Logger
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "WebSocket")

    // 日志控制开关 - 设为false来屏蔽详细WebSocket日志
    private let enableVerboseLogging = false
    
    // MARK: - Publishers
    var messagePublisher: AnyPublisher<WebSocketMessage, Never> {
        messageSubject.eraseToAnyPublisher()
    }
    
    var errorPublisher: AnyPublisher<WebSocketError, Never> {
        errorSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    
    init(baseURL: String = "", configuration: WebSocketConfiguration = WebSocketConfiguration()) {
        self.baseURL = baseURL
        super.init()
        
        // Apply configuration
        self.heartbeatInterval = configuration.heartbeatInterval
        self.maxReconnectAttempts = configuration.maxReconnectAttempts
        self.baseReconnectDelay = configuration.reconnectDelay
        self.currentReconnectDelay = configuration.reconnectDelay
        
        setupURLSession()
        setupNetworkMonitoring()
    }
    
    private func setupURLSession() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30.0
        // WebSocket长连接：设置24小时超时，实际上禁用URLSession层面的资源超时
        // 连接管理完全依赖应用层心跳机制（11秒心跳+16.5秒超时检测）
        // 这样避免了URLSession超时与WebSocket长连接的冲突
        configuration.timeoutIntervalForResource = 86400.0  // 24小时
        
        urlSession = URLSession(configuration: configuration, delegate: self, delegateQueue: nil)
    }
    
    // MARK: - Configuration Management

    func updateBaseURL(to urlString: String) {
        // Disconnect if currently connected
        if connectionState == .connected {
            disconnect()
        }

        self.baseURL = urlString
        if enableVerboseLogging {
            print("WebSocketManager base URL updated to: \(urlString)")
        }
    }

    // MARK: - Helper Methods

    private func generateWebSocketKey() -> String {
        let keyData = Data((0..<16).map { _ in UInt8.random(in: 0...255) })
        return keyData.base64EncodedString()
    }

    // MARK: - Connection Management

    func connect() {
        guard connectionState != .connected && connectionState != .connecting else {
            logger.info("WebSocket already connected or connecting")
            return
        }

        // 检查baseURL是否为空或无效
        guard !baseURL.isEmpty else {
            logger.warning("Cannot connect: baseURL is empty")
            return
        }

        // 使用传入的baseURL，不强制修改端口
        let wsURL = baseURL

        guard let url = URL(string: wsURL) else {
            logger.error("Invalid WebSocket URL: \(wsURL)")
            updateConnectionState(.failed(WebSocketError.invalidURL))
            return
        }
        
        logger.info("Connecting to WebSocket: \(url.absoluteString)")
        updateConnectionState(.connecting)
        
        var request = URLRequest(url: url)

        // 基于浏览器成功连接P2-R1的事实，完全模拟浏览器的WebSocket请求
        // 设置标准WebSocket升级头部
        request.setValue("websocket", forHTTPHeaderField: "Upgrade")
        request.setValue("Upgrade", forHTTPHeaderField: "Connection")
        request.setValue("13", forHTTPHeaderField: "Sec-WebSocket-Version")
        request.setValue(generateWebSocketKey(), forHTTPHeaderField: "Sec-WebSocket-Key")

        // 关键：设置与浏览器完全相同的头部
        request.setValue("http://\(url.host ?? "")", forHTTPHeaderField: "Origin")
        request.setValue("Mozilla/5.0 (iPhone; CPU iPhone OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1", forHTTPHeaderField: "User-Agent")

        // 浏览器标准头部
        request.setValue("gzip, deflate", forHTTPHeaderField: "Accept-Encoding")
        request.setValue("en-US,en;q=0.9", forHTTPHeaderField: "Accept-Language")
        request.setValue("same-origin", forHTTPHeaderField: "Sec-Fetch-Site")
        request.setValue("websocket", forHTTPHeaderField: "Sec-Fetch-Mode")
        request.setValue("empty", forHTTPHeaderField: "Sec-Fetch-Dest")

        // 设置合理的超时
        request.timeoutInterval = 30.0

        // Add authentication if available (但P2-R1可能不需要)
        if let apiKey = UserDefaults.standard.string(forKey: "api_key") {
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        }

        // 打印详细的请求信息用于调试
        if enableVerboseLogging {
            print("🔍 WebSocket Request Details (模拟浏览器):")
            print("   URL: \(url.absoluteString)")
            print("   Headers:")
            for (key, value) in request.allHTTPHeaderFields ?? [:] {
                print("     \(key): \(value)")
            }
        }
        
        webSocketTask = urlSession?.webSocketTask(with: request)
        webSocketTask?.resume()

        startReceiving()
        // P2-R1 uses passive heartbeat - we only monitor for server heartbeats
    }
    
    func disconnect() {
        logger.info("Disconnecting WebSocket")

        // 记录连接持续时间
        if let startTime = connectionStartTime {
            let duration = Date().timeIntervalSince(startTime)
            let timestamp = DateFormatter.iso8601Full.string(from: Date())
            print("[\(timestamp)] 📊 WebSocket连接持续时间: \(String(format: "%.1f", duration))秒 (手动断开)")
            logger.info("WebSocket connection duration: \(String(format: "%.1f", duration))s (manual disconnect)")
        }
        connectionStartTime = nil  // 重置连接开始时间

        stopHeartbeatTimeoutMonitoring()
        stopNetworkMonitoring()
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil

        updateConnectionState(.disconnected)
        reconnectAttempts = 0
        isReconnecting = false
    }
    
    private func reconnect() {
        guard !isReconnecting && reconnectAttempts < maxReconnectAttempts else {
            if reconnectAttempts >= maxReconnectAttempts {
                logger.error("Max reconnection attempts reached")
                errorSubject.send(.maxReconnectAttemptsReached)
                updateConnectionState(.failed(WebSocketError.maxReconnectAttemptsReached))
            }
            return
        }
        
        isReconnecting = true
        reconnectAttempts += 1
        
        // Implement exponential backoff: 1s, 2s, 4s, 8s, 16s, 32s, 64s, 128s
        currentReconnectDelay = min(baseReconnectDelay * pow(2.0, Double(reconnectAttempts - 1)), 128.0)
        
        logger.info("Attempting to reconnect (\(self.reconnectAttempts)/\(self.maxReconnectAttempts)) in \(self.currentReconnectDelay)s")
        updateConnectionState(.reconnecting)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + currentReconnectDelay) { [weak self] in
            self?.isReconnecting = false
            self?.connect()
        }
    }
    
    // MARK: - Message Handling
    
    private func startReceiving() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                self?.handleReceivedMessage(message)
                self?.startReceiving() // Continue receiving
                
            case .failure(let error):
                self?.logger.error("WebSocket receive error: \(error.localizedDescription)")
                self?.handleConnectionError(error)
            }
        }
    }
    
    private func handleReceivedMessage(_ message: URLSessionWebSocketTask.Message) {
        messageQueue.async { [weak self] in
            guard let self = self else { return }
            
            switch message {
            case .string(let text):
                // 只记录非心跳消息
                if !text.contains("\"what\":\"hb\"") && text.trimmingCharacters(in: .whitespacesAndNewlines) != "hb" {
                    //self.logger.debug("Received text message: \(text)")
                }
                self.processTextMessage(text)
                
            case .data(let data):
                self.logger.debug("Received binary message: \(data.count) bytes")
                self.processBinaryMessage(data)
                
            @unknown default:
                self.logger.warning("Received unknown message type")
            }
        }
    }
    
    private func processTextMessage(_ text: String) {
        guard let data = text.data(using: .utf8) else {
            logger.error("Failed to convert text message to data")
            return
        }

        // First try to parse as P2-R1 message format
        if let p2r1Message = parseP2R1Message(text) {
            handleP2R1Message(p2r1Message)
            return
        }

        // Fallback to standard WebSocket message format
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let message = try decoder.decode(WebSocketMessage.self, from: data)

            DispatchQueue.main.async {
                self.messageSubject.send(message)
            }

        } catch {
            logger.error("Failed to decode WebSocket message: \(error.localizedDescription)")
            logger.debug("Raw message: \(text)")
        }
    }
    
    private func processBinaryMessage(_ data: Data) {
        // Handle binary messages if needed
        logger.debug("Processing binary message of \(data.count) bytes")
    }
    
    // MARK: - Message Sending
    
    func send<T: Codable>(_ message: T) {
        guard connectionState == .connected else {
            logger.warning("Cannot send message: WebSocket not connected")
            // Buffer the message for later sending
            if let wsMessage = message as? WebSocketMessage {
                messageBuffer.append(wsMessage)
            }
            return
        }
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(message)
            let text = String(data: data, encoding: .utf8) ?? ""
            
            let wsMessage = URLSessionWebSocketTask.Message.string(text)
            
            webSocketTask?.send(wsMessage) { [weak self] error in
                if let error = error {
                    self?.logger.error("Failed to send message: \(error.localizedDescription)")
                    self?.errorSubject.send(.messageSerializationFailed)
                } else {
                    self?.logger.debug("Message sent successfully")
                }
            }
            
        } catch {
            logger.error("Failed to encode message: \(error.localizedDescription)")
            errorSubject.send(.messageSerializationFailed)
        }
    }
    
    private func sendBufferedMessages() {
        guard !self.messageBuffer.isEmpty else { return }

        logger.info("Sending \(self.messageBuffer.count) buffered messages")

        for message in self.messageBuffer {
            send(message)
        }

        self.messageBuffer.removeAll()
    }
    
    // MARK: - P2-R1 Heartbeat Management (Passive)

    private func startHeartbeatTimeoutMonitoring() {
        stopHeartbeatTimeoutMonitoring()
        resetHeartbeatTimeout()
        logger.debug("Started P2-R1 heartbeat timeout monitoring")
    }

    private func stopHeartbeatTimeoutMonitoring() {
        heartbeatTimeoutTimer?.invalidate()
        heartbeatTimeoutTimer = nil
        logger.debug("Stopped heartbeat timeout monitoring")
    }

    private func resetHeartbeatTimeout() {
        heartbeatTimeoutTimer?.invalidate()
        
        // P2-R1设备心跳间隔为11秒，设置16.5秒超时（1.5倍容错）
        // 根据Web页面分析，设备在11秒内必须发送心跳，否则连接会被关闭
        let timeoutInterval = heartbeatInterval * 1.5
        
        heartbeatTimeoutTimer = Timer.scheduledTimer(withTimeInterval: timeoutInterval, repeats: false) { [weak self] _ in
            guard let self = self else { return }
            
            // Check if we're still connected and network is available
            if let networkPath = self.lastNetworkPath, networkPath.status != .satisfied {
                self.logger.info("心跳超时但网络不可用 - 网络恢复时将重新连接")
                return
            }
            
            let timestamp = DateFormatter.iso8601Full.string(from: Date())
            print("[\(timestamp)] ⚠️ P2-R1心跳超时 (\(timeoutInterval)秒) - 关闭连接")
            self.logger.warning("P2-R1 heartbeat timeout after \(timeoutInterval)s - closing connection")
            self.handleConnectionError(WebSocketError.heartbeatTimeout)
        }
        //lastHeartbeatReceived = Date()
    }
    
    // MARK: - Network Monitoring
    
    private func setupNetworkMonitoring() {
        networkMonitor = NWPathMonitor()
        
        networkMonitor?.pathUpdateHandler = { [weak self] path in
            self?.handleNetworkPathUpdate(path)
        }
        
        networkMonitor?.start(queue: networkQueue)
        logger.debug("Network monitoring started")
    }
    
    private func stopNetworkMonitoring() {
        networkMonitor?.cancel()
        networkMonitor = nil
        logger.debug("Network monitoring stopped")
    }
    
    private func handleNetworkPathUpdate(_ path: NWPath) {
        let wasConnected = lastNetworkPath?.status == .satisfied
        let isConnected = path.status == .satisfied
        
        // 记录网络状态变化
        if enableVerboseLogging {
            logger.debug("Network status: \(String(describing: path.status))")
            logger.debug("Network interfaces: \(path.availableInterfaces.count)")
        }
        
        // 网络从断开到连接
        if !wasConnected && isConnected {
            logger.info("Network became available - attempting to reconnect WebSocket")
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                if self?.connectionState == .disconnected || self?.connectionState == .failed(WebSocketError.connectionFailed) {
                    self?.connect()
                }
            }
        }
        // 网络从连接到断开
        else if wasConnected && !isConnected {
            logger.warning("Network became unavailable")
            if connectionState == .connected {
                updateConnectionState(.disconnected)
            }
        }
        
        lastNetworkPath = path
    }
    
    // MARK: - State Management
    
    private func updateConnectionState(_ newState: WebSocketState) {
        let timestamp = DateFormatter.iso8601Full.string(from: Date())
        
        DispatchQueue.main.async {
            self.connectionState = newState
            self.isConnected = (newState == .connected)
            
            // 添加时间戳日志以分析WebSocket状态变化模式
            switch newState {
            case .disconnected:
                print("[\(timestamp)] 🔌 WebSocket状态: 已断开连接")
            case .connecting:
                print("[\(timestamp)] 🔌 WebSocket状态: 正在连接...")
            case .connected:
                print("[\(timestamp)] 🔌 WebSocket状态: 已连接")
            case .failed(let error):
                print("[\(timestamp)] 🔌 WebSocket状态: 连接失败 - \(error.localizedDescription)")
            case .reconnecting:
                print("[\(timestamp)] 🔌 WebSocket状态: 正在重新连接...")
            }
            
            if newState == .connected {
                self.reconnectAttempts = 0
                self.currentReconnectDelay = self.baseReconnectDelay // Reset to base delay
                //self.lastHeartbeatReceived = Date()
                self.startHeartbeatTimeoutMonitoring()
                self.sendBufferedMessages()
            }
        }
    }
    
    private func handleConnectionError(_ error: Error) {
        logger.error("WebSocket connection error: \(error.localizedDescription)")
        
        updateConnectionState(.failed(error))
        
        // Attempt to reconnect
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.reconnect()
        }
    }
}

// MARK: - URLSessionWebSocketDelegate

extension WebSocketManager: URLSessionWebSocketDelegate {
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        let protocolName = `protocol` ?? "none"
        logger.info("WebSocket connection opened with protocol: \(protocolName)")
        connectionStartTime = Date()  // 记录连接建立时间
        updateConnectionState(.connected)
    }
    
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        let reasonString = reason.flatMap { String(data: $0, encoding: .utf8) } ?? "No reason"
        
        // 记录连接持续时间
        if let startTime = connectionStartTime {
            let duration = Date().timeIntervalSince(startTime)
            let timestamp = DateFormatter.iso8601Full.string(from: Date())
            print("[\(timestamp)] 📊 WebSocket连接持续时间: \(String(format: "%.1f", duration))秒")
            logger.info("WebSocket connection duration: \(String(format: "%.1f", duration))s")
        }
        
        logger.info("WebSocket connection closed with code: \(closeCode.rawValue), reason: \(reasonString)")
        
        updateConnectionState(.disconnected)
        
        // Attempt to reconnect unless it was a normal closure
        if closeCode != .normalClosure {
            reconnect()
        }
    }
}

// MARK: - URLSessionDelegate

extension WebSocketManager: URLSessionDelegate {
    func urlSession(_ session: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
        
        // Handle SSL certificate validation
        guard challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust else {
            completionHandler(.performDefaultHandling, nil)
            return
        }
        
        // In production, implement proper certificate validation
        // For development, you might want to allow self-signed certificates
        
        #if DEBUG
        // Allow self-signed certificates in debug mode
        if let serverTrust = challenge.protectionSpace.serverTrust {
            let credential = URLCredential(trust: serverTrust)
            completionHandler(.useCredential, credential)
        } else {
            completionHandler(.performDefaultHandling, nil)
        }
        #else
        // In production, use proper certificate validation
        completionHandler(.performDefaultHandling, nil)
        #endif
    }
    
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            let nsError = error as NSError
            logger.error("URLSession task completed with error: \(error.localizedDescription)")

            // 记录连接持续时间
            if let startTime = connectionStartTime {
                let duration = Date().timeIntervalSince(startTime)
                let timestamp = DateFormatter.iso8601Full.string(from: Date())
                print("[\(timestamp)] 📊 WebSocket连接持续时间: \(String(format: "%.1f", duration))秒 (错误断开)")
                logger.info("WebSocket connection duration: \(String(format: "%.1f", duration))s (error)")
            }
            connectionStartTime = nil  // 重置连接开始时间

            // 详细的错误分析
            if enableVerboseLogging {
                print("🔍 WebSocket Error Analysis:")
                print("   Domain: \(nsError.domain)")
                print("   Code: \(nsError.code)")
                print("   Description: \(nsError.localizedDescription)")
                print("   UserInfo: \(nsError.userInfo)")

                // 检查具体的错误类型
                if nsError.domain == NSURLErrorDomain {
                    switch nsError.code {
                    case NSURLErrorSecureConnectionFailed:
                        print("   ❌ TLS handshake failed")
                    case NSURLErrorNetworkConnectionLost:
                        print("   ❌ Network connection lost")
                    case NSURLErrorCannotConnectToHost:
                        print("   ❌ Cannot connect to host")
                    case NSURLErrorTimedOut:
                        print("   ❌ Connection timed out")
                    case NSURLErrorNotConnectedToInternet:
                        print("   ❌ Not connected to internet")
                    default:
                        print("   ❌ Other URL error: \(nsError.code)")
                    }
                }
            }

            // 错误处理逻辑（保留）
            if nsError.domain == NSURLErrorDomain {
                switch nsError.code {
                case NSURLErrorSecureConnectionFailed:
                    errorSubject.send(.tlsHandshakeFailed)
                default:
                    break
                }
            }

            handleConnectionError(error)
        }
    }
}

// MARK: - P2-R1 Message Handling

extension WebSocketManager {

    private func parseP2R1Message(_ text: String) -> [String: Any]? {
        guard let data = text.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              json["what"] != nil else {
            return nil
        }
        return json
    }

    private func handleP2R1Message(_ message: [String: Any]) {
        guard let what = message["what"] as? String else { return }

        //logger.debug("Received P2-R1 message: \(what)")

        switch what {
        case "hb":
            // P2-R1 heartbeat - reset timeout timer
            //let timestamp = DateFormatter.iso8601Full.string(from: Date())
            //let timeSinceLastHeartbeat = lastHeartbeatReceived?.timeIntervalSinceNow ?? 0
            
            //print("[\(timestamp)] 💓 收到P2-R1心跳 (距上次: \(String(format: "%.1f", abs(timeSinceLastHeartbeat)))秒)")
            
            resetHeartbeatTimeout()

        case "ConfigChanged":
            handleConfigChanged(message)

        case "RecStart":
            handleRecordingStarted(message)

        case "RecStop":
            handleRecordingStopped(message)

        case "Shutdown":
            handleShutdown(message)

        case "SystemTimeChange":
            handleSystemTimeChange(message)

        case "UpdateStreamSetting":
            handleStreamSettingUpdate(message)

        case "ZoomUpdated":
            handleZoomUpdated(message)

        case "UpdateAfArea":
            handleAfAreaUpdate(message)

        case "PtzTraceSt":
            handlePtzTraceStatus(message)

        case "FramingStatus", "FramingStatusChanged", "FramingSettingChanged", "FramingInfoUpdate":
            handleFramingUpdate(message)

        case "AfDone":
            handleAfDone(message)

        case "GenlockSyncStatus":
            handleGenlockSyncStatus(message)

        case "PresetChange":
            handlePresetChange(message)

        case "RepairFileDone":
            handleRepairFileDone(message)

        case "StreamSettingsChanged":
            handleStreamSettingsChanged(message)

        case "PresetStatus":
            handlePresetStatus(message)

        case "NicknameChange":
            handleNicknameChange(message)

        case "PreRollStarted":
            handlePreRollStarted(message)

        case "ClearSetting":
            handleClearSetting(message)

        case "AI":
            handleAIDetection(message)

        default:
            //logger.debug("Unknown P2-R1 message type: \(what)")
            // Forward unknown messages as generic WebSocket messages
            forwardAsGenericMessage(message)
        }
    }

    // MARK: - P2-R1 Message Handlers

    private func handleConfigChanged(_ message: [String: Any]) {
        guard let key = message["key"] as? String else { return }

        //logger.debug("Config changed: \(key)")

        // Create a status update message for the app
        let statusUpdate = StatusUpdateMessage(
            battery: key == "battery_voltage" ? Int(message["value"] as? String ?? "0") : nil,
            recording: key.contains("rec") ? (message["value"] as? String == "true") : nil,
            temperature: key == "temperature" ? Double(message["value"] as? String ?? "0") : nil,
            storage: nil
        )

        let wsMessage = WebSocketMessage(
            type: .statusUpdate,
            data: try? JSONEncoder().encode(statusUpdate),
            timestamp: Date()
        )

        DispatchQueue.main.async {
            self.messageSubject.send(wsMessage)
        }
    }

    private func handleRecordingStarted(_ message: [String: Any]) {
        let wsMessage = WebSocketMessage(
            type: .recordingStarted,
            data: nil,
            timestamp: Date()
        )

        DispatchQueue.main.async {
            self.messageSubject.send(wsMessage)
        }
    }

    private func handleRecordingStopped(_ message: [String: Any]) {
        let wsMessage = WebSocketMessage(
            type: .recordingStopped,
            data: nil,
            timestamp: Date()
        )

        DispatchQueue.main.async {
            self.messageSubject.send(wsMessage)
        }
    }

    private func handleShutdown(_ message: [String: Any]) {
        logger.warning("Camera is shutting down")
        disconnect()
    }

    private func handleSystemTimeChange(_ message: [String: Any]) {
        logger.debug("System time changed")
        // Could trigger time sync or UI updates
    }

    private func handleStreamSettingUpdate(_ message: [String: Any]) {
        logger.debug("Stream settings updated")
        // Could trigger stream configuration refresh
    }

    private func handleZoomUpdated(_ message: [String: Any]) {
        //logger.debug("Zoom updated")
        // Could update zoom controls in UI
    }

    private func handleAfAreaUpdate(_ message: [String: Any]) {
        //logger.debug("AF area updated")
        // Could update autofocus area display
    }

    private func handlePtzTraceStatus(_ message: [String: Any]) {
        logger.debug("PTZ trace status updated")
        // Could update PTZ controls
    }

    private func handleFramingUpdate(_ message: [String: Any]) {
        logger.debug("Framing status updated")
        // Could update framing controls
    }

    private func handleAfDone(_ message: [String: Any]) {
        logger.debug("Autofocus completed")
        // Could update focus status
    }

    private func handleGenlockSyncStatus(_ message: [String: Any]) {
        logger.debug("Genlock sync status updated")
        // Could update sync status display
    }

    private func handlePresetChange(_ message: [String: Any]) {
        logger.debug("Preset changed")
        // Could update preset controls
    }

    private func handleRepairFileDone(_ message: [String: Any]) {
        logger.debug("File repair completed")
        // Could refresh file list
    }

    private func handleStreamSettingsChanged(_ message: [String: Any]) {
        logger.debug("Stream settings changed")
        // Could update streaming controls
    }

    private func handlePresetStatus(_ message: [String: Any]) {
        logger.debug("Preset status updated")
        // Could update preset status display
    }

    private func handleNicknameChange(_ message: [String: Any]) {
        if let nickname = message["nickname"] as? String {
            logger.debug("Camera nickname changed to: \(nickname)")
            // Could update device name display
        }
    }

    private func handlePreRollStarted(_ message: [String: Any]) {
        logger.debug("Pre-roll started")
        // Could update recording status
    }

    private func handleClearSetting(_ message: [String: Any]) {
        logger.debug("Settings cleared")
        // Could trigger settings refresh
    }

    // handleAIDetectionMessage方法已移除，因为不再需要从CameraManager调用
    // AI检测消息直接通过handleP2R1Message -> handleAIDetection处理
    
    private func handleAIDetection(_ message: [String: Any]) {
        print("🔍 [AI Detection] 开始处理AI检测消息: \(message)")
        
        // 解析AI检测数据
        guard let targets = parseAIDetectionTargets(from: message) else {
            print("❌ [AI Detection] 解析AI检测数据失败")
            logger.warning("Failed to parse AI detection data")
            return
        }
        
        print("✅ [AI Detection] 成功解析到 \(targets.count) 个目标")
        for (index, target) in targets.enumerated() {
            print("   目标\(index): ID=\(target.id), 类型=\(target.type.displayName), 置信度=\(String(format: "%.2f", target.confidence)), 位置=(\(String(format: "%.3f", target.x)), \(String(format: "%.3f", target.y))), 尺寸=(\(String(format: "%.3f", target.width)), \(String(format: "%.3f", target.height)))")
        }

        // 创建AI检测消息
        let aiDetectionMessage = AIDetectionMessage(targets: targets)
        print("📦 [AI Detection] 创建AIDetectionMessage，包含 \(aiDetectionMessage.targets.count) 个目标")
        
        // 编码为JSON数据
        guard let data = try? JSONEncoder().encode(aiDetectionMessage) else {
            print("❌ [AI Detection] 编码AI检测消息失败")
            logger.error("Failed to encode AI detection message")
            return
        }
        
        print("✅ [AI Detection] 成功编码AI检测消息，数据大小: \(data.count) 字节")

        // 创建WebSocket消息
        let wsMessage = WebSocketMessage(
            type: .aiDetection,
            data: data,
            timestamp: Date()
        )
        
        print("📨 [AI Detection] 创建WebSocket消息，类型: \(wsMessage.type)")

        // 发送到主线程
        DispatchQueue.main.async {
            print("🚀 [AI Detection] 发送消息到messageSubject")
            self.messageSubject.send(wsMessage)
            print("✅ [AI Detection] 消息已发送到messageSubject")
        }
    }

    // 解析AI检测目标数据
    private func parseAIDetectionTargets(from message: [String: Any]) -> [AIDetectionTarget]? {
        print("🔍 [Parse AI] 开始解析AI检测数据")
        // 根据P2-R1源码分析，AI检测数据格式如下：
        // {
        //   "what": "AI",
        //   "pending": 0,
        //   "num": 1,
        //   "cols": 8,
        //   "objs": [app, id, type, confidence, x, y, width, height, ...]
        // }
        // 每个目标占用8个连续的数组元素，顺序为：app, id, type, confidence, x, y, width, height
        
        guard let numObjects = message["num"] as? Int else {
            print("❌ [Parse AI] 消息中缺少'num'字段或类型错误")
            return nil
        }
        
        print("📊 [Parse AI] 检测到目标数量: \(numObjects)")
        
        guard numObjects > 0 else {
            print("ℹ️ [Parse AI] 没有检测到目标，返回空数组")
            return []  // 没有检测到目标
        }
        
        guard let cols = message["cols"] as? Int else {
            print("❌ [Parse AI] 消息中缺少'cols'字段或类型错误")
            return nil
        }
        
        guard let objsArray = message["objs"] as? [Any] else {
            print("❌ [Parse AI] 消息中缺少'objs'数组或类型错误")
            return nil
        }
        
        print("📊 [Parse AI] 数据列数: \(cols), objs数组长度: \(objsArray.count)")
        
        // 验证数组长度是否正确
        let expectedLength = numObjects * cols
        guard objsArray.count >= expectedLength else {
            print("❌ [Parse AI] objs数组长度不足，期望: \(expectedLength), 实际: \(objsArray.count)")
            return nil
        }
        
        var targets: [AIDetectionTarget] = []
        
        for i in 0..<numObjects {
            print("🔍 [Parse AI] 解析目标 \(i)")
            
            let startIndex = cols * i
            
            // 确保数组索引不越界
            guard startIndex + 7 < objsArray.count else {
                print("❌ [Parse AI] 目标 \(i) 数据索引越界，跳过")
                continue
            }
            
            // 提取目标数据，按照P2-R1源码的顺序：app, id, type, confidence, x, y, width, height
            guard let app = (objsArray[startIndex] as? NSNumber)?.intValue,
                  let id = (objsArray[startIndex + 1] as? NSNumber)?.intValue,
                  let typeValue = (objsArray[startIndex + 2] as? NSNumber)?.intValue,
                  let confidence = (objsArray[startIndex + 3] as? NSNumber)?.doubleValue,
                  let x = (objsArray[startIndex + 4] as? NSNumber)?.doubleValue,
                  let y = (objsArray[startIndex + 5] as? NSNumber)?.doubleValue,
                  let width = (objsArray[startIndex + 6] as? NSNumber)?.doubleValue,
                  let height = (objsArray[startIndex + 7] as? NSNumber)?.doubleValue else {
                print("❌ [Parse AI] 目标 \(i) 数据类型转换失败，跳过")
                print("   原始数据: [\(objsArray[startIndex]), \(objsArray[startIndex + 1]), \(objsArray[startIndex + 2]), \(objsArray[startIndex + 3]), \(objsArray[startIndex + 4]), \(objsArray[startIndex + 5]), \(objsArray[startIndex + 6]), \(objsArray[startIndex + 7])]")
                continue
            }
            
            print("📍 [Parse AI] 目标 \(i) 原始数据: app=\(app), id=\(id), type=\(typeValue), conf=\(confidence), x=\(x), y=\(y), w=\(width), h=\(height)")
            
            // P2-R1的坐标数据已经是归一化的（0-1之间），不需要再转换
            print("📐 [Parse AI] 目标 \(i) 归一化坐标: x=\(String(format: "%.3f", x)), y=\(String(format: "%.3f", y)), w=\(String(format: "%.3f", width)), h=\(String(format: "%.3f", height)))")
            
            // 转换目标类型
            let targetType = AIDetectionTargetType(rawValue: typeValue) ?? .unknown
            
            // 修复置信度处理：P2-R1可能发送0-100的百分比值，需要标准化为0-1
            let normalizedConfidence = confidence > 1.0 ? confidence / 100.0 : confidence
            print("📊 [Parse AI] 目标 \(i) 置信度处理: 原始=\(confidence), 标准化=\(String(format: "%.3f", normalizedConfidence))")
            
            // 检查是否为高亮目标 (可以根据实际需求调整)
            let isHighlighted = normalizedConfidence > 0.9 || targetType == .face
            
            let target = AIDetectionTarget(
                id: id,
                x: x,
                y: y,
                width: width,
                height: height,
                type: targetType,
                confidence: normalizedConfidence,
                isHighlighted: isHighlighted
            )
            
            print("✅ [Parse AI] 成功创建目标 \(i): \(targetType.displayName), 置信度=\(String(format: "%.2f", normalizedConfidence))")
            targets.append(target)
        }
        
        print("🎯 [Parse AI] 解析完成，共创建 \(targets.count) 个有效目标")
        return targets
    }

    private func forwardAsGenericMessage(_ message: [String: Any]) {
        // Convert P2-R1 message to generic WebSocket message format
        if let data = try? JSONSerialization.data(withJSONObject: message) {
            let wsMessage = WebSocketMessage(
                type: .statusUpdate,
                data: data,
                timestamp: Date()
            )

            DispatchQueue.main.async {
                self.messageSubject.send(wsMessage)
            }
        }
    }
}

import Foundation
import WebRTC
import Combine

/// P2-R1 WebRTC Streamer 客户端
/// 基于 P2-R1 Web 界面的实际实现
@MainActor
class WebRTCStreamerClient: NSObject, ObservableObject {

    // MARK: - Singleton
    static let shared = WebRTCStreamerClient()

    // MARK: - Published Properties
    @Published var connectionState: RTCPeerConnectionState = .new
    @Published var iceConnectionState: RTCIceConnectionState = .new
    @Published var isConnected: Bool = false
    @Published var remoteVideoTrack: RTCVideoTrack?

    // MARK: - Private Properties
    private var peerConnection: RTCPeerConnection?
    private var peerConnectionFactory: RTCPeerConnectionFactory
    private var config: RTCConfiguration
    private var constraints: RTCMediaConstraints
    private var currentCameraIP: String?

    // MARK: - Initialization
    
    override init() {
        // 初始化 WebRTC 工厂
        RTCInitializeSSL()
        peerConnectionFactory = RTCPeerConnectionFactory()
        
        // 配置 RTCConfiguration
        config = RTCConfiguration()
        
        // 设置媒体约束
        constraints = RTCMediaConstraints(
            mandatoryConstraints: [
                "OfferToReceiveAudio": "true",
                "OfferToReceiveVideo": "true"
            ],
            optionalConstraints: nil
        )
        
        super.init()
        setupConfiguration()
    }
    
    deinit {
        print("[WebRTCStreamer] 💀 deinit called")

        // 同步清理，避免在deinit中使用异步操作
        if let pc = peerConnection {
            print("[WebRTCStreamer] 💀 Closing peer connection in deinit")
            pc.close()
        }

        // 清理资源（不能在deinit中修改@Published属性）
        peerConnection = nil
        // remoteVideoTrack 是 @Published 属性，不能在 deinit 中修改

        RTCCleanupSSL()
        print("[WebRTCStreamer] 💀 deinit completed")
    }
    
    // MARK: - Public Methods
    
    /// 连接到 P2-R1 WebRTC Streamer 服务
    func connect(to cameraIP: String, port: Int = 8000) async throws {
        print("[WebRTCStreamer] Connecting to \(cameraIP):\(port)")

        // 检查是否已经连接到相同的地址
        if isConnected && currentCameraIP == cameraIP {
            print("[WebRTCStreamer] Already connected to \(cameraIP), skipping connection")
            return
        }

        // 先断开现有连接
        if peerConnection != nil {
            disconnect()
            // 等待断开完成
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        }

        currentCameraIP = cameraIP

        do {
            // 0. 检查 WebRTC Streamer 服务是否可用（不阻塞连接）
            await checkWebRTCStreamerService(cameraIP: cameraIP, port: port)

            // 1. 获取 ICE 服务器配置（容错处理）
            await setupICEServers(cameraIP: cameraIP, port: port)

            // 2. 创建 PeerConnection
            try createPeerConnection()

            // 3. 请求 RTSP 流（尝试多种方式）
            try await requestRTSPStream(cameraIP: cameraIP, port: port)

            print("[WebRTCStreamer] Connection process completed")
        } catch {
            print("[WebRTCStreamer] ❌ Connection failed: \(error)")
            // 清理失败的连接
            disconnect()
            throw error
        }
    }
    
    /// 断开连接
    func disconnect() {
        print("[WebRTCStreamer] 🔌 Starting disconnect process...")

        // 先清理视频轨道引用，避免内存访问错误
        if remoteVideoTrack != nil {
            print("[WebRTCStreamer] 🔌 Removing video track")
            // 在主线程上安全地清理视频轨道
            DispatchQueue.main.async {
                // 通知UI层视频轨道已移除
                self.remoteVideoTrack = nil
            }
        }

        // 关闭 PeerConnection
        if let pc = peerConnection {
            print("[WebRTCStreamer] 🔌 Closing peer connection")
            pc.close()
        }

        // 清理资源
        peerConnection = nil
        currentCameraIP = nil

        // 更新状态
        isConnected = false
        connectionState = .closed
        iceConnectionState = .closed

        print("[WebRTCStreamer] 🔌 ✅ Disconnect completed")
    }
    
    // MARK: - Private Methods
    
    private func setupConfiguration() {
        // 使用 Google 的公共 STUN 服务器作为默认
        let stunServer = RTCIceServer(urlStrings: ["stun:stun.l.google.com:19302"])
        config.iceServers = [stunServer]
        config.sdpSemantics = .unifiedPlan
        config.continualGatheringPolicy = .gatherContinually
    }

    private func checkWebRTCStreamerService(cameraIP: String, port: Int) async {
        // 检查多个可能的WebRTC Streamer端点
        let possibleEndpoints = [
            "http://\(cameraIP):\(port)/api/getIceServers",
            "http://\(cameraIP):\(port)/api/version",
            "http://\(cameraIP):\(port)/",
            "http://\(cameraIP):8000/api/getIceServers",  // 强制尝试8000端口
            "http://\(cameraIP):8000/",
            "http://\(cameraIP):8443/api/getIceServers", // 尝试HTTPS端口
            "http://\(cameraIP):8443/"
        ]

        print("[WebRTCStreamer] 🔍 Checking WebRTC Streamer service availability...")

        for endpoint in possibleEndpoints {
            guard let url = URL(string: endpoint) else { continue }

            do {
                var request = URLRequest(url: url)
                request.timeoutInterval = 3.0

                print("[WebRTCStreamer] Testing endpoint: \(endpoint)")
                let (data, response) = try await URLSession.shared.data(for: request)

                if let httpResponse = response as? HTTPURLResponse {
                    print("[WebRTCStreamer] Endpoint \(endpoint) returned status: \(httpResponse.statusCode)")

                    if httpResponse.statusCode == 200 {
                        let responseBody = String(data: data, encoding: .utf8) ?? ""
                        print("[WebRTCStreamer] ✅ WebRTC Streamer service found at: \(endpoint)")
                        print("[WebRTCStreamer] Response: \(responseBody.prefix(200))")
                        return
                    }
                }
            } catch {
                print("[WebRTCStreamer] Endpoint \(endpoint) failed: \(error)")
            }
        }

        print("[WebRTCStreamer] ❌ WebRTC Streamer service not found on any endpoint")
        print("[WebRTCStreamer] 💡 This P2-R1 device may not have WebRTC Streamer enabled")

        // 不抛出错误，继续尝试连接，可能设备有WebRTC但端点不同
        print("[WebRTCStreamer] ⚠️ Continuing without service check...")
    }
    
    private func updateICEServers(_ iceServers: [[String: Any]]) {
        var rtcIceServers: [RTCIceServer] = []
        
        for serverConfig in iceServers {
            if let urls = serverConfig["urls"] as? [String] {
                let iceServer = RTCIceServer(urlStrings: urls)
                rtcIceServers.append(iceServer)
            } else if let url = serverConfig["url"] as? String {
                let iceServer = RTCIceServer(urlStrings: [url])
                rtcIceServers.append(iceServer)
            }
        }
        
        if !rtcIceServers.isEmpty {
            config.iceServers = rtcIceServers
            print("[WebRTCStreamer] Updated ICE servers: \(rtcIceServers.count) servers")
        }
    }
    
    private func setupICEServers(cameraIP: String, port: Int) async {
        // P2-R1 WebRTC Streamer 使用 HTTP API 获取 ICE 服务器
        let iceServersURL = "http://\(cameraIP):\(port)/api/getIceServers"
        guard let url = URL(string: iceServersURL) else {
            print("[WebRTCStreamer] ⚠️ Invalid ICE servers URL, using default configuration")
            // 使用默认的 ICE 服务器配置而不是抛出错误
            return
        }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                print("[WebRTCStreamer] ⚠️ ICE servers API not available, using default configuration")
                return
            }

            // 解析 ICE 服务器配置
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let iceServers = json["iceServers"] as? [[String: Any]] {
                print("[WebRTCStreamer] Got ICE servers: \(iceServers)")
                updateICEServers(iceServers)
            }

            print("[WebRTCStreamer] ICE servers configured from \(iceServersURL)")
        } catch {
            print("[WebRTCStreamer] ⚠️ Failed to get ICE servers: \(error), using default configuration")
            // 继续使用默认配置，不抛出错误
        }
    }
    
    private func createPeerConnection() throws {
        guard peerConnection == nil else { return }
        
        peerConnection = peerConnectionFactory.peerConnection(
            with: config,
            constraints: RTCMediaConstraints(mandatoryConstraints: nil, optionalConstraints: nil),
            delegate: self
        )
        
        guard peerConnection != nil else {
            throw WebRTCStreamerError.failedToCreatePeerConnection
        }
        
        print("[WebRTCStreamer] PeerConnection created")
    }
    
    private func requestRTSPStream(cameraIP: String, port: Int) async throws {
        // P2-R1 使用 HTTP API 调用，遵循原始 WebRTC Streamer 协议
        guard let peerConnection = peerConnection else {
            throw WebRTCStreamerError.failedToCreatePeerConnection
        }

        // 生成 peer ID（模拟 P2-R1 Web 界面的方式）
        let peerID = UUID().uuidString.lowercased()

        // 创建 Offer
        let offer = try await peerConnection.offer(for: constraints)
        try await peerConnection.setLocalDescription(offer)

        // 尝试多种WebRTC连接方式
        print("[WebRTCStreamer] 🔄 Attempting WebRTC connection...")

        // 方式1: 尝试标准的WebRTC Streamer API
        let success = await tryWebRTCStreamerAPI(cameraIP: cameraIP, port: port, peerID: peerID, offer: offer)

        if !success {
            // 方式2: 尝试直接WebRTC连接（如果P2-R1支持）
            print("[WebRTCStreamer] 🔄 Trying alternative WebRTC connection methods...")
            try await tryAlternativeWebRTCConnection(cameraIP: cameraIP, offer: offer)
        }
    }

    private func tryWebRTCStreamerAPI(cameraIP: String, port: Int, peerID: String, offer: RTCSessionDescription) async -> Bool {
        // 基于P2-R1源码分析，使用精确的WebRTC Streamer协议
        // P2-R1使用固定的RTSP URL: "rtsp://127.0.0.1/live_h264"
        let rtspURL = "rtsp://127.0.0.1/live_h264"
        let options = "timeout=1"  // P2-R1使用的选项

        // 尝试多个可能的端口（按P2-R1优先级）
        let possiblePorts = [8000, 8443, port]  // 8000是HTTP，8443是HTTPS

        for testPort in possiblePorts {
            // 构建完全符合P2-R1 WebRTC Streamer协议的URL
            let callURL = "http://\(cameraIP):\(testPort)/api/call?peerid=\(peerID)&url=\(rtspURL.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")&options=\(options.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")"

                guard let url = URL(string: callURL) else { continue }

            do {
                var request = URLRequest(url: url)
                request.httpMethod = "POST"
                request.setValue("application/json", forHTTPHeaderField: "Content-Type")
                request.timeoutInterval = 5.0

                // 按照P2-R1 WebRTC Streamer的格式发送offer
                request.httpBody = try JSONSerialization.data(withJSONObject: [
                    "type": offer.type.rawValue,
                    "sdp": offer.sdp
                ])

                print("[WebRTCStreamer] 🔄 Calling WebRTC Streamer API: \(callURL)")

                let (data, response) = try await URLSession.shared.data(for: request)

                if let httpResponse = response as? HTTPURLResponse {
                    print("[WebRTCStreamer] API response status: \(httpResponse.statusCode)")

                    if httpResponse.statusCode == 200 {
                        // 解析WebRTC Streamer的响应
                        if let responseDict = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                            print("[WebRTCStreamer] ✅ WebRTC Streamer response: \(responseDict)")

                            // 处理answer（按照WebRTC Streamer协议）
                            if let type = responseDict["type"] as? String,
                               let sdp = responseDict["sdp"] as? String,
                               type == "answer" {
                                let answer = RTCSessionDescription(type: .answer, sdp: sdp)
                                try await peerConnection?.setRemoteDescription(answer)
                                print("[WebRTCStreamer] ✅ Remote description set successfully")

                                // 按照P2-R1的方式获取ICE candidates
                                await getICECandidates(cameraIP: cameraIP, port: testPort, peerID: peerID)
                                return true
                            }
                        }
                    } else {
                        print("[WebRTCStreamer] WebRTC Streamer API failed with status: \(httpResponse.statusCode)")
                        if let responseString = String(data: data, encoding: .utf8) {
                            print("[WebRTCStreamer] Error response: \(responseString)")
                        }
                    }
                }
            } catch {
                print("[WebRTCStreamer] WebRTC Streamer API request failed: \(error)")
            }
        }

        return false
    }

    private func getICECandidates(cameraIP: String, port: Int, peerID: String) async {
        // 按照P2-R1 WebRTC Streamer协议获取ICE candidates
        let iceCandidatesURL = "http://\(cameraIP):\(port)/api/getIceCandidate?peerid=\(peerID)"

        guard let url = URL(string: iceCandidatesURL) else { return }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200 {

                if let candidatesArray = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
                    print("[WebRTCStreamer] Received \(candidatesArray.count) ICE candidates")

                    for candidateDict in candidatesArray {
                        if let candidate = candidateDict["candidate"] as? String,
                           let sdpMLineIndex = candidateDict["sdpMLineIndex"] as? Int32,
                           let sdpMid = candidateDict["sdpMid"] as? String {

                            let iceCandidate = RTCIceCandidate(
                                sdp: candidate,
                                sdpMLineIndex: sdpMLineIndex,
                                sdpMid: sdpMid
                            )

                            try await peerConnection?.add(iceCandidate)
                            print("[WebRTCStreamer] Added ICE candidate: \(candidate.prefix(50))...")
                        }
                    }
                }
            }
        } catch {
            print("[WebRTCStreamer] Failed to get ICE candidates: \(error)")
        }
    }

    private func tryAlternativeWebRTCConnection(cameraIP: String, offer: RTCSessionDescription) async throws {
        print("[WebRTCStreamer] ❌ WebRTC Streamer API not available")
        print("[WebRTCStreamer] 💡 This P2-R1 device may not support WebRTC streaming")
        print("[WebRTCStreamer] 💡 Consider using MJPEG stream as alternative")

        // 抛出错误，表示WebRTC连接失败
        throw WebRTCStreamerError.connectionTimeout
    }
}

// MARK: - RTCPeerConnectionDelegate

extension WebRTCStreamerClient: RTCPeerConnectionDelegate {

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange stateChanged: RTCSignalingState) {
        print("[WebRTCStreamer] Signaling state changed: \(stateChanged)")
    }

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didAdd stream: RTCMediaStream) {
        print("[WebRTCStreamer] Remote stream added")
        print("[WebRTCStreamer] Stream has \(stream.videoTracks.count) video tracks")
        print("[WebRTCStreamer] Stream has \(stream.audioTracks.count) audio tracks")

        if let videoTrack = stream.videoTracks.first {
            print("[WebRTCStreamer] Video track details: \(videoTrack)")
            print("[WebRTCStreamer] Video track enabled: \(videoTrack.isEnabled)")
            Task { @MainActor in
                self.remoteVideoTrack = videoTrack
                print("[WebRTCStreamer] Remote video track received and assigned to @Published property")
                print("[WebRTCStreamer] Current remoteVideoTrack: \(String(describing: self.remoteVideoTrack))")
            }
        } else {
            print("[WebRTCStreamer] ⚠️ No video tracks found in remote stream")
        }
    }

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didRemove stream: RTCMediaStream) {
        print("[WebRTCStreamer] Remote stream removed")
        Task { @MainActor in
            self.remoteVideoTrack = nil
        }
    }

    nonisolated func peerConnectionShouldNegotiate(_ peerConnection: RTCPeerConnection) {
        print("[WebRTCStreamer] Should negotiate")
    }

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceConnectionState) {
        print("[WebRTCStreamer] ICE connection state changed: \(newState)")

        Task { @MainActor in
            self.iceConnectionState = newState

            switch newState {
            case .connected, .completed:
                print("[WebRTCStreamer] Setting isConnected = true (ICE state: \(newState))")
                self.isConnected = true
            case .disconnected:
                print("[WebRTCStreamer] ICE disconnected - attempting to maintain connection")
                // 不立即设置为false，给重连机会
            case .failed, .closed:
                print("[WebRTCStreamer] Setting isConnected = false (ICE state: \(newState))")
                self.isConnected = false
                // 连接失败时自动清理
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    if self.iceConnectionState == .failed || self.iceConnectionState == .closed {
                        print("[WebRTCStreamer] Auto-disconnecting due to failed ICE state")
                        self.disconnect()
                    }
                }
            default:
                print("[WebRTCStreamer] ICE state \(newState) - no isConnected change")
                break
            }
        }
    }

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCIceGatheringState) {
        print("[WebRTCStreamer] ICE gathering state changed: \(newState)")
    }

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didGenerate candidate: RTCIceCandidate) {
        print("[WebRTCStreamer] ICE candidate generated")
        // P2-R1 的实现中，ICE candidates 通过 HTTP API 自动处理
    }

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didRemove candidates: [RTCIceCandidate]) {
        print("[WebRTCStreamer] ICE candidates removed")
    }

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didOpen dataChannel: RTCDataChannel) {
        print("[WebRTCStreamer] Data channel opened")
    }

    nonisolated func peerConnection(_ peerConnection: RTCPeerConnection, didChange newState: RTCPeerConnectionState) {
        print("[WebRTCStreamer] Peer connection state changed: \(newState)")
        Task { @MainActor in
            self.connectionState = newState
        }
    }
}

// MARK: - Error Types

enum WebRTCStreamerError: Error, LocalizedError {
    case invalidURL
    case noWebSocketConnection
    case failedToCreatePeerConnection
    case connectionTimeout
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .noWebSocketConnection:
            return "No WebSocket connection"
        case .failedToCreatePeerConnection:
            return "Failed to create peer connection"
        case .connectionTimeout:
            return "Connection timeout"
        }
    }
}

//
//  LocalStorageManager.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Security
import os.log

// MARK: - UserDefaults Wrapper

@propertyWrapper
struct UserDefault<T> {
    let key: String
    let defaultValue: T
    let userDefaults: UserDefaults
    
    init(key: String, defaultValue: T, userDefaults: UserDefaults = .standard) {
        self.key = key
        self.defaultValue = defaultValue
        self.userDefaults = userDefaults
    }
    
    var wrappedValue: T {
        get {
            return userDefaults.object(forKey: key) as? T ?? defaultValue
        }
        set {
            userDefaults.set(newValue, forKey: key)
        }
    }
}

// MARK: - App Settings

class AppSettings: ObservableObject {
    static let shared = AppSettings()
    
    // MARK: - Camera Settings
    @UserDefault(key: "default_camera_ip", defaultValue: "")
    var defaultCameraIP: String
    
    @UserDefault(key: "default_camera_port", defaultValue: 80)
    var defaultCameraPort: Int
    
    @UserDefault(key: "auto_connect_camera", defaultValue: true)
    var autoConnectCamera: Bool
    
    @UserDefault(key: "camera_connection_timeout", defaultValue: 30.0)
    var cameraConnectionTimeout: TimeInterval
    
    // MARK: - Stream Settings
    @UserDefault(key: "default_stream_quality", defaultValue: "high")
    var defaultStreamQuality: String
    
    @UserDefault(key: "enable_hardware_acceleration", defaultValue: true)
    var enableHardwareAcceleration: Bool

    @UserDefault(key: "enable_adaptive_quality", defaultValue: true)
    var enableAdaptiveQuality: Bool

    @UserDefault(key: "buffer_size", defaultValue: 64)
    var bufferSize: Int

    @UserDefault(key: "stream_buffer_size", defaultValue: 1024)
    var streamBufferSize: Int
    
    // MARK: - Storage Settings
    @UserDefault(key: "auto_download", defaultValue: false)
    var autoDownload: Bool

    @UserDefault(key: "save_to_photos", defaultValue: true)
    var saveToPhotos: Bool

    @UserDefault(key: "allow_cellular_download", defaultValue: false)
    var allowCellularDownload: Bool

    @UserDefault(key: "auto_download_files", defaultValue: false)
    var autoDownloadFiles: Bool

    @UserDefault(key: "keep_files_after_download", defaultValue: false)
    var keepFilesAfterDownload: Bool

    @UserDefault(key: "max_storage_days", defaultValue: 30)
    var maxStorageDays: Int

    @UserDefault(key: "download_over_cellular", defaultValue: false)
    var downloadOverCellular: Bool
    
    // MARK: - Connection Settings
    @UserDefault(key: "use_https", defaultValue: false)
    var useHTTPS: Bool

    @UserDefault(key: "verify_ssl_certificate", defaultValue: true)
    var verifySSLCertificate: Bool

    @UserDefault(key: "max_retry_attempts", defaultValue: 3)
    var maxRetryAttempts: Int

    @UserDefault(key: "retry_delay", defaultValue: 2.0)
    var retryDelay: TimeInterval

    // MARK: - UI Settings
    @UserDefault(key: "app_theme", defaultValue: "system")
    var appTheme: String

    @UserDefault(key: "enable_haptic_feedback", defaultValue: true)
    var enableHapticFeedback: Bool

    @UserDefault(key: "show_status_bar", defaultValue: true)
    var showStatusBar: Bool

    @UserDefault(key: "show_advanced_controls", defaultValue: false)
    var showAdvancedControls: Bool
    
    // MARK: - Notification Settings
    @UserDefault(key: "enable_notifications", defaultValue: true)
    var enableNotifications: Bool

    @UserDefault(key: "notify_recording_start", defaultValue: true)
    var notifyRecordingStart: Bool

    @UserDefault(key: "notify_recording_stop", defaultValue: true)
    var notifyRecordingStop: Bool

    @UserDefault(key: "notify_connection_lost", defaultValue: true)
    var notifyConnectionLost: Bool

    @UserDefault(key: "notify_connection_restored", defaultValue: true)
    var notifyConnectionRestored: Bool

    @UserDefault(key: "notify_low_battery", defaultValue: true)
    var notifyLowBattery: Bool

    @UserDefault(key: "notify_storage_full", defaultValue: true)
    var notifyStorageFull: Bool

    // MARK: - Privacy Settings
    @UserDefault(key: "enable_analytics", defaultValue: false)
    var enableAnalytics: Bool

    @UserDefault(key: "enable_crash_reports", defaultValue: true)
    var enableCrashReports: Bool

    @UserDefault(key: "require_authentication", defaultValue: false)
    var requireAuthentication: Bool
    
    // MARK: - Debug Settings
    @UserDefault(key: "enable_debug_logging", defaultValue: false)
    var enableDebugLogging: Bool
    
    @UserDefault(key: "log_network_requests", defaultValue: false)
    var logNetworkRequests: Bool
    
    // MARK: - Last Used Values
    @UserDefault(key: "last_connected_device_id", defaultValue: "")
    var lastConnectedDeviceId: String
    
    @UserDefault(key: "last_used_preset", defaultValue: "")
    var lastUsedPreset: String
    
    @UserDefault(key: "app_launch_count", defaultValue: 0)
    var appLaunchCount: Int
    
    @UserDefault(key: "last_app_version", defaultValue: "")
    var lastAppVersion: String
    
    private init() {}
    
    // MARK: - Methods
    
    func reset() {
        let domain = Bundle.main.bundleIdentifier!
        UserDefaults.standard.removePersistentDomain(forName: domain)
        UserDefaults.standard.synchronize()
    }
    
    func incrementLaunchCount() {
        appLaunchCount += 1
    }
    
    func updateAppVersion() {
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            lastAppVersion = version
        }
    }
}

// MARK: - Keychain Manager

class KeychainManager {
    static let shared = KeychainManager()
    
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "Keychain")
    
    private init() {}
    
    // MARK: - Keychain Operations
    
    func save(key: String, data: Data) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        // Delete any existing item
        SecItemDelete(query as CFDictionary)
        
        // Add the new item
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecSuccess {
            logger.debug("Successfully saved item to keychain: \(key)")
            return true
        } else {
            logger.error("Failed to save item to keychain: \(key), status: \(status)")
            return false
        }
    }
    
    func load(key: String) -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess {
            logger.debug("Successfully loaded item from keychain: \(key)")
            return result as? Data
        } else {
            logger.debug("Failed to load item from keychain: \(key), status: \(status)")
            return nil
        }
    }
    
    func delete(key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        if status == errSecSuccess || status == errSecItemNotFound {
            logger.debug("Successfully deleted item from keychain: \(key)")
            return true
        } else {
            logger.error("Failed to delete item from keychain: \(key), status: \(status)")
            return false
        }
    }
    
    // MARK: - Convenience Methods
    
    func save(key: String, string: String) -> Bool {
        guard let data = string.data(using: .utf8) else {
            logger.error("Failed to convert string to data for key: \(key)")
            return false
        }
        return save(key: key, data: data)
    }
    
    func loadString(key: String) -> String? {
        guard let data = load(key: key) else {
            return nil
        }
        return String(data: data, encoding: .utf8)
    }
    
    func save<T: Codable>(key: String, object: T) -> Bool {
        do {
            let data = try JSONEncoder().encode(object)
            return save(key: key, data: data)
        } catch {
            logger.error("Failed to encode object for key: \(key), error: \(error.localizedDescription)")
            return false
        }
    }
    
    func load<T: Codable>(key: String, type: T.Type) -> T? {
        guard let data = load(key: key) else {
            return nil
        }
        
        do {
            return try JSONDecoder().decode(type, from: data)
        } catch {
            logger.error("Failed to decode object for key: \(key), error: \(error.localizedDescription)")
            return nil
        }
    }
}

// MARK: - Secure Storage

class SecureStorage {
    static let shared = SecureStorage()
    
    private let keychain = KeychainManager.shared
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "SecureStorage")
    
    private init() {}
    
    // MARK: - API Keys
    
    var apiKey: String? {
        get {
            return keychain.loadString(key: "api_key")
        }
        set {
            if let newValue = newValue {
                _ = keychain.save(key: "api_key", string: newValue)
            } else {
                _ = keychain.delete(key: "api_key")
            }
        }
    }
    
    // MARK: - Camera Credentials
    
    func saveCameraCredentials(deviceId: String, username: String, password: String) -> Bool {
        let credentials = CameraCredentials(username: username, password: password)
        return keychain.save(key: "camera_credentials_\(deviceId)", object: credentials)
    }
    
    func loadCameraCredentials(deviceId: String) -> CameraCredentials? {
        return keychain.load(key: "camera_credentials_\(deviceId)", type: CameraCredentials.self)
    }
    
    func deleteCameraCredentials(deviceId: String) -> Bool {
        return keychain.delete(key: "camera_credentials_\(deviceId)")
    }
    
    // MARK: - WiFi Credentials
    
    func saveWiFiCredentials(ssid: String, password: String) -> Bool {
        let credentials = WiFiCredentials(ssid: ssid, password: password)
        return keychain.save(key: "wifi_credentials_\(ssid)", object: credentials)
    }
    
    func loadWiFiCredentials(ssid: String) -> WiFiCredentials? {
        return keychain.load(key: "wifi_credentials_\(ssid)", type: WiFiCredentials.self)
    }
    
    func deleteWiFiCredentials(ssid: String) -> Bool {
        return keychain.delete(key: "wifi_credentials_\(ssid)")
    }
    
    // MARK: - Encryption Keys
    
    func generateEncryptionKey() -> Data? {
        var keyData = Data(count: 32) // 256-bit key
        let result = keyData.withUnsafeMutableBytes {
            SecRandomCopyBytes(kSecRandomDefault, 32, $0.baseAddress!)
        }
        
        if result == errSecSuccess {
            return keyData
        } else {
            logger.error("Failed to generate encryption key")
            return nil
        }
    }
    
    func saveEncryptionKey(_ key: Data, identifier: String) -> Bool {
        return keychain.save(key: "encryption_key_\(identifier)", data: key)
    }
    
    func loadEncryptionKey(identifier: String) -> Data? {
        return keychain.load(key: "encryption_key_\(identifier)")
    }
    
    func deleteEncryptionKey(identifier: String) -> Bool {
        return keychain.delete(key: "encryption_key_\(identifier)")
    }
    
    // MARK: - Cleanup
    
    func clearAllSecureData() {
        logger.info("Clearing all secure data")
        
        // Clear API keys
        apiKey = nil
        
        // This is a simplified cleanup - in a real app you might want to
        // enumerate and delete all items with your app's prefix
    }
}

// MARK: - Credential Models

struct CameraCredentials: Codable {
    let username: String
    let password: String
    let createdAt: Date
    
    init(username: String, password: String) {
        self.username = username
        self.password = password
        self.createdAt = Date()
    }
}

struct WiFiCredentials: Codable {
    let ssid: String
    let password: String
    let createdAt: Date
    
    init(ssid: String, password: String) {
        self.ssid = ssid
        self.password = password
        self.createdAt = Date()
    }
}

// MARK: - File Storage Manager

class FileStorageManager {
    static let shared = FileStorageManager()
    
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "FileStorage")
    private let fileManager = FileManager.default
    
    private init() {
        createDirectoriesIfNeeded()
    }
    
    // MARK: - Directory URLs
    
    var documentsURL: URL {
        return fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    }
    
    var cacheURL: URL {
        return fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
    }
    
    var downloadsURL: URL {
        return documentsURL.appendingPathComponent("Downloads")
    }
    
    var thumbnailsURL: URL {
        return cacheURL.appendingPathComponent("Thumbnails")
    }
    
    var logsURL: URL {
        return documentsURL.appendingPathComponent("Logs")
    }
    
    // MARK: - Directory Management
    
    private func createDirectoriesIfNeeded() {
        let directories = [downloadsURL, thumbnailsURL, logsURL]
        
        for directory in directories {
            do {
                try fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
            } catch {
                logger.error("Failed to create directory: \(directory.path), error: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - File Operations
    
    func saveFile(data: Data, to url: URL) throws {
        try data.write(to: url)
        logger.debug("File saved to: \(url.path)")
    }
    
    func loadFile(from url: URL) throws -> Data {
        let data = try Data(contentsOf: url)
        logger.debug("File loaded from: \(url.path)")
        return data
    }
    
    func deleteFile(at url: URL) throws {
        try fileManager.removeItem(at: url)
        logger.debug("File deleted: \(url.path)")
    }
    
    func fileExists(at url: URL) -> Bool {
        return fileManager.fileExists(atPath: url.path)
    }
    
    func fileSize(at url: URL) -> Int64? {
        do {
            let attributes = try fileManager.attributesOfItem(atPath: url.path)
            return attributes[.size] as? Int64
        } catch {
            logger.error("Failed to get file size: \(url.path), error: \(error.localizedDescription)")
            return nil
        }
    }
    
    // MARK: - Storage Info
    
    func getStorageInfo() -> (total: Int64, available: Int64, used: Int64)? {
        do {
            let attributes = try fileManager.attributesOfFileSystem(forPath: documentsURL.path)
            
            guard let total = attributes[.systemSize] as? Int64,
                  let available = attributes[.systemFreeSize] as? Int64 else {
                return nil
            }
            
            let used = total - available
            return (total: total, available: available, used: used)
        } catch {
            logger.error("Failed to get storage info: \(error.localizedDescription)")
            return nil
        }
    }
    
    // MARK: - Cleanup
    
    func cleanupOldFiles(olderThan days: Int) {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        
        cleanupDirectory(downloadsURL, olderThan: cutoffDate)
        cleanupDirectory(thumbnailsURL, olderThan: cutoffDate)
        cleanupDirectory(logsURL, olderThan: cutoffDate)
    }
    
    private func cleanupDirectory(_ directory: URL, olderThan cutoffDate: Date) {
        do {
            let files = try fileManager.contentsOfDirectory(at: directory, includingPropertiesForKeys: [.creationDateKey])
            
            for file in files {
                let attributes = try fileManager.attributesOfItem(atPath: file.path)
                if let creationDate = attributes[.creationDate] as? Date,
                   creationDate < cutoffDate {
                    try fileManager.removeItem(at: file)
                    logger.debug("Cleaned up old file: \(file.path)")
                }
            }
        } catch {
            logger.error("Failed to cleanup directory: \(directory.path), error: \(error.localizedDescription)")
        }
    }
}
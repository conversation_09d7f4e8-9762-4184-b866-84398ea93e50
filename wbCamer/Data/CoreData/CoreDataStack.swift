//
//  CoreDataStack.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import CoreData
import os.log

// MARK: - Core Data Stack

class CoreDataStack: ObservableObject {
    static let shared = CoreDataStack()
    
    private let logger = Logger(subsystem: "com.wbcamer.app", category: "CoreData")
    
    // MARK: - Core Data Stack
    
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "wbCamerDataModel")
        
        // Configure store description
        let storeDescription = container.persistentStoreDescriptions.first
        storeDescription?.shouldInferMappingModelAutomatically = true
        storeDescription?.shouldMigrateStoreAutomatically = true
        
        // Enable persistent history tracking
        storeDescription?.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        storeDescription?.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        
        container.loadPersistentStores { [weak self] _, error in
            if let error = error as NSError? {
                self?.logger.error("Core Data error: \(error.localizedDescription)")
                fatalError("Core Data error: \(error), \(error.userInfo)")
            } else {
                self?.logger.info("Core Data stack loaded successfully")
            }
        }
        
        // Configure view context
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        return container
    }()
    
    var viewContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    // MARK: - Background Context
    
    func newBackgroundContext() -> NSManagedObjectContext {
        let context = persistentContainer.newBackgroundContext()
        context.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        return context
    }
    
    // MARK: - Save Context
    
    func save() {
        let context = persistentContainer.viewContext
        
        if context.hasChanges {
            do {
                try context.save()
                logger.debug("Core Data context saved successfully")
            } catch {
                logger.error("Failed to save Core Data context: \(error.localizedDescription)")
            }
        }
    }
    
    func saveContext(_ context: NSManagedObjectContext) {
        if context.hasChanges {
            do {
                try context.save()
                logger.debug("Core Data background context saved successfully")
            } catch {
                logger.error("Failed to save Core Data background context: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Batch Operations
    
    func performBackgroundTask<T>(_ block: @escaping (NSManagedObjectContext) throws -> T) async throws -> T {
        return try await withCheckedThrowingContinuation { continuation in
            let context = newBackgroundContext()
            
            context.perform {
                do {
                    let result = try block(context)
                    
                    if context.hasChanges {
                        try context.save()
                    }
                    
                    continuation.resume(returning: result)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Batch Delete
    
    func batchDelete<T: NSManagedObject>(entity: T.Type, predicate: NSPredicate? = nil) throws {
        let request = NSBatchDeleteRequest(fetchRequest: T.fetchRequest())
        request.fetchRequest.predicate = predicate
        request.resultType = .resultTypeObjectIDs
        
        let result = try viewContext.execute(request) as? NSBatchDeleteResult
        let objectIDArray = result?.result as? [NSManagedObjectID]
        let changes = [NSDeletedObjectsKey: objectIDArray ?? []]
        
        NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [viewContext])
        
        logger.info("Batch deleted \(objectIDArray?.count ?? 0) \(String(describing: entity)) objects")
    }
    
    // MARK: - Data Migration
    
    func migrateStoreIfNeeded() {
        guard let storeURL = persistentContainer.persistentStoreDescriptions.first?.url else {
            logger.error("Could not find store URL for migration")
            return
        }
        
        do {
            let metadata = try NSPersistentStoreCoordinator.metadataForPersistentStore(ofType: NSSQLiteStoreType, at: storeURL, options: nil)
            
            if !persistentContainer.managedObjectModel.isConfiguration(withName: nil, compatibleWithStoreMetadata: metadata) {
                logger.info("Core Data model is not compatible, migration needed")
                // Migration will be handled automatically by the persistent container
            } else {
                logger.info("Core Data model is compatible, no migration needed")
            }
        } catch {
            logger.error("Failed to check Core Data compatibility: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Cleanup
    
    func cleanup() {
        // Clean up old data based on retention policies
        cleanupOldFiles()
        cleanupOldLogs()
    }
    
    private func cleanupOldFiles() {
        // TODO: Implement file cleanup when CoreData entities are available
        logger.info("File cleanup skipped - CoreData entities not available")
    }
    
    private func cleanupOldLogs() {
        // TODO: Implement log cleanup when CoreData entities are available
        logger.info("Log cleanup skipped - CoreData entities not available")
    }
}

// MARK: - Entity Extensions

extension NSManagedObject {
    static var entityName: String {
        return String(describing: self)
    }
    
    static func fetchRequest<T: NSManagedObject>() -> NSFetchRequest<T> {
        return NSFetchRequest<T>(entityName: entityName)
    }
}

// MARK: - Fetch Request Helpers

extension CoreDataStack {
    func fetch<T: NSManagedObject>(
        _ type: T.Type,
        predicate: NSPredicate? = nil,
        sortDescriptors: [NSSortDescriptor] = [],
        limit: Int? = nil
    ) throws -> [T] {
        let request: NSFetchRequest<T> = T.fetchRequest()
        request.predicate = predicate
        request.sortDescriptors = sortDescriptors
        
        if let limit = limit {
            request.fetchLimit = limit
        }
        
        return try viewContext.fetch(request)
    }
    
    func fetchFirst<T: NSManagedObject>(
        _ type: T.Type,
        predicate: NSPredicate? = nil,
        sortDescriptors: [NSSortDescriptor] = []
    ) throws -> T? {
        return try fetch(type, predicate: predicate, sortDescriptors: sortDescriptors, limit: 1).first
    }
    
    func count<T: NSManagedObject>(
        _ type: T.Type,
        predicate: NSPredicate? = nil
    ) throws -> Int {
        let request: NSFetchRequest<T> = T.fetchRequest()
        request.predicate = predicate
        
        return try viewContext.count(for: request)
    }
}

// MARK: - Repository Protocol

protocol Repository {
    associatedtype Entity: NSManagedObject
    
    func save(_ entity: Entity) throws
    func delete(_ entity: Entity) throws
    func fetch(predicate: NSPredicate?, sortDescriptors: [NSSortDescriptor]) throws -> [Entity]
    func fetchFirst(predicate: NSPredicate?) throws -> Entity?
    func count(predicate: NSPredicate?) throws -> Int
}

// MARK: - Base Repository

class BaseRepository<T: NSManagedObject>: Repository {
    typealias Entity = T
    
    let coreDataStack: CoreDataStack
    
    init(coreDataStack: CoreDataStack = .shared) {
        self.coreDataStack = coreDataStack
    }
    
    func save(_ entity: T) throws {
        coreDataStack.save()
    }
    
    func delete(_ entity: T) throws {
        coreDataStack.viewContext.delete(entity)
        coreDataStack.save()
    }
    
    func fetch(predicate: NSPredicate? = nil, sortDescriptors: [NSSortDescriptor] = []) throws -> [T] {
        return try coreDataStack.fetch(T.self, predicate: predicate, sortDescriptors: sortDescriptors)
    }
    
    func fetchFirst(predicate: NSPredicate? = nil) throws -> T? {
        return try coreDataStack.fetchFirst(T.self, predicate: predicate)
    }
    
    func count(predicate: NSPredicate? = nil) throws -> Int {
        return try coreDataStack.count(T.self, predicate: predicate)
    }
}
//
//  EagleServiceDiscovery.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import Foundation
import Network
import Combine

#if canImport(Darwin)
import Darwin
#elseif canImport(Glibc)
import Glibc
#endif

// MARK: - 服务实例数据模型
struct EagleServiceInstance: Identifiable, Equatable {
    let id = UUID()
    let instanceName: String
    let type: String
    let domain: String
    var hostName: String?
    var ipAddress: String?
    var port: UInt16?
    var isResolved: Bool = false
    
    static func == (lhs: EagleServiceInstance, rhs: EagleServiceInstance) -> Bool {
        return lhs.instanceName == rhs.instanceName && lhs.type == rhs.type && lhs.domain == rhs.domain
    }
}

// MARK: - 服务发现管理器
class EagleServiceDiscovery: ObservableObject {
    static let shared = EagleServiceDiscovery()

    @Published var discoveredServices: [EagleServiceInstance] = []
    @Published var discoveredIPs: [String] = []
    @Published var isScanning: Bool = false
    @Published var lastScanTime: Date?

    private var browser: NWBrowser?
    private var autoScanTimer: Timer?
    private let serviceType = "_eagle._tcp"
    private let domain = "local"
    private let scanTimeout: TimeInterval = 10.0
    private let autoScanInterval: TimeInterval = 10.0

    private var scanTimeoutTimer: Timer?
    private var hasPerformedInitialScan = false

    private init() {
        // 私有初始化，确保单例
        print("🔧 EagleServiceDiscovery singleton initialized")

        // 初始化完成后执行一次扫描
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.performInitialScanIfNeeded()
        }
    }
    
    deinit {
        stopScanning()
    }
    
    // MARK: - 初始扫描
    private func performInitialScanIfNeeded() {
        guard !hasPerformedInitialScan else {
            print("⚠️ Initial scan already performed, skipping")
            return
        }

        hasPerformedInitialScan = true
        print("Starting initial device discovery scan...")
        startManualScan()
    }

    private func startInitialScan() {
        print("Starting initial device discovery scan...")
        startManualScan()
    }

    // MARK: - 手动扫描
    func startManualScan() {
        print("🔍 EagleServiceDiscovery.startManualScan() called")
        guard !isScanning else {
            print("⚠️ Scan already in progress, ignoring new scan request")
            return
        }
        
        DispatchQueue.main.async { [weak self] in
            self?.isScanning = true
            self?.discoveredServices.removeAll()
            self?.discoveredIPs.removeAll()
            self?.lastScanTime = Date()
        }
        
        startBrowsing()
        
        // 设置扫描超时
        scanTimeoutTimer?.invalidate()
        scanTimeoutTimer = Timer.scheduledTimer(withTimeInterval: scanTimeout, repeats: false) { [weak self] _ in
            self?.stopScanning()
        }
    }
    
    func stopScanning() {
        DispatchQueue.main.async { [weak self] in
            self?.isScanning = false
        }
        
        stopBrowsing()
        
        scanTimeoutTimer?.invalidate()
        scanTimeoutTimer = nil
    }
    
    private func stopBrowsing() {
        browser?.cancel()
        browser = nil
    }
    
    // MARK: - 自动扫描 (已禁用)
    // 注意：自动扫描功能已被禁用，现在只支持手动扫描

    @available(*, deprecated, message: "自动扫描已禁用，请使用 startManualScan() 进行手动扫描")
    func setupAutoScan() {
        // 不再设置自动扫描定时器
        print("自动扫描功能已禁用")
    }

    @available(*, deprecated, message: "自动扫描已禁用")
    func stopAutoScan() {
        // 清理可能存在的定时器
        autoScanTimer?.invalidate()
        autoScanTimer = nil
    }

    @available(*, deprecated, message: "自动扫描已禁用，请使用 startManualScan() 进行手动扫描")
    func enableAutoScan(_ enabled: Bool) {
        print("自动扫描功能已禁用，请使用 startManualScan() 进行手动扫描")
    }
    
    // MARK: - 私有方法
    private func startBrowsing() {
        stopBrowsing()
        
        let parameters = NWParameters()
        parameters.includePeerToPeer = false
        parameters.allowLocalEndpointReuse = true
        
        // 使用正确的服务类型格式
        let serviceType = NWBrowser.Descriptor.bonjour(type: "_eagle._tcp", domain: "local.")
        browser = NWBrowser(for: serviceType, using: parameters)
        
        browser?.stateUpdateHandler = { [weak self] state in
            DispatchQueue.main.async {
                switch state {
                case .ready:
                    print("Browser ready")
                    self?.isScanning = true
                case .failed(let error):
                    print("Browser failed: \(error)")
                    self?.isScanning = false
                case .cancelled:
                    print("Browser cancelled")
                    self?.isScanning = false
                default:
                    break
                }
            }
        }
        
        browser?.browseResultsChangedHandler = { [weak self] results, changes in
            self?.handleBrowseResults(results: results, changes: changes)
        }
        
        browser?.start(queue: .global(qos: .userInitiated))
    }
    
    private func handleBrowseResults(results: Set<NWBrowser.Result>, changes: Set<NWBrowser.Result.Change>) {
        for change in changes {
            switch change {
            case .added(let result):
                handleServiceAdded(result: result)
            case .removed(let result):
                handleServiceRemoved(result: result)
            case .changed(old: _, new: let result, flags: _):
                handleServiceAdded(result: result)
            case .identical:
                break
            @unknown default:
                break
            }
        }
    }
    
    private func handleServiceAdded(result: NWBrowser.Result) {
        print("🔍 Service discovered: \(result.endpoint)")

        switch result.endpoint {
        case .service(name: let name, type: let type, domain: let domain, interface: _):
            print("📡 Found Eagle service: \(name) of type \(type) in domain \(domain)")

            let serviceInstance = EagleServiceInstance(
                instanceName: name,
                type: type,
                domain: domain
            )

            DispatchQueue.main.async { [weak self] in
                if let self = self, !self.discoveredServices.contains(serviceInstance) {
                    self.discoveredServices.append(serviceInstance)
                }
            }

            // 使用正确的DNS-SD解析流程获取IP地址
            resolveServiceWithDNSSD(result: result, serviceInstance: serviceInstance)
        default:
            return
        }
    }
    
    private func handleServiceRemoved(result: NWBrowser.Result) {
        switch result.endpoint {
        case .service(name: let name, type: let type, domain: let domain, interface: _):
            DispatchQueue.main.async { [weak self] in
                self?.discoveredServices.removeAll { service in
                    service.instanceName == name && service.type == type && service.domain == domain
                }
            }
        default:
            return
        }
    }
    
    /// 使用DNS-SD协议正确解析服务IP地址
    /// 参考文档中的三阶段解析流程：browse -> resolve -> getAddrInfo
    private func resolveServiceWithDNSSD(result: NWBrowser.Result, serviceInstance: EagleServiceInstance) {
        // 对于mDNS服务，我们需要正确处理服务端点
        switch result.endpoint {
        case .service(name: let name, type: let type, domain: let domain, interface: _):
            print("Attempting to resolve service: \(name) of type \(type) in domain \(domain)")

            // 尝试通过连接来触发端点解析，这会将服务名解析为实际的主机名和IP
            let connection = NWConnection(to: result.endpoint, using: .tcp)

            connection.stateUpdateHandler = { [weak self] state in
                switch state {
                case .ready:
                    // 连接成功，端点已被解析为实际的主机名/IP
                    if let remoteEndpoint = connection.currentPath?.remoteEndpoint {
                        print("Successfully resolved endpoint for \(name)")
                        self?.extractIPFromResolvedEndpoint(remoteEndpoint, serviceInstance: serviceInstance)
                    }
                    connection.cancel()

                case .waiting(_):
                    // 在等待状态，端点可能已经被部分解析
                    if let remoteEndpoint = connection.currentPath?.remoteEndpoint {
                        print("Endpoint partially resolved for \(name) (waiting state)")
                        self?.extractIPFromResolvedEndpoint(remoteEndpoint, serviceInstance: serviceInstance)
                    }
                    connection.cancel()

                case .failed(let error):
                    print("Connection failed for \(name): \(error)")
                    // 即使连接失败，端点可能已经被解析
                    if let remoteEndpoint = connection.currentPath?.remoteEndpoint {
                        print("Endpoint resolved despite connection failure for \(name)")
                        self?.extractIPFromResolvedEndpoint(remoteEndpoint, serviceInstance: serviceInstance)
                    } else {
                        print("No endpoint information available for \(name)")
                    }
                    connection.cancel()

                default:
                    break
                }
            }

            // 启动连接以触发DNS解析
            connection.start(queue: .global(qos: .utility))

            // 设置超时
            DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 5.0) {
                connection.cancel()
            }

        default:
            // 如果不是服务端点，直接尝试提取IP
            extractIPFromResolvedEndpoint(result.endpoint, serviceInstance: serviceInstance)
        }
    }

    /// 第二阶段：解析服务端点获取主机名和端口
    private func resolveServiceEndpoint(result: NWBrowser.Result, serviceInstance: EagleServiceInstance) {
        // 使用NWBrowser.Descriptor.resolve来正确解析服务
        switch result.endpoint {
        case .service(name: let name, type: let type, domain: let domain, interface: _):
            print("Resolving service: \(name) of type \(type) in domain \(domain)")

            // 创建服务解析器
            let parameters = NWParameters()
            parameters.includePeerToPeer = false

            // 使用NWBrowser来解析特定服务实例
            let serviceDescriptor = NWBrowser.Descriptor.bonjour(type: type, domain: domain)
            let resolver = NWBrowser(for: serviceDescriptor, using: parameters)

            resolver.browseResultsChangedHandler = { [weak self] results, changes in
                for result in results {
                    if case .service(name: let resultName, type: _, domain: _, interface: _) = result.endpoint,
                       resultName == name {
                        // 找到匹配的服务，尝试解析其端点
                        self?.resolveSpecificService(result: result, serviceInstance: serviceInstance)
                        resolver.cancel()
                        return
                    }
                }
            }

            resolver.stateUpdateHandler = { state in
                switch state {
                case .failed(let error):
                    print("Service resolver failed: \(error)")
                case .cancelled:
                    print("Service resolver cancelled")
                default:
                    break
                }
            }

            resolver.start(queue: .global(qos: .utility))

            // 设置超时
            DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 5.0) {
                resolver.cancel()
            }

        default:
            break
        }
    }

    /// 解析特定服务实例
    private func resolveSpecificService(result: NWBrowser.Result, serviceInstance: EagleServiceInstance) {
        // 尝试建立连接以获取解析后的端点信息
        let connection = NWConnection(to: result.endpoint, using: .tcp)

        connection.stateUpdateHandler = { [weak self] state in
            switch state {
            case .ready:
                // 连接成功，提取端点信息
                if let remoteEndpoint = connection.currentPath?.remoteEndpoint {
                    self?.extractIPFromResolvedEndpoint(remoteEndpoint, serviceInstance: serviceInstance)
                }
                connection.cancel()
            case .failed(let error):
                print("Failed to resolve service endpoint: \(error)")
                connection.cancel()
            default:
                break
            }
        }

        connection.start(queue: .global(qos: .utility))

        // 设置连接超时
        DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 3.0) {
            connection.cancel()
        }
    }
    
    /// 从已解析的端点提取IP地址
    private func extractIPFromResolvedEndpoint(_ endpoint: NWEndpoint, serviceInstance: EagleServiceInstance) {
        print("Extracting IP from endpoint for \(serviceInstance.instanceName): \(endpoint)")

        var ipAddress: String?
        var port: UInt16?
        var hostName: String?

        switch endpoint {
        case .hostPort(let host, let portValue):
            port = portValue.rawValue
            print("Found hostPort endpoint with port \(portValue.rawValue)")

            switch host {
            case .ipv4(let ipv4):
                // 提取纯IP地址，去除接口信息（如%en0）
                let fullDescription = ipv4.debugDescription
                if let percentIndex = fullDescription.firstIndex(of: "%") {
                    ipAddress = String(fullDescription[..<percentIndex])
                } else {
                    ipAddress = fullDescription
                }
                print("✅ Successfully resolved IPv4 for \(serviceInstance.instanceName): \(ipAddress!):\(portValue.rawValue)")

            case .ipv6(let ipv6):
                // 提取纯IP地址，去除接口信息（如%en0）
                let fullDescription = ipv6.debugDescription
                if let percentIndex = fullDescription.firstIndex(of: "%") {
                    ipAddress = String(fullDescription[..<percentIndex])
                } else {
                    ipAddress = fullDescription
                }
                print("✅ Successfully resolved IPv6 for \(serviceInstance.instanceName): \(ipAddress!):\(portValue.rawValue)")

            case .name(let hostname, _):
                hostName = hostname
                print("Got hostname for \(serviceInstance.instanceName): \(hostname):\(portValue.rawValue)")

                // 检查主机名是否是有效的格式
                if hostname.hasSuffix(".local.") || hostname.hasSuffix(".local") {
                    // 这是一个有效的mDNS主机名，尝试解析
                    resolveHostnameToIP(hostname, port: port, serviceInstance: serviceInstance)
                    return
                } else {
                    print("⚠️ Invalid hostname format: \(hostname)")
                    return
                }

            @unknown default:
                print("❌ Unknown host type for \(serviceInstance.instanceName)")
                break
            }

        case .service(name: let name, type: let type, domain: let domain, interface: _):
            print("⚠️ Still got service endpoint (not resolved): \(name).\(type).\(domain)")
            // 服务端点表示还没有被解析为实际的主机名
            return

        default:
            print("❌ Unexpected endpoint type for \(serviceInstance.instanceName): \(endpoint)")
            break
        }

        // 如果直接获得了IP地址，在主线程更新服务实例
        if let ip = ipAddress {
            DispatchQueue.main.async { [weak self] in
                self?.updateServiceInstance(serviceInstance, hostName: hostName, ipAddress: ip, port: port)
            }
        } else {
            print("❌ No IP address extracted for \(serviceInstance.instanceName)")
        }
    }

    /// 直接从端点提取信息（第三阶段：IP地址解析）
    private func extractEndpointInfoDirectly(from endpoint: NWEndpoint, serviceInstance: EagleServiceInstance) {
        var ipAddress: String?
        var port: UInt16?
        var hostName: String?

        switch endpoint {
        case .hostPort(let host, let portValue):
            port = portValue.rawValue

            switch host {
            case .ipv4(let ipv4):
                // 直接获得IPv4地址
                ipAddress = ipv4.debugDescription
            case .ipv6(let ipv6):
                // 直接获得IPv6地址
                ipAddress = ipv6.debugDescription
            case .name(let hostname, _):
                // 获得主机名，需要进一步解析为IP
                hostName = hostname
                resolveHostnameToIP(hostname, port: port, serviceInstance: serviceInstance)
                return
            @unknown default:
                break
            }

        case .service(name: let name, type: let type, domain: let domain, interface: _):
            // 对于服务端点，我们需要进一步解析来获取实际的主机名和IP
            // 服务名不等于主机名，需要通过DNS-SD解析获取
            print("Service endpoint detected: \(name).\(type).\(domain)")
            print("Service endpoints require further resolution - this should be handled in resolveSpecificService")
            return

        default:
            break
        }

        // 如果直接获得了IP地址，更新服务实例
        if let ip = ipAddress {
            updateServiceInstance(serviceInstance, hostName: hostName, ipAddress: ip, port: port)
        }
    }
    
    /// 第三阶段：将主机名解析为IP地址（使用DNS查询而非TCP连接）
    private func resolveHostnameToIP(_ hostname: String, port: UInt16?, serviceInstance: EagleServiceInstance) {
        print("🔍 Attempting DNS resolution for hostname: \(hostname)")

        // 使用系统的DNS解析功能，而不是建立TCP连接
        DispatchQueue.global(qos: .utility).async { [weak self] in
            var hints = addrinfo()
            hints.ai_family = AF_INET // 只查询IPv4地址
            hints.ai_socktype = SOCK_STREAM

            var result: UnsafeMutablePointer<addrinfo>?
            let status = getaddrinfo(hostname, nil, &hints, &result)

            defer {
                if let result = result {
                    freeaddrinfo(result)
                }
            }

            if status == 0, let addrInfo = result {
                var current: UnsafeMutablePointer<addrinfo>? = addrInfo

                while let currentAddr = current {
                    if currentAddr.pointee.ai_family == AF_INET {
                        let addr = currentAddr.pointee.ai_addr.withMemoryRebound(to: sockaddr_in.self, capacity: 1) { $0 }
                        let ipAddress = String(cString: inet_ntoa(addr.pointee.sin_addr))

                        print("✅ Successfully resolved \(hostname) to \(ipAddress)")

                        // 成功解析到IP地址
                        DispatchQueue.main.async {
                            self?.updateServiceInstance(serviceInstance, hostName: hostname, ipAddress: ipAddress, port: port)
                        }
                        return
                    }
                    current = currentAddr.pointee.ai_next
                }

                print("❌ No IPv4 address found for \(hostname)")
            } else {
                // DNS解析失败，打印详细错误信息
                let errorString = String(cString: gai_strerror(status))
                print("❌ DNS resolution failed for \(hostname): \(errorString) (status: \(status))")
            }
        }
    }

    /// 更新服务实例信息
    private func updateServiceInstance(_ serviceInstance: EagleServiceInstance, hostName: String?, ipAddress: String, port: UInt16?) {
        // 更新服务实例
        if let index = self.discoveredServices.firstIndex(where: { $0.id == serviceInstance.id }) {
            self.discoveredServices[index] = EagleServiceInstance(
                instanceName: serviceInstance.instanceName,
                type: serviceInstance.type,
                domain: serviceInstance.domain,
                hostName: hostName,
                ipAddress: ipAddress,
                port: port,
                isResolved: true
            )
        }

        // 添加到IP列表（避免重复）
        if !self.discoveredIPs.contains(ipAddress) {
            self.discoveredIPs.append(ipAddress)
            print("Successfully resolved Eagle device: \(serviceInstance.instanceName) -> \(ipAddress)")
        }
    }
    
    // MARK: - 公共接口
    func getDiscoveredIPs() -> [String] {
        return discoveredIPs
    }
    
    func clearResults() {
        DispatchQueue.main.async { [weak self] in
            self?.discoveredServices.removeAll()
            self?.discoveredIPs.removeAll()
        }
    }
}

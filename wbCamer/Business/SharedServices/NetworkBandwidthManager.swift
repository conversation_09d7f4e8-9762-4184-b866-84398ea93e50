//
//  NetworkBandwidthManager.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import Network
import Combine

// MARK: - Network Bandwidth Manager

/// 网络带宽管理器 - 管理多摄像机场景下的网络带宽分配
@MainActor
class NetworkBandwidthManager: ObservableObject {
    static let shared = NetworkBandwidthManager()
    
    // MARK: - Published Properties
    @Published var totalBandwidth: Double = 100.0  // 总带宽 (Mbps)
    @Published var availableBandwidth: Double = 100.0  // 可用带宽 (Mbps)
    @Published var usedBandwidth: Double = 0.0  // 已使用带宽 (Mbps)
    @Published var networkQuality: NetworkQuality = .excellent
    @Published var isMonitoring: Bool = false
    
    // 带宽分配记录
    @Published var bandwidthAllocations: [UUID: BandwidthAllocation] = [:]
    
    // 网络状态
    @Published var connectionType: NWInterface.InterfaceType = .wifi
    @Published var networkLatency: TimeInterval = 0.0
    @Published var packetLoss: Double = 0.0
    
    // MARK: - Private Properties
    private let pathMonitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "network.bandwidth.monitor")
    private var bandwidthTestTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // 带宽测试配置
    private let bandwidthTestInterval: TimeInterval = 10.0
    private let minBandwidthPerStream: Double = 0.5  // 最小带宽 (Mbps)
    private let maxBandwidthPerStream: Double = 15.0  // 最大带宽 (Mbps)
    
    // MARK: - Initialization
    
    private init() {
        startNetworkMonitoring()
        startBandwidthTesting()
    }
    
    deinit {
        stopNetworkMonitoring()
        stopBandwidthTesting()
    }
    
    // MARK: - Bandwidth Allocation
    
    func allocateBandwidth(for cardId: UUID, quality: VideoQuality) -> Double? {
        let requiredBandwidth = quality.bandwidth
        
        guard availableBandwidth >= requiredBandwidth else {
            print("[NetworkBandwidthManager] ❌ Insufficient bandwidth for card \(cardId.uuidString.prefix(8))")
            return optimizeBandwidthDistribution(for: cardId, required: requiredBandwidth)
        }
        
        let allocation = BandwidthAllocation(
            cardId: cardId,
            allocatedBandwidth: requiredBandwidth,
            quality: quality,
            allocatedAt: Date()
        )
        
        bandwidthAllocations[cardId] = allocation
        updateBandwidthUsage()
        
        print("[NetworkBandwidthManager] ✅ Bandwidth allocated for card \(cardId.uuidString.prefix(8)): \(requiredBandwidth) Mbps")
        return requiredBandwidth
    }
    
    func deallocateBandwidth(for cardId: UUID) {
        if let allocation = bandwidthAllocations.removeValue(forKey: cardId) {
            updateBandwidthUsage()
            print("[NetworkBandwidthManager] 🗑️ Bandwidth deallocated for card \(cardId.uuidString.prefix(8)): \(allocation.allocatedBandwidth) Mbps")
        }
    }
    
    func updateBandwidthAllocation(for cardId: UUID, newQuality: VideoQuality) -> Bool {
        let newRequiredBandwidth = newQuality.bandwidth
        
        guard var allocation = bandwidthAllocations[cardId] else {
            return false
        }
        
        let currentBandwidth = allocation.allocatedBandwidth
        let bandwidthDifference = newRequiredBandwidth - currentBandwidth
        
        if bandwidthDifference > 0 && availableBandwidth < bandwidthDifference {
            print("[NetworkBandwidthManager] ⚠️ Cannot update bandwidth allocation for card \(cardId.uuidString.prefix(8))")
            return false
        }
        
        allocation.allocatedBandwidth = newRequiredBandwidth
        allocation.quality = newQuality
        bandwidthAllocations[cardId] = allocation
        updateBandwidthUsage()
        
        print("[NetworkBandwidthManager] 🔄 Bandwidth allocation updated for card \(cardId.uuidString.prefix(8)): \(newRequiredBandwidth) Mbps")
        return true
    }
    
    // MARK: - Bandwidth Optimization
    
    private func optimizeBandwidthDistribution(for cardId: UUID, required: Double) -> Double? {
        print("[NetworkBandwidthManager] 🔧 Optimizing bandwidth distribution")
        
        // 获取按优先级排序的分配（最新的优先级最高）
        let sortedAllocations = bandwidthAllocations.values.sorted { allocation1, allocation2 in
            allocation1.allocatedAt < allocation2.allocatedAt
        }
        
        var freedBandwidth: Double = 0
        
        // 尝试降低其他流的质量来释放带宽
        for allocation in sortedAllocations {
            if let lowerQuality = allocation.quality.lowerQuality() {
                let currentBandwidth = allocation.allocatedBandwidth
                let newBandwidth = lowerQuality.bandwidth
                let savedBandwidth = currentBandwidth - newBandwidth
                
                if updateBandwidthAllocation(for: allocation.cardId, newQuality: lowerQuality) {
                    freedBandwidth += savedBandwidth
                    print("[NetworkBandwidthManager] ⬇️ Reduced quality for card \(allocation.cardId.uuidString.prefix(8)) to free \(savedBandwidth) Mbps")
                    
                    if freedBandwidth >= required {
                        break
                    }
                }
            }
        }
        
        // 检查是否有足够的带宽
        if availableBandwidth >= required {
            return allocateBandwidth(for: cardId, quality: VideoQuality.qualityForBandwidth(required))
        }
        
        return nil
    }
    
    func optimizeAllAllocations() {
        print("[NetworkBandwidthManager] 🔧 Optimizing all bandwidth allocations")
        
        // 根据网络质量调整分配策略
        switch networkQuality {
        case .poor:
            degradeAllToMinimumQuality()
        case .fair:
            balanceQualityForFairNetwork()
        case .good, .excellent:
            upgradeQualityWherePossible()
        }
    }
    
    private func degradeAllToMinimumQuality() {
        for (cardId, allocation) in bandwidthAllocations {
            if allocation.quality != .low {
                _ = updateBandwidthAllocation(for: cardId, newQuality: .low)
            }
        }
    }
    
    private func balanceQualityForFairNetwork() {
        let targetQuality: VideoQuality = .medium
        
        for (cardId, allocation) in bandwidthAllocations {
            if allocation.quality.bandwidth > targetQuality.bandwidth {
                _ = updateBandwidthAllocation(for: cardId, newQuality: targetQuality)
            }
        }
    }
    
    private func upgradeQualityWherePossible() {
        let sortedAllocations = bandwidthAllocations.values.sorted { allocation1, allocation2 in
            allocation1.allocatedAt < allocation2.allocatedAt
        }
        
        for allocation in sortedAllocations {
            if let higherQuality = allocation.quality.higherQuality() {
                _ = updateBandwidthAllocation(for: allocation.cardId, newQuality: higherQuality)
            }
        }
    }
    
    // MARK: - Network Monitoring
    
    private func startNetworkMonitoring() {
        pathMonitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor [weak self] in
                self?.handleNetworkPathUpdate(path)
            }
        }
        
        pathMonitor.start(queue: monitorQueue)
        isMonitoring = true
    }
    
    private func stopNetworkMonitoring() {
        pathMonitor.cancel()
        isMonitoring = false
    }
    
    private func handleNetworkPathUpdate(_ path: NWPath) {
        // 更新连接类型
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
            totalBandwidth = 100.0  // WiFi通常有更高带宽
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
            totalBandwidth = 50.0   // 蜂窝网络带宽较低
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .wiredEthernet
            totalBandwidth = 1000.0 // 有线网络带宽最高
        }
        
        // 更新网络状态
        if path.status == .satisfied {
            updateNetworkQuality()
        } else {
            networkQuality = .poor
        }
        
        updateBandwidthUsage()
    }
    
    // MARK: - Bandwidth Testing
    
    private func startBandwidthTesting() {
        bandwidthTestTimer = Timer.scheduledTimer(withTimeInterval: bandwidthTestInterval, repeats: true) { [weak self] _ in
            Task { @MainActor [weak self] in
                await self?.performBandwidthTest()
            }
        }
    }
    
    private func stopBandwidthTesting() {
        bandwidthTestTimer?.invalidate()
        bandwidthTestTimer = nil
    }
    
    private func performBandwidthTest() async {
        // 简化的带宽测试（实际实现应该进行真实的网络测试）
        let testLatency = await measureNetworkLatency()
        let testPacketLoss = await measurePacketLoss()
        
        networkLatency = testLatency
        packetLoss = testPacketLoss
        
        updateNetworkQuality()
        
        // 根据测试结果调整带宽分配
        if shouldOptimizeBandwidth() {
            optimizeAllAllocations()
        }
    }
    
    private func measureNetworkLatency() async -> TimeInterval {
        // 模拟延迟测试（实际实现应该ping测试服务器）
        let baseLatency: TimeInterval
        
        switch connectionType {
        case .wifi:
            baseLatency = 0.020  // 20ms
        case .cellular:
            baseLatency = 0.050  // 50ms
        case .wiredEthernet:
            baseLatency = 0.005  // 5ms
        default:
            baseLatency = 0.100  // 100ms
        }
        
        // 添加一些随机变化
        let variation = Double.random(in: -0.010...0.010)
        return baseLatency + variation
    }
    
    private func measurePacketLoss() async -> Double {
        // 模拟丢包率测试
        let basePacketLoss: Double
        
        switch connectionType {
        case .wifi:
            basePacketLoss = 0.001  // 0.1%
        case .cellular:
            basePacketLoss = 0.005  // 0.5%
        case .wiredEthernet:
            basePacketLoss = 0.0001 // 0.01%
        default:
            basePacketLoss = 0.01   // 1%
        }
        
        return basePacketLoss + Double.random(in: 0...0.002)
    }
    
    private func updateNetworkQuality() {
        // 根据延迟和丢包率确定网络质量
        if networkLatency < 0.030 && packetLoss < 0.001 {
            networkQuality = .excellent
        } else if networkLatency < 0.060 && packetLoss < 0.005 {
            networkQuality = .good
        } else if networkLatency < 0.100 && packetLoss < 0.01 {
            networkQuality = .fair
        } else {
            networkQuality = .poor
        }
    }
    
    private func shouldOptimizeBandwidth() -> Bool {
        return networkQuality == .poor || 
               usedBandwidth > totalBandwidth * 0.9 ||
               packetLoss > 0.01
    }
    
    // MARK: - Helper Methods
    
    private func updateBandwidthUsage() {
        usedBandwidth = bandwidthAllocations.values.reduce(0) { $0 + $1.allocatedBandwidth }
        availableBandwidth = max(0, totalBandwidth - usedBandwidth)
    }
    
    func getBandwidthAllocation(for cardId: UUID) -> BandwidthAllocation? {
        return bandwidthAllocations[cardId]
    }
    
    func getAllBandwidthAllocations() -> [BandwidthAllocation] {
        return Array(bandwidthAllocations.values)
    }
    
    func getRecommendedQuality() -> VideoQuality {
        switch networkQuality {
        case .excellent:
            return .ultra
        case .good:
            return .high
        case .fair:
            return .medium
        case .poor:
            return .low
        }
    }
}

// MARK: - Supporting Types

/// 带宽分配记录
struct BandwidthAllocation: Identifiable {
    let id = UUID()
    let cardId: UUID
    var allocatedBandwidth: Double
    var quality: VideoQuality
    let allocatedAt: Date
}

/// 网络质量等级
enum NetworkQuality: String, CaseIterable {
    case poor = "poor"
    case fair = "fair"
    case good = "good"
    case excellent = "excellent"
    
    var displayName: String {
        switch self {
        case .poor: return "差"
        case .fair: return "一般"
        case .good: return "良好"
        case .excellent: return "优秀"
        }
    }
    
    var color: String {
        switch self {
        case .poor: return "red"
        case .fair: return "orange"
        case .good: return "yellow"
        case .excellent: return "green"
        }
    }
}

// MARK: - VideoQuality Extensions

extension VideoQuality {
    static func qualityForBandwidth(_ bandwidth: Double) -> VideoQuality {
        if bandwidth >= 8.0 {
            return .ultra
        } else if bandwidth >= 4.0 {
            return .high
        } else if bandwidth >= 1.5 {
            return .medium
        } else {
            return .low
        }
    }
}

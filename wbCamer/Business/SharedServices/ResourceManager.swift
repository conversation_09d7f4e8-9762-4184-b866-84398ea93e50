//
//  ResourceManager.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import Combine

// MARK: - Resource Manager

/// 系统资源管理器 - 管理多摄像机场景下的系统资源分配
@MainActor
class ResourceManager: ObservableObject {
    static let shared = ResourceManager()
    
    // MARK: - Published Properties
    @Published var activeCameraCount: Int = 0
    @Published var totalBandwidthUsage: Double = 0
    @Published var systemMemoryUsage: Double = 0
    @Published var systemCPUUsage: Double = 0
    @Published var batteryLevel: Double = 100.0
    @Published var thermalState: ThermalState = .nominal
    
    // 资源限制
    @Published var maxConcurrentStreams: Int = 4
    @Published var maxBandwidthUsage: Double = 50.0  // Mbps
    @Published var maxMemoryUsage: Double = 0.8      // 80%
    @Published var maxCPUUsage: Double = 0.7         // 70%
    
    // 资源分配记录
    @Published var resourceAllocations: [UUID: ResourceAllocation] = [:]
    
    // MARK: - Private Properties
    private var systemMonitorTimer: Timer?
    private let monitoringInterval: TimeInterval = 2.0
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    private init() {
        startSystemMonitoring()
        setupThermalStateMonitoring()
    }
    
    deinit {
        stopSystemMonitoring()
    }
    
    // MARK: - Resource Allocation
    
    func allocateResourcesForCard(_ cardId: UUID, quality: VideoQuality) -> ResourceAllocation? {
        let requiredResources = calculateRequiredResources(for: quality)
        
        // 检查是否有足够的资源
        guard canAllocateResources(requiredResources) else {
            print("[ResourceManager] ❌ Cannot allocate resources for card \(cardId.uuidString.prefix(8))")
            return nil
        }
        
        let allocation = ResourceAllocation(
            cardId: cardId,
            memoryMB: requiredResources.memoryMB,
            cpuPercentage: requiredResources.cpuPercentage,
            bandwidthMbps: requiredResources.bandwidthMbps,
            quality: quality,
            allocatedAt: Date()
        )
        
        resourceAllocations[cardId] = allocation
        updateSystemUsage()
        
        print("[ResourceManager] ✅ Resources allocated for card \(cardId.uuidString.prefix(8)): \(allocation)")
        return allocation
    }
    
    func deallocateResourcesForCard(_ cardId: UUID) {
        if let allocation = resourceAllocations.removeValue(forKey: cardId) {
            updateSystemUsage()
            print("[ResourceManager] 🗑️ Resources deallocated for card \(cardId.uuidString.prefix(8))")
        }
    }
    
    func updateResourceAllocation(_ cardId: UUID, newQuality: VideoQuality) -> Bool {
        guard var allocation = resourceAllocations[cardId] else {
            return false
        }
        
        let newRequiredResources = calculateRequiredResources(for: newQuality)
        
        // 临时移除当前分配以检查新分配是否可行
        resourceAllocations.removeValue(forKey: cardId)
        
        if canAllocateResources(newRequiredResources) {
            allocation.memoryMB = newRequiredResources.memoryMB
            allocation.cpuPercentage = newRequiredResources.cpuPercentage
            allocation.bandwidthMbps = newRequiredResources.bandwidthMbps
            allocation.quality = newQuality
            
            resourceAllocations[cardId] = allocation
            updateSystemUsage()
            
            print("[ResourceManager] 🔄 Resource allocation updated for card \(cardId.uuidString.prefix(8))")
            return true
        } else {
            // 恢复原分配
            resourceAllocations[cardId] = allocation
            print("[ResourceManager] ⚠️ Cannot update resource allocation for card \(cardId.uuidString.prefix(8))")
            return false
        }
    }
    
    // MARK: - Resource Optimization
    
    func optimizeResourceDistribution() {
        print("[ResourceManager] 🔧 Optimizing resource distribution")
        
        // 获取按优先级排序的分配
        let sortedAllocations = resourceAllocations.values.sorted { allocation1, allocation2 in
            // 按质量和分配时间排序
            if allocation1.quality != allocation2.quality {
                return allocation1.quality.bandwidth > allocation2.quality.bandwidth
            }
            return allocation1.allocatedAt < allocation2.allocatedAt
        }
        
        // 检查是否需要降级
        if systemMemoryUsage > maxMemoryUsage || systemCPUUsage > maxCPUUsage || totalBandwidthUsage > maxBandwidthUsage {
            degradeQualityForLowPriorityCards(sortedAllocations)
        }
        
        // 检查是否可以升级
        else if systemMemoryUsage < maxMemoryUsage * 0.6 && systemCPUUsage < maxCPUUsage * 0.6 {
            upgradeQualityForCards(sortedAllocations)
        }
    }
    
    private func degradeQualityForLowPriorityCards(_ allocations: [ResourceAllocation]) {
        for allocation in allocations.reversed() { // 从低优先级开始
            if let lowerQuality = allocation.quality.lowerQuality() {
                if updateResourceAllocation(allocation.cardId, newQuality: lowerQuality) {
                    print("[ResourceManager] ⬇️ Degraded quality for card \(allocation.cardId.uuidString.prefix(8)) to \(lowerQuality)")
                    
                    // 检查是否已经满足资源要求
                    if systemMemoryUsage <= maxMemoryUsage && systemCPUUsage <= maxCPUUsage && totalBandwidthUsage <= maxBandwidthUsage {
                        break
                    }
                }
            }
        }
    }
    
    private func upgradeQualityForCards(_ allocations: [ResourceAllocation]) {
        for allocation in allocations { // 从高优先级开始
            if let higherQuality = allocation.quality.higherQuality() {
                if updateResourceAllocation(allocation.cardId, newQuality: higherQuality) {
                    print("[ResourceManager] ⬆️ Upgraded quality for card \(allocation.cardId.uuidString.prefix(8)) to \(higherQuality)")
                }
            }
        }
    }
    
    // MARK: - Resource Calculation
    
    private func calculateRequiredResources(for quality: VideoQuality) -> RequiredResources {
        switch quality {
        case .low:
            return RequiredResources(memoryMB: 50, cpuPercentage: 0.05, bandwidthMbps: 0.5)
        case .medium:
            return RequiredResources(memoryMB: 100, cpuPercentage: 0.10, bandwidthMbps: 2.0)
        case .high:
            return RequiredResources(memoryMB: 200, cpuPercentage: 0.15, bandwidthMbps: 5.0)
        case .ultra:
            return RequiredResources(memoryMB: 400, cpuPercentage: 0.25, bandwidthMbps: 10.0)
        }
    }
    
    private func canAllocateResources(_ required: RequiredResources) -> Bool {
        let projectedMemory = systemMemoryUsage + (required.memoryMB / 1024.0) // 转换为GB
        let projectedCPU = systemCPUUsage + required.cpuPercentage
        let projectedBandwidth = totalBandwidthUsage + required.bandwidthMbps
        
        return projectedMemory <= maxMemoryUsage &&
               projectedCPU <= maxCPUUsage &&
               projectedBandwidth <= maxBandwidthUsage &&
               activeCameraCount < maxConcurrentStreams
    }
    
    // MARK: - System Monitoring
    
    private func startSystemMonitoring() {
        systemMonitorTimer = Timer.scheduledTimer(withTimeInterval: monitoringInterval, repeats: true) { [weak self] _ in
            Task { @MainActor [weak self] in
                self?.updateSystemMetrics()
            }
        }
    }
    
    private func stopSystemMonitoring() {
        systemMonitorTimer?.invalidate()
        systemMonitorTimer = nil
    }
    
    private func updateSystemMetrics() {
        // 更新系统指标（这里使用模拟数据，实际实现应该使用系统API）
        systemMemoryUsage = getCurrentMemoryUsage()
        systemCPUUsage = getCurrentCPUUsage()
        batteryLevel = getCurrentBatteryLevel()
        
        updateSystemUsage()
        
        // 检查是否需要优化
        if shouldOptimizeResources() {
            optimizeResourceDistribution()
        }
    }
    
    private func updateSystemUsage() {
        activeCameraCount = resourceAllocations.count
        totalBandwidthUsage = resourceAllocations.values.reduce(0) { $0 + $1.bandwidthMbps }
    }
    
    private func shouldOptimizeResources() -> Bool {
        return systemMemoryUsage > maxMemoryUsage * 0.9 ||
               systemCPUUsage > maxCPUUsage * 0.9 ||
               totalBandwidthUsage > maxBandwidthUsage * 0.9 ||
               thermalState == .critical
    }
    
    // MARK: - System Metrics (Simulated)
    
    private func getCurrentMemoryUsage() -> Double {
        // 实际实现应该使用 mach_task_basic_info 等系统API
        let baseUsage = 0.3 + Double(activeCameraCount) * 0.1
        return min(baseUsage, 1.0)
    }
    
    private func getCurrentCPUUsage() -> Double {
        // 实际实现应该使用 host_processor_info 等系统API
        let baseUsage = 0.1 + Double(activeCameraCount) * 0.05
        return min(baseUsage, 1.0)
    }
    
    private func getCurrentBatteryLevel() -> Double {
        // 实际实现应该使用 UIDevice.current.batteryLevel
        return max(batteryLevel - 0.1, 0.0) // 模拟电池消耗
    }
    
    private func setupThermalStateMonitoring() {
        // 实际实现应该监听 NSProcessInfo.thermalStateDidChangeNotification
        // 这里使用模拟数据
        Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { [weak self] _ in
            Task { @MainActor [weak self] in
                self?.updateThermalState()
            }
        }
    }
    
    private func updateThermalState() {
        // 根据系统负载模拟热状态
        if systemCPUUsage > 0.8 || activeCameraCount > 3 {
            thermalState = .critical
        } else if systemCPUUsage > 0.6 || activeCameraCount > 2 {
            thermalState = .serious
        } else if systemCPUUsage > 0.4 || activeCameraCount > 1 {
            thermalState = .fair
        } else {
            thermalState = .nominal
        }
    }
    
    // MARK: - Public Methods
    
    func canCreateNewConnection() -> Bool {
        return activeCameraCount < maxConcurrentStreams &&
               systemMemoryUsage < maxMemoryUsage &&
               systemCPUUsage < maxCPUUsage &&
               thermalState != .critical
    }
    
    func getResourceUsageForCard(_ cardId: UUID) -> ResourceAllocation? {
        return resourceAllocations[cardId]
    }
    
    func getAllResourceAllocations() -> [ResourceAllocation] {
        return Array(resourceAllocations.values)
    }
}

// MARK: - Supporting Types

/// 资源分配记录
struct ResourceAllocation: Identifiable {
    let id = UUID()
    let cardId: UUID
    var memoryMB: Double
    var cpuPercentage: Double
    var bandwidthMbps: Double
    var quality: VideoQuality
    let allocatedAt: Date
}

/// 所需资源
private struct RequiredResources {
    let memoryMB: Double
    let cpuPercentage: Double
    let bandwidthMbps: Double
}

/// 热状态
enum ThermalState: String, CaseIterable {
    case nominal = "nominal"
    case fair = "fair"
    case serious = "serious"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .nominal: return "正常"
        case .fair: return "一般"
        case .serious: return "严重"
        case .critical: return "危险"
        }
    }
}

// MARK: - VideoQuality Extensions

extension VideoQuality {
    func lowerQuality() -> VideoQuality? {
        switch self {
        case .ultra: return .high
        case .high: return .medium
        case .medium: return .low
        case .low: return nil
        }
    }

    func higherQuality() -> VideoQuality? {
        switch self {
        case .low: return .medium
        case .medium: return .high
        case .high: return .ultra
        case .ultra: return nil
        }
    }
}

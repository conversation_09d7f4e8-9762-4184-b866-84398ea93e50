//
//  CameraDevice.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import Foundation

// MARK: - Camera Device Model

/// 摄像头设备信息模型
struct CameraDevice: Identifiable, Codable, Equatable {
    let id: UUID
    let name: String
    let ipAddress: String
    let port: UInt16
    let macAddress: String?
    let model: String?
    let firmware: String?
    let capabilities: CameraCapabilities?
    let isOnline: Bool
    let lastSeen: Date?
    
    init(name: String, ipAddress: String, port: UInt16, macAddress: String? = nil, model: String? = nil, firmware: String? = nil, capabilities: CameraCapabilities? = nil, isOnline: Bool = false, lastSeen: Date? = nil) {
        self.id = UUID()
        self.name = name
        self.ipAddress = ipAddress
        self.port = port
        self.macAddress = macAddress
        self.model = model
        self.firmware = firmware
        self.capabilities = capabilities
        self.isOnline = isOnline
        self.lastSeen = lastSeen
    }
    

    
    static func == (lhs: CameraDevice, rhs: CameraDevice) -> Bool {
        return lhs.ipAddress == rhs.ipAddress && lhs.port == rhs.port
    }
}

// MARK: - Camera Capabilities

/// 摄像头功能特性
struct CameraCapabilities: Codable {
    let supportsPTZ: Bool
    let supportsZoom: Bool
    let supportsPresets: Bool
    let supportsRecording: Bool
    let supportsAudio: Bool
    let supportsNightVision: Bool
    let maxZoomLevel: Float
    let supportedResolutions: [VideoResolution]
    let supportedFrameRates: [Int]
    
    init(
        supportsPTZ: Bool = true,
        supportsZoom: Bool = true,
        supportsPresets: Bool = true,
        supportsRecording: Bool = true,
        supportsAudio: Bool = true,
        supportsNightVision: Bool = false,
        maxZoomLevel: Float = 10.0,
        supportedResolutions: [VideoResolution] = [.hd720, .hd1080],
        supportedFrameRates: [Int] = [15, 25, 30]
    ) {
        self.supportsPTZ = supportsPTZ
        self.supportsZoom = supportsZoom
        self.supportsPresets = supportsPresets
        self.supportsRecording = supportsRecording
        self.supportsAudio = supportsAudio
        self.supportsNightVision = supportsNightVision
        self.maxZoomLevel = maxZoomLevel
        self.supportedResolutions = supportedResolutions
        self.supportedFrameRates = supportedFrameRates
    }
}

// MARK: - Video Resolution

/// 视频分辨率枚举
enum VideoResolution: String, CaseIterable, Codable {
    case sd480 = "480p"
    case hd720 = "720p"
    case hd1080 = "1080p"
    case uhd4k = "4K"
    
    var dimensions: (width: Int, height: Int) {
        switch self {
        case .sd480:
            return (640, 480)
        case .hd720:
            return (1280, 720)
        case .hd1080:
            return (1920, 1080)
        case .uhd4k:
            return (3840, 2160)
        }
    }
    
    var displayName: String {
        return rawValue
    }
}

// MARK: - PTZ Position

/// PTZ位置信息
struct PTZPosition: Codable, Equatable {
    let pan: Float    // 水平角度 (-180 to 180)
    let tilt: Float   // 垂直角度 (-90 to 90)
    let zoom: Float   // 缩放级别 (1.0 to maxZoom)
    
    init(pan: Float = 0, tilt: Float = 0, zoom: Float = 1.0) {
        self.pan = max(-180, min(180, pan))
        self.tilt = max(-90, min(90, tilt))
        self.zoom = max(1.0, zoom)
    }
    
    static let home = PTZPosition(pan: 0, tilt: 0, zoom: 1.0)
}

// MARK: - Camera Preset

/// 摄像头预设位置
struct CameraPreset: Identifiable, Codable {
    let id: UUID
    let name: String
    let position: PTZPosition
    let createdAt: Date
    
    init(name: String, position: PTZPosition) {
        self.id = UUID()
        self.name = name
        self.position = position
        self.createdAt = Date()
    }
}
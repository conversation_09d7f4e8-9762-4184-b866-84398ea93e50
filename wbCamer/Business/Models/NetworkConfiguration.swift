//
//  NetworkConfiguration.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import Foundation

// MARK: - Network Configuration

/// 网络配置模型
struct NetworkConfiguration: Codable {
    let webrtcConfig: WebRTCConfiguration
    let websocketConfig: WebSocketConfiguration
    let httpConfig: HTTPConfiguration
    
    init(
        webrtcConfig: WebRTCConfiguration = WebRTCConfiguration(),
        websocketConfig: WebSocketConfiguration = WebSocketConfiguration(),
        httpConfig: HTTPConfiguration = HTTPConfiguration()
    ) {
        self.webrtcConfig = webrtcConfig
        self.websocketConfig = websocketConfig
        self.httpConfig = httpConfig
    }
}

// MARK: - WebRTC Configuration

/// WebRTC配置
struct WebRTCConfiguration: Codable {
    let iceServers: [ICEServer]
    let enableAudio: Bool
    let enableVideo: Bool
    let videoCodec: VideoCodec
    let audioCodec: AudioCodec
    
    init(
        iceServers: [ICEServer] = [ICEServer.defaultSTUN],
        enableAudio: Bool = true,
        enableVideo: Bool = true,
        videoCodec: VideoCodec = .h264,
        audioCodec: AudioCodec = .opus
    ) {
        self.iceServers = iceServers
        self.enableAudio = enableAudio
        self.enableVideo = enableVideo
        self.videoCodec = videoCodec
        self.audioCodec = audioCodec
    }
}

// MARK: - ICE Server

/// ICE服务器配置
struct ICEServer: Codable {
    let urls: [String]
    let username: String?
    let credential: String?
    
    init(urls: [String], username: String? = nil, credential: String? = nil) {
        self.urls = urls
        self.username = username
        self.credential = credential
    }
    
    static let defaultSTUN = ICEServer(urls: ["stun:stun.l.google.com:19302"])
}

// MARK: - Video Codec

/// 视频编解码器
enum VideoCodec: String, CaseIterable, Codable {
    case h264 = "H264"
    case h265 = "H265"
    case vp8 = "VP8"
    case vp9 = "VP9"
    
    var displayName: String {
        return rawValue
    }
}

// MARK: - Audio Codec

/// 音频编解码器
enum AudioCodec: String, CaseIterable, Codable {
    case opus = "OPUS"
    case g722 = "G722"
    case pcmu = "PCMU"
    case pcma = "PCMA"
    
    var displayName: String {
        return rawValue
    }
}

// MARK: - WebSocket Configuration

/// WebSocket配置
struct WebSocketConfiguration: Codable {
    let connectionTimeout: TimeInterval
    let heartbeatInterval: TimeInterval
    let maxReconnectAttempts: Int
    let reconnectDelay: TimeInterval
    
    init(
        connectionTimeout: TimeInterval = 30.0,
        heartbeatInterval: TimeInterval = 20.0,
        maxReconnectAttempts: Int = 8,
        reconnectDelay: TimeInterval = 1.0
    ) {
        self.connectionTimeout = connectionTimeout
        self.heartbeatInterval = heartbeatInterval
        self.maxReconnectAttempts = maxReconnectAttempts
        self.reconnectDelay = reconnectDelay
    }
}

// MARK: - HTTP Configuration

/// HTTP配置
struct HTTPConfiguration: Codable {
    let timeout: TimeInterval
    let maxRetries: Int
    let retryDelay: TimeInterval
    let userAgent: String
    
    init(
        timeout: TimeInterval = 30.0,
        maxRetries: Int = 3,
        retryDelay: TimeInterval = 1.0,
        userAgent: String = "wbCamer/1.0"
    ) {
        self.timeout = timeout
        self.maxRetries = maxRetries
        self.retryDelay = retryDelay
        self.userAgent = userAgent
    }
}

// MARK: - Connection State

/// 连接状态枚举
enum ConnectionState: String, CaseIterable {
    case disconnected = "disconnected"
    case connecting = "connecting"
    case connected = "connected"
    case reconnecting = "reconnecting"
    case failed = "failed"
    
    var displayName: String {
        switch self {
        case .disconnected:
            return "已断开"
        case .connecting:
            return "连接中"
        case .connected:
            return "已连接"
        case .reconnecting:
            return "重连中"
        case .failed:
            return "连接失败"
        }
    }
    
    var isConnected: Bool {
        return self == .connected
    }
}
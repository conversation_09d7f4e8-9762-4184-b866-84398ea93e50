//
//  PTZManager.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine

// MARK: - 去重相关数据结构

struct PTZDirectionRequest {
    let action: PTZControlRequest.PTZAction
    let fspeed: Float
    let timestamp: Date
}

struct PTZMoveRequest {
    let panSpeed: Float
    let tiltSpeed: Float
    let timestamp: Date
}

struct ZoomRequest {
    let action: ZoomControlRequest.ZoomAction
    let fspeed: Float
    let timestamp: Date
}

// MARK: - PTZ Manager

@MainActor
class PTZManager: ObservableObject {
    static let shared = PTZManager()
    
    @Published var isControlling = false
    @Published var currentPTZAction: PTZControlRequest.PTZAction?
    @Published var currentZoomAction: ZoomControlRequest.ZoomAction?
    
    private var apiClient: APIClient?
    private var cancellables = Set<AnyCancellable>()
    private var ptzTimer: Timer?
    private var zoomTimer: Timer?
    
    // MARK: - 去重相关属性
    private var lastPTZDirectionRequest: PTZDirectionRequest?
    private var lastPTZMoveRequest: PTZMoveRequest?
    private var lastZoomRequest: ZoomRequest?
    
    // 去重配置
    private let deduplicationTimeThreshold: TimeInterval = 0.1 // 100ms内的重复请求将被忽略
    private let speedDifferenceThreshold: Float = 0.05 // 速度差异小于0.05的请求将被忽略
    
    private init() {}
    
    // MARK: - Configuration
    
    func configure(with apiClient: APIClient) {
        self.apiClient = apiClient
    }
    
    // MARK: - PTZ Control

    // 单方向PTZ控制 (directionMove)
    func executePTZAction(_ action: PTZControlRequest.PTZAction, fspeed: Float = 0.5) {
        guard apiClient != nil else {
            print("[PTZManager] ❌ API client not configured")
            return
        }

        currentPTZAction = action

        // 停止之前的PTZ定时器
        ptzTimer?.invalidate()

        // 对于停止操作，允许重复发送
        if action == .stop {
            //print("[PTZManager] 🛑 PTZ Stop")
            sendPTZStopCommand()
            currentPTZAction = nil
            // 清除上次请求记录
            lastPTZDirectionRequest = nil
            return
        }

        // 去重检查
        let currentRequest = PTZDirectionRequest(action: action, fspeed: fspeed, timestamp: Date())
        if shouldSkipPTZDirectionRequest(currentRequest) {
            print("[PTZManager] 🔄 PTZ Direction request skipped due to deduplication: \(action), fspeed: \(fspeed)")
            return
        }

        print("[PTZManager] 🎮 PTZ Action: \(action), fspeed: \(fspeed)")

        // 记录当前请求
        lastPTZDirectionRequest = currentRequest

        // 立即发送命令
        sendPTZDirectionCommand(action, fspeed: fspeed)
    }

    // 同时控制pan和tilt (ptMove) - 这是主要的摇杆控制方法
    func executePTZMove(panSpeed: Float, tiltSpeed: Float) {
        guard apiClient != nil else {
            print("[PTZManager] ❌ API client not configured")
            return
        }

        // 停止之前的PTZ定时器
        ptzTimer?.invalidate()

        // 如果速度都为0，发送停止命令（停止操作允许重复发送）
        if abs(panSpeed) < 0.01 && abs(tiltSpeed) < 0.01 {
            //print("[PTZManager] 🛑 PTZ Stop (pan: \(panSpeed), tilt: \(tiltSpeed))")
            sendPTZStopCommand()
            currentPTZAction = nil
            // 清除上次请求记录
            lastPTZMoveRequest = nil
            return
        }

        // 去重检查
        let currentRequest = PTZMoveRequest(panSpeed: panSpeed, tiltSpeed: tiltSpeed, timestamp: Date())
        if shouldSkipPTZMoveRequest(currentRequest) {
            //print("[PTZManager] 🔄 PTZ Move request skipped due to deduplication: pan=\(panSpeed), tilt=\(tiltSpeed)")
            return
        }

        //print("[PTZManager] 🎮 PTZ Move: pan=\(panSpeed), tilt=\(tiltSpeed)")

        // 记录当前请求
        lastPTZMoveRequest = currentRequest

        // 发送PTZ移动命令
        sendPTZMoveCommand(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
    }

    private func sendPTZDirectionCommand(_ action: PTZControlRequest.PTZAction, fspeed: Float) {
        guard let apiClient = apiClient else { return }

        let request = PTZControlRequest(action: action, fspeed: fspeed)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ PTZ direction command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    // PTZ日志已屏蔽以减少高频输出
                    // print("[PTZManager] ✅ PTZ direction command sent: \(action), fspeed: \(fspeed)")
                }
            )
            .store(in: &cancellables)
    }

    private func sendPTZMoveCommand(panSpeed: Float, tiltSpeed: Float) {
        guard let apiClient = apiClient else { return }

        let request = PTZControlRequest(panSpeed: panSpeed, tiltSpeed: tiltSpeed)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ PTZ move command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    // PTZ日志已屏蔽以减少高频输出
                    // print("[PTZManager] ✅ PTZ move command sent: pan=\(panSpeed), tilt=\(tiltSpeed)")
                }
            )
            .store(in: &cancellables)
    }

    private func sendPTZStopCommand() {
        guard let apiClient = apiClient else { return }

        let request = PTZControlRequest(stopAll: false)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ PTZ stop command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    // PTZ日志已屏蔽以减少高频输出
                    // print("[PTZManager] ✅ PTZ stop command sent")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Zoom Control

    func executeZoomAction(_ action: ZoomControlRequest.ZoomAction, fspeed: Float = 0.5) {
        guard apiClient != nil else {
            print("[PTZManager] ❌ API client not configured")
            return
        }

        currentZoomAction = action

        // 停止之前的变焦定时器
        zoomTimer?.invalidate()

        // 检查是否是停止操作（停止操作允许重复发送）
        if case .stop = action {
            // Zoom日志已屏蔽以减少高频输出
        // print("[PTZManager] 🛑 Zoom Stop")
            sendZoomCommand(action, fspeed: fspeed)
            currentZoomAction = nil
            // 清除上次请求记录
            lastZoomRequest = nil
            return
        }

        // 去重检查
        let currentRequest = ZoomRequest(action: action, fspeed: fspeed, timestamp: Date())
        if shouldSkipZoomRequest(currentRequest) {
            // Zoom日志已屏蔽以减少高频输出
            // print("[PTZManager] 🔄 Zoom request skipped due to deduplication: \(action), fspeed: \(fspeed)")
            return
        }

        // Zoom日志已屏蔽以减少高频输出
        // print("[PTZManager] 🔍 Zoom Action: \(action), fspeed: \(fspeed)")

        // 记录当前请求
        lastZoomRequest = currentRequest

        // 立即发送命令
        sendZoomCommand(action, fspeed: fspeed)
    }

    private func sendZoomCommand(_ action: ZoomControlRequest.ZoomAction, fspeed: Float = 0.5) {
        guard let apiClient = apiClient else { return }

        let request = ZoomControlRequest(action: action, fspeed: fspeed)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ Zoom command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    // Zoom日志已屏蔽以减少高频输出
                    // print("[PTZManager] ✅ Zoom command sent: \(action), fspeed: \(fspeed)")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Focus Control
    
    func executeFocusAction(_ action: FocusControlRequest.FocusAction) {
        guard let apiClient = apiClient else {
            print("[PTZManager] ❌ API client not configured")
            return
        }
        
        print("[PTZManager] 🎯 Focus Action: \(action)")
        
        let request = FocusControlRequest(action: action)
        
        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("[PTZManager] ❌ Focus command failed: \(error)")
                    }
                },
                receiveValue: { response in
                    print("[PTZManager] ✅ Focus command sent: \(action)")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - 去重检查方法
    
    private func shouldSkipPTZDirectionRequest(_ request: PTZDirectionRequest) -> Bool {
        guard let lastRequest = lastPTZDirectionRequest else {
            return false
        }
        
        let timeDifference = request.timestamp.timeIntervalSince(lastRequest.timestamp)
        let speedDifference = abs(request.fspeed - lastRequest.fspeed)
        
        // 如果方向相同，时间间隔短，且速度差异小，则跳过
        return request.action == lastRequest.action &&
               timeDifference < deduplicationTimeThreshold &&
               speedDifference < speedDifferenceThreshold
    }
    
    private func shouldSkipPTZMoveRequest(_ request: PTZMoveRequest) -> Bool {
        guard let lastRequest = lastPTZMoveRequest else {
            return false
        }
        
        let timeDifference = request.timestamp.timeIntervalSince(lastRequest.timestamp)
        let panSpeedDifference = abs(request.panSpeed - lastRequest.panSpeed)
        let tiltSpeedDifference = abs(request.tiltSpeed - lastRequest.tiltSpeed)
        
        // 如果时间间隔短，且pan和tilt速度差异都很小，则跳过
        return timeDifference < deduplicationTimeThreshold &&
               panSpeedDifference < speedDifferenceThreshold &&
               tiltSpeedDifference < speedDifferenceThreshold
    }
    
    private func shouldSkipZoomRequest(_ request: ZoomRequest) -> Bool {
        guard let lastRequest = lastZoomRequest else {
            return false
        }
        
        let timeDifference = request.timestamp.timeIntervalSince(lastRequest.timestamp)
        let speedDifference = abs(request.fspeed - lastRequest.fspeed)
        
        // 如果动作相同，时间间隔短，且速度差异小，则跳过
        return isSameZoomAction(request.action, lastRequest.action) &&
               timeDifference < deduplicationTimeThreshold &&
               speedDifference < speedDifferenceThreshold
    }
    
    private func isSameZoomAction(_ action1: ZoomControlRequest.ZoomAction, _ action2: ZoomControlRequest.ZoomAction) -> Bool {
        switch (action1, action2) {
        case (.zoomIn, .zoomIn), (.zoomOut, .zoomOut), (.stop, .stop), (.status, .status):
            return true
        case (.position(let pos1), .position(let pos2)):
            return pos1 == pos2
        default:
            return false
        }
    }
    
    // MARK: - Cleanup
    
    func stopAllActions() {
        print("[PTZManager] 🛑 Stopping all PTZ actions")
        
        ptzTimer?.invalidate()
        zoomTimer?.invalidate()
        
        if currentPTZAction != nil {
            sendPTZStopCommand()
            currentPTZAction = nil
        }

        if currentZoomAction != nil {
            sendZoomCommand(.stop, fspeed: 0.5)
            currentZoomAction = nil
        }
        
        // 清除去重记录
        lastPTZDirectionRequest = nil
        lastPTZMoveRequest = nil
        lastZoomRequest = nil
        
        isControlling = false
    }
    
    deinit {
        ptzTimer?.invalidate()
        zoomTimer?.invalidate()
        cancellables.removeAll()
    }
}

// MARK: - PTZ Control Extensions

extension PTZManager {

    // 便捷方法：移动到指定方向 (单方向控制)
    func moveToDirection(_ direction: JoystickDirection, fspeed: Float = 0.5) {
        executePTZAction(direction.ptzAction, fspeed: fspeed)
    }

    // 便捷方法：停止移动
    func stopMovement() {
        executePTZAction(PTZControlRequest.PTZAction.stop)
    }

    // 便捷方法：摇杆控制 (同时控制pan和tilt)
    func moveWithJoystick(panSpeed: Float, tiltSpeed: Float) {
        executePTZMove(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
    }

    // 便捷方法：变焦放大
    func zoomIn(fspeed: Float = 0.5) {
        executeZoomAction(ZoomControlRequest.ZoomAction.zoomIn, fspeed: fspeed)
    }

    // 便捷方法：变焦缩小
    func zoomOut(fspeed: Float = 0.5) {
        executeZoomAction(ZoomControlRequest.ZoomAction.zoomOut, fspeed: fspeed)
    }

    // 便捷方法：停止变焦
    func stopZoom() {
        executeZoomAction(ZoomControlRequest.ZoomAction.stop)
    }

    // 便捷方法：自动对焦
    func autoFocus() {
        executeFocusAction(FocusControlRequest.FocusAction.autoFocus)
    }
}

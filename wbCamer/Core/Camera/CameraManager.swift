//
//  CameraManager.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import Foundation
import Combine
import Network
import AVFoundation

// MARK: - Camera Manager

@MainActor
class CameraManager: ObservableObject {
    static let shared = CameraManager()
    
    // MARK: - Published Properties
    @Published var isConnected = false  // 整体连接状态，基于WebRTC和基本HTTP连接
    @Published var isConnecting = false
    @Published var currentCamera: CameraDevice?
    @Published var cameraStatus: CameraStatus?
    @Published var isRecording = false
    @Published var recordingDuration: TimeInterval = 0
    @Published var batteryLevel: Int = 0
    @Published var storageInfo: StorageInfo?
    @Published var connectionError: Error?

    // 新增：是否应该显示视频预览界面（基于WebRTC连接状态）
    @Published var shouldShowVideoInterface = false

    // Work mode tracking
    private var workModeLastUpdated: Date?
    @Published var availableCameras: [CameraDevice] = []
    
    // MARK: - Private Properties
    private let apiClient = APIClient.shared
    let webSocketManager: WebSocketManager
    private let networkMonitor = NetworkMonitor.shared
    private let coreDataStack = CoreDataStack.shared
    
    // WebRTC support (使用优化的 WebRTC Streamer 客户端)
    @Published var webRTCStreamerClient: OptimizedWebRTCStreamerClient?
    @Published var isWebRTCConnected = false
    
    private var cancellables = Set<AnyCancellable>()
    private var statusUpdateTimer: Timer?
    private var recordingTimer: Timer?
    private var discoveryTimer: Timer?
    
    // MARK: - Initialization
    
    private init() {
        print("🏗️ CameraManager initialized")
        
        // Initialize WebSocketManager with optimized configuration
        // 不使用默认URL，等待连接时动态设置
        let wsConfig = WebSocketConfiguration(
            connectionTimeout: 30.0,
            heartbeatInterval: 11.0,  // P2-R1设备心跳间隔为11秒
            maxReconnectAttempts: 8,
            reconnectDelay: 1.0
        )
        // 使用空的baseURL，避免硬编码的默认地址
        self.webSocketManager = WebSocketManager(baseURL: "", configuration: wsConfig)
        
        setupBindings()
        _ = loadSavedCameras()
        print("✅ CameraManager setup complete")
    }
    
    // MARK: - Public Methods
    
    func discoverCameras() {
        guard case .reachable = networkMonitor.networkInfo.status else {
            print("Network not available for camera discovery")
            return
        }
        
        // Start network scanning for cameras
        scanForCamerasOnNetwork { [weak self] cameras in
            DispatchQueue.main.async {
                self?.availableCameras = cameras
                self?.saveCamerasToStorage(cameras)
            }
        }
    }
    
    func connect(to camera: CameraDevice) {
        print("📞 CameraManager.connect() called for \(camera.ipAddress):\(camera.port)")

        guard !isConnecting else {
            print("⚠️ Connection already in progress, ignoring new connection request")
            return
        }

        print("🔗 Attempting to connect to camera: \(camera.name) at \(camera.ipAddress):\(camera.port)")

        isConnecting = true
        connectionError = nil
        currentCamera = camera

        // Update APIClient base URL to match the camera's IP
        // Don't include port 80 in URL as it's the default HTTP port
        let baseURL: String
        if camera.port == 80 {
            baseURL = "http://\(camera.ipAddress)"
        } else {
            baseURL = "http://\(camera.ipAddress):\(camera.port)"
        }
        apiClient.updateBaseURL(to: baseURL)

        // Configure PTZManager with the APIClient
        Task { @MainActor in
            PTZManager.shared.configure(with: apiClient)
            print("✅ PTZManager configured with APIClient for \(camera.ipAddress)")
        }

        // Test camera connection
        networkMonitor.testCameraConnectivity(to: camera.ipAddress, port: Int(camera.port)) { [weak self] success, latency in
            DispatchQueue.main.async {
                if success {
                    print("✅ Camera connectivity test passed (latency: \(latency?.description ?? "unknown"))")
                    self?.establishCameraConnection(to: camera)
                } else {
                    print("❌ Camera connectivity test failed")
                    self?.handleConnectionFailure(APIError.networkError(NSError(domain: "CameraManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Connection failed"])))
                }
            }
        }
    }
    
    func disconnect() {
        stopStatusUpdates()
        stopRecordingTimer()
        webSocketManager.disconnect()
        forceDisconnectWebRTC()  // 使用强制断开，因为这是真正的相机断开

        isConnected = false
        isConnecting = false
        currentCamera = nil
        cameraStatus = nil
        connectionError = nil

        // 更新视频界面可见性
        updateVideoInterfaceVisibility()
    }
    
    func reconnect() {
        guard let camera = currentCamera else { return }
        disconnect()
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.connect(to: camera)
        }
    }
    
    // MARK: - Camera Control Methods
    
    func startRecording() {
        guard isConnected, let _ = currentCamera else { return }

        let request = RecordingControlAPIRequest(action: .start)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to start recording: \(error)")
                    }
                },
                receiveValue: { [weak self] (response: BasicResponse) in
                    if response.code == 0 {
                        self?.isRecording = true
                        self?.startRecordingTimer()
                    } else {
                        print("Recording start failed: \(response.desc ?? "Unknown error")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func stopRecording() {
        guard isConnected, let _ = currentCamera else { return }

        let request = RecordingControlAPIRequest(action: .stop)

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to stop recording: \(error)")
                    }
                },
                receiveValue: { [weak self] (response: BasicResponse) in
                    if response.code == 0 {
                        self?.isRecording = false
                        self?.stopRecordingTimer()
                    } else {
                        print("Recording stop failed: \(response.desc ?? "Unknown error")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func updateCameraSettings(_ settings: CameraSettings) {
        guard isConnected, let _ = currentCamera else { return }
        
        // 使用官方API逐个设置参数
        if let iso = settings.iso {
            let isoRequest = SetCameraSettingsRequest(key: "iso", value: String(iso))
            apiClient.request(isoRequest)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            print("Failed to set ISO: \(error)")
                        }
                    },
                    receiveValue: { (response: BasicResponse) in
                        if response.code == 0 {
                            print("ISO set successfully")
                        }
                    }
                )
                .store(in: &cancellables)
        }

        // 设置白平衡
        let wbRequest = SetCameraSettingsRequest(key: "wb", value: settings.whiteBalance.rawValue)
        apiClient.request(wbRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to set white balance: \(error)")
                    }
                },
                receiveValue: { (response: BasicResponse) in
                    if response.code == 0 {
                        print("White balance set successfully")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func capturePhoto() {
        guard isConnected, let _ = currentCamera else { return }

        let request = CapturePhotoRequest()

        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to capture photo: \(error)")
                        print("⚠️ Note: /ctrl/snapshot API may not be supported by this device")
                    }
                },
                receiveValue: { (response: BasicResponse) in
                    if response.code == 0 {
                        print("Photo captured successfully")
                    } else {
                        print("Photo capture failed: \(response.desc ?? "Unknown error")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - WebRTC Methods
    
    @MainActor
    func initializeWebRTC(for camera: CameraDevice) {
        guard webRTCStreamerClient == nil else {
            print("[CameraManager] WebRTC client already exists, skipping initialization")
            return
        }

        print("[CameraManager] Initializing optimized WebRTC client...")
        webRTCStreamerClient = OptimizedWebRTCStreamerClient()
        print("[CameraManager] Optimized WebRTC client created: \(webRTCStreamerClient != nil)")

        // Monitor WebRTC connection state
        webRTCStreamerClient?.$connectionState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                print("[CameraManager] WebRTC connection state changed: \(state)")
                let isWebRTCConnected = (state == .connected)
                self?.isWebRTCConnected = isWebRTCConnected

                // 更新是否应该显示视频界面
                self?.updateVideoInterfaceVisibility()
            }
            .store(in: &cancellables)
            
        // Monitor WebRTC ICE connection state for more accurate connection status
        webRTCStreamerClient?.$iceConnectionState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] iceState in
                print("[CameraManager] WebRTC ICE state changed: \(iceState)")
                // 只有在ICE连接真正建立时才认为WebRTC已连接
                let isReallyConnected = (iceState == .connected || iceState == .completed)
                if self?.isWebRTCConnected != isReallyConnected {
                    self?.isWebRTCConnected = isReallyConnected
                    self?.updateVideoInterfaceVisibility()
                }
            }
            .store(in: &cancellables)
    }
    
    func connectWebRTC(to camera: CameraDevice) {
        Task { @MainActor in
            if webRTCStreamerClient == nil {
                initializeWebRTC(for: camera)
            }

            guard let webRTCStreamerClient = webRTCStreamerClient else {
                print("❌ WebRTC client not available")
                return
            }

            do {
                // P2-R1 WebRTC Streamer 运行在端口 8000，而不是 HTTP API 的端口 80
                let webrtcPort = 8000
                print("🎥 Connecting to WebRTC Streamer at \(camera.ipAddress):\(webrtcPort)")
                try await webRTCStreamerClient.connect(to: camera.ipAddress, port: webrtcPort)
                print("✅ WebRTC Streamer connected successfully to \(camera.name)")
                self.isWebRTCConnected = true

                // 更新视频界面可见性
                DispatchQueue.main.async {
                    self.updateVideoInterfaceVisibility()
                }
            } catch {
                print("❌ Failed to connect WebRTC Streamer: \(error)")
                self.isWebRTCConnected = false

                // 更新视频界面可见性
                DispatchQueue.main.async {
                    self.updateVideoInterfaceVisibility()
                    self.connectionError = error
                }

                // 尝试重新连接（最多3次）
                await retryWebRTCConnection(to: camera, attempt: 1)
            }
        }
    }

    private func retryWebRTCConnection(to camera: CameraDevice, attempt: Int) async {
        guard attempt <= 3 else {
            print("❌ WebRTC connection failed after 3 attempts")
            return
        }

        print("🔄 Retrying WebRTC connection (attempt \(attempt)/3)...")

        // 等待一段时间后重试
        try? await Task.sleep(nanoseconds: UInt64(attempt * 2_000_000_000)) // 2秒 * 尝试次数

        guard let webRTCStreamerClient = webRTCStreamerClient else { return }

        do {
            let webrtcPort = 8000
            try await webRTCStreamerClient.connect(to: camera.ipAddress, port: webrtcPort)
            print("✅ WebRTC Streamer connected successfully on retry \(attempt)")
            self.isWebRTCConnected = true
        } catch {
            print("❌ WebRTC retry \(attempt) failed: \(error)")
            await retryWebRTCConnection(to: camera, attempt: attempt + 1)
        }
    }
    
    func disconnectWebRTC() {
        Task { @MainActor in
            print("[CameraManager] Disconnecting WebRTC...")

            // 先断开连接，但保持对象引用
            webRTCStreamerClient?.disconnect()
            isWebRTCConnected = false

            // 延迟清理对象引用，确保断开操作完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                print("[CameraManager] Cleaning up WebRTC client reference")
                self?.webRTCStreamerClient = nil
            }
        }
    }

    // 新增：仅在真正需要时断开WebRTC（如相机断开连接）
    func forceDisconnectWebRTC() {
        disconnectWebRTC()
    }

    // 检查WebRTC连接的真实状态
    @MainActor
    private func checkWebRTCRealConnectionState() -> Bool {
        guard let client = webRTCStreamerClient else { return false }
        
        // 检查WebRTC的内部连接状态
        let isReallyConnected = client.isConnected && 
                               (client.iceConnectionState == .connected || client.iceConnectionState == .completed) &&
                               client.remoteVideoTrack != nil
        
        print("[CameraManager] WebRTC real connection state: isConnected=\(client.isConnected), iceState=\(client.iceConnectionState), hasVideoTrack=\(client.remoteVideoTrack != nil)")
        return isReallyConnected
    }
    
    // 仅在WebRTC客户端丢失时重新初始化（用于应用启动或相机切换）
    func initializeWebRTCIfNeeded(for camera: CameraDevice) {
        guard isConnected else { return }
        
        Task { @MainActor in
            if webRTCStreamerClient == nil {
                print("[CameraManager] WebRTC client missing during initialization, creating new client...")
                initializeWebRTC(for: camera)
                connectWebRTC(to: camera)
            } else {
                print("[CameraManager] WebRTC client already exists, skipping initialization")
            }
        }
    }
    
    // 检查并恢复WebRTC连接状态 - 仅在真正需要时重建
    func ensureWebRTCConnection(for camera: CameraDevice) {
        guard isConnected else { return }

        Task { @MainActor in
            // 只有在WebRTC客户端完全丢失时才重新初始化
            if webRTCStreamerClient == nil {
                print("[CameraManager] WebRTC client missing, reinitializing...")
                initializeWebRTC(for: camera)
                // 初始化后立即尝试连接
                connectWebRTC(to: camera)
            } else {
                // 检查WebRTC的真实连接状态
                let isReallyConnected = checkWebRTCRealConnectionState()
                if !isReallyConnected {
                    print("[CameraManager] WebRTC client exists but not properly connected, checking if reconnection is needed...")
                    // 只有在ICE连接状态为failed或closed时才重连
                    if webRTCStreamerClient?.iceConnectionState == .failed || 
                       webRTCStreamerClient?.iceConnectionState == .closed {
                        print("[CameraManager] WebRTC ICE connection failed, attempting reconnection...")
                        connectWebRTC(to: camera)
                    } else {
                        print("[CameraManager] WebRTC is in transitional state, letting it self-recover...")
                    }
                } else {
                    print("[CameraManager] WebRTC connection is healthy, no action needed")
                }
            }
        }
    }

    // 更新视频界面可见性
    private func updateVideoInterfaceVisibility() {
        let shouldShow = isConnected && currentCamera != nil && isWebRTCConnected
        print("[CameraManager] Updating shouldShowVideoInterface: \(shouldShow) (isConnected: \(isConnected), hasCamera: \(currentCamera != nil), isWebRTCConnected: \(isWebRTCConnected))")
        shouldShowVideoInterface = shouldShow
    }
    
    @available(*, deprecated, message: "使用getCameraInfo代替，HTTP状态轮询已移除")
    func refreshCameraStatus() {
        // 此方法已废弃，不再用于状态轮询
        // /info接口只在初始连接时调用一次获取摄像机基本信息
        // 实时状态更新通过WebSocket消息处理
        print("⚠️ refreshCameraStatus() is deprecated - use WebSocket for real-time updates")
    }

    // refreshCameraStatusIfNeeded 方法已移除
    // HTTP状态轮询不再需要，WebSocket和WebRTC有独立的状态管理机制
    // /info接口的响应内容在单次使用过程中不会变化，重复调用没有意义

    private func refreshWorkMode() {
        print("🔧 Refreshing work mode...")
        let modeRequest = WorkModeRequest(action: .query)

        apiClient.request(modeRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        print("Failed to get work mode: \(error)")
                    } else {
                        self?.workModeLastUpdated = Date()
                    }
                },
                receiveValue: { (response: BasicResponse) in
                    print("Work mode response: \(response)")
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods

    private func scanForCamerasOnNetwork(completion: @escaping ([CameraDevice]) -> Void) {
        // Simulate camera discovery - in real implementation, this would use mDNS or network scanning
        DispatchQueue.global(qos: .userInitiated).async {
            var discoveredCameras: [CameraDevice] = []

            // Add any previously saved cameras
            Task { @MainActor in
                discoveredCameras.append(contentsOf: self.loadSavedCameras())
            }

            // 注释掉硬编码的示例相机，只依赖真实的网络发现
            // let sampleCamera = CameraDevice(
            //     name: "Eagle Camera",
            //     ipAddress: "*************",
            //     port: 80
            // )

            // if !discoveredCameras.contains(where: { $0.ipAddress == sampleCamera.ipAddress }) {
            //     discoveredCameras.append(sampleCamera)
            // }

            DispatchQueue.main.async {
                completion(discoveredCameras)
            }
        }
    }
    
    private func setupBindings() {
        // Monitor network status
        networkMonitor.$networkInfo
            .sink { [weak self] networkInfo in
                if networkInfo.status == .notReachable && self?.isConnected == true {
                    let networkError = NSError(domain: "CameraManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Network not reachable"])
                    self?.handleConnectionFailure(APIError.networkError(networkError))
                }
            }
            .store(in: &cancellables)
        
        // Monitor WebSocket messages
        webSocketManager.messagePublisher
            .sink { [weak self] message in
                self?.handleWebSocketMessage(message)
            }
            .store(in: &cancellables)
        
        // Monitor WebSocket connection state - 但不影响整体连接状态
        // 优化说明：WebSocket重连不再触发WebRTC重建，避免不必要的连接中断
        webSocketManager.$connectionState
            .sink { state in
                let timestamp = DateFormatter.iso8601Full.string(from: Date())
                print("[\(timestamp)] [CameraManager] WebSocket state changed: \(state)")
                switch state {
                case .disconnected:
                    print("[\(timestamp)] ⚠️ WebSocket disconnected - will attempt reconnection in background")
                    // WebSocket断开不影响整体连接状态，只在后台重连
                case .connected:
                    print("[\(timestamp)] ✅ WebSocket reconnected successfully")
                    // 优化：WebSocket重连成功，但不重建WebRTC连接
                    // WebRTC连接独立管理，有自己的状态监控和重连机制
                case .failed(let error):
                    print("[\(timestamp)] ❌ WebSocket connection failed: \(error.localizedDescription)")
                    // 只有在多次重连失败后才考虑断开整体连接
                case .reconnecting:
                    print("[\(timestamp)] 🔄 WebSocket reconnecting...")
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }
    
    private func establishCameraConnection(to camera: CameraDevice) {
        print("🔄 Establishing camera connection to \(camera.ipAddress):\(camera.port)")

        // 直接获取摄像机信息，不需要占用session
        getCameraInfo(camera: camera)
    }

    private func getCameraInfo(camera: CameraDevice) {
        // 获取摄像机基本信息
        let infoRequest = GetCameraInfoRequest()
        print("📡 Getting camera info...")

        apiClient.request(infoRequest)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] (completion: Subscribers.Completion<APIError>) in
                    if case .failure(let error) = completion {
                        print("❌ Camera info request failed: \(error.localizedDescription)")
                        self?.handleConnectionFailure(error)
                    }
                },
                receiveValue: { [weak self] (response: CameraInfoResponse) in
                    print("✅ Camera info request successful: \(response)")
                    // 更新摄像机信息
                    self?.updateCameraInfo(response)
                    // 建立WebSocket连接
                    self?.connectWebSocket(to: camera)
                }
            )
            .store(in: &cancellables)
    }
    
    private func connectWebSocket(to camera: CameraDevice) {
        print("🔌 Connecting WebSocket to camera...")

        // 直接使用ws://协议连接，无需查询HTTPS状态
        let wsURL = "ws://\(camera.ipAddress):81/"
        
        print("🔌 直接连接到WebSocket地址: \(wsURL)")
        
        webSocketManager.updateBaseURL(to: wsURL)
        webSocketManager.connect()
        
        // Monitor connection state changes
        webSocketManager.$connectionState
            .sink { [weak self] state in
                print("🔌 WebSocket state changed: \(state)")
                switch state {
                case .connected:
                    print("✅ WebSocket connected successfully to: \(wsURL)")
                    self?.finishConnection()
                case .failed(let error):
                    print("❌ WebSocket connection failed to \(wsURL): \(error.localizedDescription)")
                    self?.handleConnectionFailure(error)
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }

    // 移除了多重连接尝试的相关方法，现在直接使用正确的WebSocket地址
    
    private func finishConnection() {
        print("🎉 Camera connection established successfully!")

        // 确保在主线程上更新 @Published 属性
        DispatchQueue.main.async {
            print("[CameraManager] Updating isConnected to true on main thread")
            self.isConnected = true
            self.isConnecting = false
            self.connectionError = nil
            print("[CameraManager] isConnected updated: \(self.isConnected)")

            // 更新视频界面可见性
            self.updateVideoInterfaceVisibility()
        }

        // 延迟启动状态更新，优先使用WebSocket消息
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.startStatusUpdates()
        }

        // WebSocket状态更新已启用，不再需要HTTP状态轮询
        print("✅ WebSocket connection established, relying on real-time status updates")

        // Initialize and connect WebRTC for video streaming
        if let camera = currentCamera {
            print("🎥 Initializing WebRTC for video streaming...")
            Task { @MainActor in
                initializeWebRTC(for: camera)
                print("[CameraManager] WebRTC client after initialization: \(webRTCStreamerClient != nil)")

                // 延迟启动 WebRTC 连接，确保 WebRTC 客户端已初始化
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                    print("🎥 Starting WebRTC connection...")
                    self?.connectWebRTC(to: camera)
                }
            }

            saveLastConnectedCamera(camera)
        }
    }
    
    private func handleConnectionFailure(_ error: Error) {
        print("💥 Connection failed: \(error.localizedDescription)")

        isConnected = false
        isConnecting = false
        connectionError = error

        stopStatusUpdates()
        webSocketManager.disconnect()
    }
    
    private func updateCameraInfo(_ response: CameraInfoResponse) {
        // 更新摄像机基本信息
        print("Camera Model: \(response.model ?? "Unknown")")
        print("Software Version: \(response.sw ?? "Unknown")")
        print("MAC Address: \(response.mac ?? "Unknown")")
        print("IP Address: \(response.eth_ip ?? "Unknown")")
        print("Serial Number: \(response.sn ?? "Unknown")")

        // 标记连接成功 - 确保在主线程上更新 @Published 属性
        DispatchQueue.main.async {
            print("[CameraManager] Updating isConnected to true on main thread (from updateCameraInfo)")
            self.isConnected = true
            self.isConnecting = false
            self.connectionError = nil
            print("[CameraManager] isConnected updated: \(self.isConnected)")
        }

        // 移除了录制格式查询，减少不必要的API调用
    }

    // getCameraSettings方法已移除 - 不再需要查询录制格式
    
    private func handleWebSocketMessage(_ message: WebSocketMessage) {
        //print("📨 Received WebSocket message: \(message.type)")

        switch message.type {
        case .statusUpdate:
            if let data = message.data,
               let statusMessage = try? JSONDecoder().decode(StatusUpdateMessage.self, from: data) {
                //print("📊 Status update from WebSocket: battery=\(statusMessage.battery ?? -1), recording=\(statusMessage.recording ?? false)")
                DispatchQueue.main.async {
                    if let battery = statusMessage.battery {
                        self.batteryLevel = battery
                        print("🔋 Battery updated via WebSocket: \(battery)%")
                    }
                    if let recording = statusMessage.recording {
                        if recording != self.isRecording {
                            self.isRecording = recording
                            if recording {
                                self.startRecordingTimer()
                            } else {
                                self.stopRecordingTimer()
                            }
                            print("🎬 Recording status updated via WebSocket: \(recording)")
                        }
                    }
                    if let temperature = statusMessage.temperature {
                        print("🌡️ Temperature updated via WebSocket: \(temperature)°C")
                    }
                    // Handle storage info if needed
                }
            }
        case .recordingStarted:
            print("🔴 Recording started via WebSocket")
            DispatchQueue.main.async {
                self.isRecording = true
                self.startRecordingTimer()
            }
        case .recordingStopped:
            print("⏹️ Recording stopped via WebSocket")
            DispatchQueue.main.async {
                self.isRecording = false
                self.stopRecordingTimer()
            }
        case .configChanged:
            print("⚙️ Configuration changed via WebSocket")
        case .systemTimeChange:
            print("🕐 System time changed via WebSocket")
        case .shutdown:
            print("🔌 Camera shutting down via WebSocket")
            DispatchQueue.main.async {
                self.disconnect()
            }
        case .aiDetection:
            // AI检测消息已由WebSocketManager直接处理并转发给AIDetectionOverlayView
            // 无需在CameraManager中重复处理，避免解析错误
            print("🤖 AI detection message handled by WebSocketManager")
        default:
            print("📨 Other WebSocket message type: \(message.type)")
        }
    }
    
    private func startStatusUpdates() {
        // HTTP状态轮询已移除 - WebSocket和WebRTC有独立的状态管理
        // /info接口只用于初始连接时获取摄像机基本信息，不需要重复调用
        print("ℹ️ HTTP status polling disabled - using WebSocket for real-time updates")
        // statusUpdateTimer 不再启动，依赖WebSocket消息进行状态更新
    }
    
    private func stopStatusUpdates() {
        statusUpdateTimer?.invalidate()
        statusUpdateTimer = nil
    }
    
    private func startRecordingTimer() {
        recordingDuration = 0
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.recordingDuration += 1
            }
        }
    }
    
    private func stopRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
        recordingDuration = 0
    }
    
    // MARK: - Persistence Methods
    
    private func loadSavedCameras() -> [CameraDevice] {
        // Simplified implementation - in real app, this would use CoreData
        let savedCameras = UserDefaults.standard.array(forKey: "SavedCameras") as? [[String: Any]] ?? []

        return savedCameras.compactMap { dict -> CameraDevice? in
            guard let name = dict["name"] as? String,
                  let ipAddress = dict["ipAddress"] as? String,
                  let port = dict["port"] as? Int else {
                return nil
            }

            return CameraDevice(
                name: name,
                ipAddress: ipAddress,
                port: UInt16(port)
            )
        }
    }
    
    private func saveCamerasToStorage(_ cameras: [CameraDevice]) {
        // Simplified implementation using UserDefaults
        let camerasData = cameras.map { camera in
            [
                "name": camera.name,
                "ipAddress": camera.ipAddress,
                "port": camera.port
            ]
        }
        UserDefaults.standard.set(camerasData, forKey: "SavedCameras")
    }

    private func saveCameras(_ cameras: [CameraDevice]) {
        saveCamerasToStorage(cameras)
    }
    
    private func saveLastConnectedCamera(_ camera: CameraDevice) {
        UserDefaults.standard.set(camera.ipAddress, forKey: "last_connected_camera_ip")
    }

    func getLastConnectedCamera() -> CameraDevice? {
        guard let ipAddress = UserDefaults.standard.string(forKey: "last_connected_camera_ip") else {
            return nil
        }

        return availableCameras.first { $0.ipAddress == ipAddress }
    }
}

// MARK: - Camera Status

struct CameraStatus: Codable {
    let isRecording: Bool
    let batteryLevel: Int
    let storageInfo: StorageInfo
    let temperature: Double
    let mode: CameraMode
    let settings: CameraSettings
    let lastActivity: Date
    
    enum CameraMode: String, Codable, CaseIterable {
        case photo = "photo"
        case video = "video"
        case timelapse = "timelapse"
        case burst = "burst"
        
        var displayName: String {
            switch self {
            case .photo: return "Photo"
            case .video: return "Video"
            case .timelapse: return "Timelapse"
            case .burst: return "Burst"
            }
        }
    }
}

// MARK: - Camera Settings

struct CameraSettings: Codable {
    let resolution: VideoResolution
    let frameRate: Int
    let quality: String
    let autoFocus: Bool
    let whiteBalance: WhiteBalanceMode
    let exposureMode: ExposureMode
    let iso: Int?
    let shutterSpeed: Double?

    enum WhiteBalanceMode: String, Codable, CaseIterable {
        case auto = "auto"
        case daylight = "daylight"
        case cloudy = "cloudy"
        case tungsten = "tungsten"
        case fluorescent = "fluorescent"
        case manual = "manual"
    }

    enum ExposureMode: String, Codable, CaseIterable {
        case auto = "auto"
        case manual = "manual"
        case aperturePriority = "aperture_priority"
        case shutterPriority = "shutter_priority"
    }
}

// MARK: - Storage Info

struct StorageInfo: Codable {
    let totalSpace: Int64
    let usedSpace: Int64
    let availableSpace: Int64
    let recordingTimeRemaining: TimeInterval

    var usagePercentage: Double {
        guard totalSpace > 0 else { return 0 }
        return Double(usedSpace) / Double(totalSpace)
    }
    
    var formattedTotalSpace: String {
        ByteCountFormatter.string(fromByteCount: totalSpace, countStyle: .file)
    }
    
    var formattedUsedSpace: String {
        ByteCountFormatter.string(fromByteCount: usedSpace, countStyle: .file)
    }
    
    var formattedAvailableSpace: String {
        ByteCountFormatter.string(fromByteCount: availableSpace, countStyle: .file)
    }
}



// MARK: - Gallery Manager

@MainActor
class GalleryManager: ObservableObject {
    static let shared = GalleryManager()
    
    @Published var files: [CameraFile] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    private let apiClient = APIClient.shared
    private let cameraManager = CameraManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    @MainActor
    func loadFiles() {
        guard cameraManager.currentCamera != nil else { return }
        
        isLoading = true
        error = nil
        
        let request = GetFileListRequest()
        
        apiClient.request(request)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] (completion: Subscribers.Completion<APIError>) in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] (response: DCIMResponse) in
                    // 处理DCIM目录响应
                    if let files = response.files {
                        // 将文件名转换为CameraFile对象
                        let cameraFiles = files.compactMap { filename -> CameraFile? in
                            return CameraFile(
                                id: UUID(),
                                name: filename,
                                type: filename.hasSuffix(".MOV") ? .video : .photo,
                                size: 0, // 需要单独获取
                                createdAt: Date(),
                                path: "/DCIM/\(filename)"
                            )
                        }
                        self?.files = cameraFiles
                    } else if let folders = response.folders {
                        // 如果返回的是文件夹列表，选择第一个文件夹继续获取
                        if let firstFolder = folders.first, let strongSelf = self {
                            let folderRequest = GetFileListRequest(folder: firstFolder)
                            strongSelf.apiClient.request(folderRequest)
                                .receive(on: DispatchQueue.main)
                                .sink(
                                    receiveCompletion: { _ in },
                                    receiveValue: { [weak self] (folderResponse: DCIMResponse) in
                                        guard let self = self else { return }
                                        if let files = folderResponse.files {
                                            let cameraFiles = files.compactMap { filename -> CameraFile? in
                                                return CameraFile(
                                                    id: UUID(),
                                                    name: filename,
                                                    type: filename.hasSuffix(".MOV") ? .video : .photo,
                                                    size: 0,
                                                    createdAt: Date(),
                                                    path: "/DCIM/\(firstFolder)/\(filename)"
                                                )
                                            }
                                            self.files = cameraFiles
                                        }
                                    }
                                )
                                .store(in: &strongSelf.cancellables)
                        }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    @MainActor
    func refreshFiles() {
        loadFiles()
    }
    
    func filteredFiles(for filter: FileFilter) -> [CameraFile] {
        switch filter {
        case .all:
            return files
        case .photos:
            return files.filter { $0.type == .photo }
        case .videos:
            return files.filter { $0.type == .video }
        case .recent:
            return Array(files.sorted { $0.createdAt > $1.createdAt }.prefix(20))
        }
    }
}

enum FileFilter: String, CaseIterable {
    case all = "all"
    case photos = "photos"
    case videos = "videos"
    case recent = "recent"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .photos: return "Photos"
        case .videos: return "Videos"
        case .recent: return "Recent"
        }
    }
}


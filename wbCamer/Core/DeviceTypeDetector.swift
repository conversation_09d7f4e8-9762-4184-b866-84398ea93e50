//
//  DeviceTypeDetector.swift
//  wbCamer
//
//  Created by AI Assistant on 2025/6/30.
//

import UIKit
import SwiftUI

// MARK: - Device Type Detection

enum DeviceType {
    case iPhone
    case iPad
    
    var isPhone: Bool {
        return self == .iPhone
    }
    
    var isTablet: Bool {
        return self == .iPad
    }
}

class DeviceTypeDetector: ObservableObject {
    static let shared = DeviceTypeDetector()
    
    @Published var currentDeviceType: DeviceType
    
    private init() {
        self.currentDeviceType = DeviceTypeDetector.detectDeviceType()
    }
    
    static func detectDeviceType() -> DeviceType {
        switch UIDevice.current.userInterfaceIdiom {
        case .phone:
            return .iPhone
        case .pad:
            return .iPad
        default:
            // 默认为iPhone，包括其他未知设备类型
            return .iPhone
        }
    }
    
    /// 获取当前设备类型
    var deviceType: DeviceType {
        return currentDeviceType
    }
    
    /// 是否为iPhone
    var isPhone: Bool {
        return currentDeviceType.isPhone
    }
    
    /// 是否为iPad
    var isTablet: Bool {
        return currentDeviceType.isTablet
    }
    
    /// 获取适合当前设备的摇杆大小 (已扩大30%)
    var joystickSize: CGFloat {
        switch currentDeviceType {
        case .iPhone:
            return 208  // iPhone: 160 * 1.3 = 208
        case .iPad:
            return 260  // iPad: 200 * 1.3 = 260
        }
    }
    
    /// 获取适合当前设备的控制面板高度
    var controlPanelHeight: CGFloat {
        switch currentDeviceType {
        case .iPhone:
            return 120  // iPhone使用较小的控制面板
        case .iPad:
            return 150  // iPad使用较大的控制面板
        }
    }
    
    /// 获取适合当前设备的边距
    var defaultPadding: CGFloat {
        switch currentDeviceType {
        case .iPhone:
            return 16
        case .iPad:
            return 24
        }
    }
    
    /// 获取适合当前设备的摇杆边距
    var joystickPadding: CGFloat {
        switch currentDeviceType {
        case .iPhone:
            return 20
        case .iPad:
            return 30
        }
    }
}

// MARK: - SwiftUI Environment Key

struct DeviceTypeKey: EnvironmentKey {
    static let defaultValue: DeviceType = DeviceTypeDetector.detectDeviceType()
}

extension EnvironmentValues {
    var deviceType: DeviceType {
        get { self[DeviceTypeKey.self] }
        set { self[DeviceTypeKey.self] = newValue }
    }
}

// MARK: - SwiftUI View Extensions

extension View {
    /// 根据设备类型应用不同的修饰符
    func deviceSpecific<iPhone: View, iPad: View>(
        iPhone: () -> iPhone,
        iPad: () -> iPad
    ) -> some View {
        Group {
            if DeviceTypeDetector.shared.isPhone {
                iPhone()
            } else {
                iPad()
            }
        }
    }
    
    /// 根据设备类型设置不同的padding
    func devicePadding() -> some View {
        self.padding(DeviceTypeDetector.shared.defaultPadding)
    }
    
    /// 强制横屏显示
    func forceLandscape() -> some View {
        self.onAppear {
            AppDelegate.orientationLock = .landscape
            
            // 使用现代API替代已弃用的UIDevice.orientation设置
            if #available(iOS 16.0, *) {
                guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
                    return
                }
                
                // 获取设备的物理方向来决定最合适的横屏方向
                let deviceOrientation = UIDevice.current.orientation
                let currentInterfaceOrientation = windowScene.interfaceOrientation
                let targetOrientation: UIInterfaceOrientationMask
                
                // 优先级：设备物理方向 > 当前界面方向 > 默认左横屏
                if deviceOrientation.isLandscape {
                    // 根据设备物理方向选择对应的界面方向
                    switch deviceOrientation {
                    case .landscapeLeft:
                        // 设备左横屏对应界面右横屏（因为坐标系不同）
                        targetOrientation = .landscapeRight
                    case .landscapeRight:
                        // 设备右横屏对应界面左横屏
                        targetOrientation = .landscapeLeft
                    default:
                        targetOrientation = .landscapeLeft
                    }
                } else if currentInterfaceOrientation.isLandscape {
                    // 如果设备方向不明确但当前已是横屏，保持当前方向
                    targetOrientation = currentInterfaceOrientation == .landscapeLeft ? .landscapeLeft : .landscapeRight
                } else {
                    // 默认使用左横屏
                    targetOrientation = .landscapeLeft
                }
                
                let geometryPreferences = UIWindowScene.GeometryPreferences.iOS(
                    interfaceOrientations: targetOrientation
                )
                
                windowScene.requestGeometryUpdate(geometryPreferences) { error in
                    // requestGeometryUpdate的error参数是非可选的Error类型
                    // 如果有错误，error参数会包含具体的错误信息
                    print("Orientation update error: \(error.localizedDescription)")
                }
            } else {
                // iOS 15及以下版本的兼容性处理
                let deviceOrientation = UIDevice.current.orientation
                let currentInterfaceOrientation = UIApplication.shared.statusBarOrientation
                let targetOrientation: UIInterfaceOrientation
                
                if deviceOrientation.isLandscape {
                    switch deviceOrientation {
                    case .landscapeLeft:
                        targetOrientation = .landscapeRight
                    case .landscapeRight:
                        targetOrientation = .landscapeLeft
                    default:
                        targetOrientation = .landscapeLeft
                    }
                } else if currentInterfaceOrientation.isLandscape {
                    targetOrientation = currentInterfaceOrientation == .landscapeLeft ? .landscapeLeft : .landscapeRight
                } else {
                    targetOrientation = .landscapeLeft
                }
                
                UIDevice.current.setValue(targetOrientation.rawValue, forKey: "orientation")
            }
        }
        .onDisappear {
            AppDelegate.orientationLock = .all
        }
    }
}

// MARK: - App Delegate Extension for Orientation Control

class AppDelegate: NSObject, UIApplicationDelegate {
    static var orientationLock = UIInterfaceOrientationMask.all
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return AppDelegate.orientationLock
    }
}

//
//  CardState.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import Combine

// MARK: - Card Connection State

/// 卡片连接状态
enum CardConnectionState: Equatable {
    case disconnected
    case connecting
    case connected
    case error(AppError)
    case suspended  // 后台挂起状态
    
    static func == (lhs: CardConnectionState, rhs: CardConnectionState) -> <PERSON><PERSON> {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected),
             (.connecting, .connecting),
             (.connected, .connected),
             (.suspended, .suspended):
            return true
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
    
    var isConnected: Bool {
        if case .connected = self {
            return true
        }
        return false
    }
    
    var isConnecting: Bool {
        if case .connecting = self {
            return true
        }
        return false
    }
}

// MARK: - Card Video State

/// 卡片视频状态
enum CardVideoState: Equatable {
    case noVideo
    case loading
    case streaming(quality: VideoQuality)
    case paused
    case error(AppError)
    
    static func == (lhs: CardVideoState, rhs: CardVideoState) -> Bool {
        switch (lhs, rhs) {
        case (.noVideo, .noVideo),
             (.loading, .loading),
             (.paused, .paused):
            return true
        case (.streaming(let lhsQuality), .streaming(let rhsQuality)):
            return lhsQuality == rhsQuality
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
    
    var isStreaming: Bool {
        if case .streaming = self {
            return true
        }
        return false
    }
}

// MARK: - Video Quality

/// 视频质量等级
enum VideoQuality: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case ultra = "ultra"
    
    var displayName: String {
        switch self {
        case .low: return "低质量"
        case .medium: return "中等质量"
        case .high: return "高质量"
        case .ultra: return "超高质量"
        }
    }
    
    var bandwidth: Double {
        switch self {
        case .low: return 0.5    // 0.5 Mbps
        case .medium: return 2.0 // 2 Mbps
        case .high: return 5.0   // 5 Mbps
        case .ultra: return 10.0 // 10 Mbps
        }
    }
    
    var resolution: String {
        switch self {
        case .low: return "480p"
        case .medium: return "720p"
        case .high: return "1080p"
        case .ultra: return "4K"
        }
    }
}

// MARK: - PTZ State

/// PTZ控制状态
enum PTZState: Equatable {
    case idle
    case moving(direction: PTZDirection)
    case zooming(direction: ZoomDirection)
    case error(String)
    
    static func == (lhs: PTZState, rhs: PTZState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle):
            return true
        case (.moving(let lhsDir), .moving(let rhsDir)):
            return lhsDir == rhsDir
        case (.zooming(let lhsDir), .zooming(let rhsDir)):
            return lhsDir == rhsDir
        case (.error(let lhsMsg), .error(let rhsMsg)):
            return lhsMsg == rhsMsg
        default:
            return false
        }
    }
}

enum PTZDirection: String, CaseIterable {
    case up, down, left, right
    case upLeft, upRight, downLeft, downRight
}

enum ZoomDirection: String, CaseIterable {
    case zoomIn, zoomOut
}

// MARK: - Resource Usage

/// 资源使用情况
struct ResourceUsage: Codable {
    var memoryUsage: Double = 0.0      // MB
    var cpuUsage: Double = 0.0         // 百分比
    var bandwidthUsage: Double = 0.0   // Mbps
    var batteryImpact: Double = 0.0    // 百分比
    
    init(memoryUsage: Double = 0.0, cpuUsage: Double = 0.0, bandwidthUsage: Double = 0.0, batteryImpact: Double = 0.0) {
        self.memoryUsage = memoryUsage
        self.cpuUsage = cpuUsage
        self.bandwidthUsage = bandwidthUsage
        self.batteryImpact = batteryImpact
    }
}

// MARK: - Card Priority

/// 卡片优先级
enum CardPriority: Int, CaseIterable, Codable {
    case low = 1
    case normal = 2
    case high = 3
    case critical = 4
    
    var displayName: String {
        switch self {
        case .low: return "低优先级"
        case .normal: return "普通"
        case .high: return "高优先级"
        case .critical: return "关键"
        }
    }
}

// MARK: - Card State

/// 卡片状态管理
class CardState: ObservableObject {
    @Published var connectionState: CardConnectionState = .disconnected
    @Published var videoState: CardVideoState = .noVideo
    @Published var ptzState: PTZState = .idle
    @Published var lastActiveTime: Date = Date()
    @Published var resourceUsage: ResourceUsage = ResourceUsage()
    @Published var priority: CardPriority = .normal
    @Published var isActive: Bool = false
    @Published var isFocused: Bool = false
    
    // 统计信息
    @Published var connectionAttempts: Int = 0
    @Published var totalUptime: TimeInterval = 0
    @Published var lastErrorMessage: String?
    
    private var uptimeTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupStateObservers()
    }
    
    deinit {
        uptimeTimer?.invalidate()
    }
    
    private func setupStateObservers() {
        // 监听连接状态变化
        $connectionState
            .sink { [weak self] state in
                self?.handleConnectionStateChange(state)
            }
            .store(in: &cancellables)
        
        // 监听活跃状态变化
        $isActive
            .sink { [weak self] isActive in
                if isActive {
                    self?.lastActiveTime = Date()
                    self?.startUptimeTimer()
                } else {
                    self?.stopUptimeTimer()
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleConnectionStateChange(_ state: CardConnectionState) {
        switch state {
        case .connecting:
            connectionAttempts += 1
        case .connected:
            lastErrorMessage = nil
            isActive = true
        case .error(let error):
            lastErrorMessage = error.localizedDescription
            isActive = false
        case .disconnected, .suspended:
            isActive = false
        }
    }
    
    private func startUptimeTimer() {
        uptimeTimer?.invalidate()
        uptimeTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.totalUptime += 1.0
        }
    }
    
    private func stopUptimeTimer() {
        uptimeTimer?.invalidate()
        uptimeTimer = nil
    }
    
    // MARK: - Public Methods
    
    func updateResourceUsage(_ usage: ResourceUsage) {
        DispatchQueue.main.async {
            self.resourceUsage = usage
        }
    }
    
    func markAsActive() {
        DispatchQueue.main.async {
            self.isActive = true
            self.lastActiveTime = Date()
        }
    }
    
    func markAsInactive() {
        DispatchQueue.main.async {
            self.isActive = false
        }
    }
    
    func focus() {
        DispatchQueue.main.async {
            self.isFocused = true
            self.markAsActive()
        }
    }
    
    func unfocus() {
        DispatchQueue.main.async {
            self.isFocused = false
        }
    }
    
    func reset() {
        DispatchQueue.main.async {
            self.connectionState = .disconnected
            self.videoState = .noVideo
            self.ptzState = .idle
            self.isActive = false
            self.isFocused = false
            self.connectionAttempts = 0
            self.totalUptime = 0
            self.lastErrorMessage = nil
            self.resourceUsage = ResourceUsage()
        }
    }
}

// MARK: - App Error

/// 应用错误类型
enum AppError: Error, LocalizedError {
    case networkError(Error)
    case connectionTimeout
    case invalidConfiguration
    case resourceExhausted
    case deviceNotFound
    case authenticationFailed
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .connectionTimeout:
            return "连接超时"
        case .invalidConfiguration:
            return "配置无效"
        case .resourceExhausted:
            return "系统资源不足"
        case .deviceNotFound:
            return "设备未找到"
        case .authenticationFailed:
            return "认证失败"
        case .unknown(let message):
            return "未知错误: \(message)"
        }
    }
}

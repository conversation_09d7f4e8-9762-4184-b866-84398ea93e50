//
//  CameraCardManager.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import Combine
import SwiftUI

// MARK: - Camera Card Manager

/// 摄像机卡片管理器 - 管理所有摄像机卡片的生命周期
@MainActor
class CameraCardManager: ObservableObject {
    static let shared = CameraCardManager()
    
    // MARK: - Published Properties
    @Published var activeCards: [CameraCard] = []
    @Published var suspendedCards: [CameraCard] = []
    @Published var availableDevices: [CameraDevice] = []
    @Published var primaryCard: CameraCard?
    @Published var isDiscoveryVisible: Bool = false
    
    // 当前活跃的卡片（用于PTZ控制）
    @Published var currentActiveCard: CameraCard?
    
    // 统计信息
    @Published var totalConnectedCameras: Int = 0
    @Published var totalBandwidthUsage: Double = 0.0
    @Published var systemResourceUsage: ResourceUsage = ResourceUsage()
    
    // MARK: - Private Properties
    private let maxConcurrentCards = 4
    private let resourceManager = ResourceManager.shared
    private let serviceDiscovery = EagleServiceDiscovery.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    private init() {
        setupServiceDiscoveryBinding()
        setupResourceMonitoring()
    }
    
    private func setupServiceDiscoveryBinding() {
        // 监听设备发现结果
        serviceDiscovery.$discoveredIPs
            .map { ips in
                ips.map { ip in
                    let cleanIP = ip.components(separatedBy: ":").first ?? ip
                    return CameraDevice(
                        name: "P2-R1 Camera",
                        ipAddress: cleanIP,
                        port: 80
                    )
                }
            }
            .assign(to: \.availableDevices, on: self)
            .store(in: &cancellables)
    }
    
    private func setupResourceMonitoring() {
        // 定期更新资源使用情况
        Timer.publish(every: 2.0, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.updateResourceUsage()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Card Management
    
    func createCard(for device: CameraDevice, configuration: CardConfiguration = CardConfiguration()) -> CameraCard {
        print("[CameraCardManager] 🆕 Creating card for \(device.name) at \(device.ipAddress)")
        
        let card = CameraCard(device: device, configuration: configuration)
        
        // 设置卡片状态监听
        setupCardStateBinding(card)
        
        return card
    }
    
    func addCard(_ card: CameraCard) {
        guard !activeCards.contains(where: { $0.id == card.id }) else {
            print("[CameraCardManager] ⚠️ Card already exists: \(card.name)")
            return
        }
        
        if activeCards.count >= maxConcurrentCards {
            print("[CameraCardManager] ⚠️ Maximum concurrent cards reached, suspending oldest card")
            suspendOldestCard()
        }
        
        activeCards.append(card)
        
        // 如果是第一张卡片，设为主卡片
        if primaryCard == nil {
            primaryCard = card
            currentActiveCard = card
        }
        
        updateStatistics()
        print("[CameraCardManager] ✅ Card added: \(card.name)")
    }
    
    func removeCard(_ card: CameraCard) {
        Task {
            await card.disconnect()
        }
        
        activeCards.removeAll { $0.id == card.id }
        suspendedCards.removeAll { $0.id == card.id }
        
        // 更新主卡片
        if primaryCard?.id == card.id {
            primaryCard = activeCards.first
            currentActiveCard = primaryCard
        }
        
        // 更新当前活跃卡片
        if currentActiveCard?.id == card.id {
            currentActiveCard = activeCards.first
        }
        
        updateStatistics()
        print("[CameraCardManager] 🗑️ Card removed: \(card.name)")
    }
    
    func connectCard(_ card: CameraCard) async {
        guard resourceManager.canCreateNewConnection() else {
            print("[CameraCardManager] ⚠️ Cannot connect card: resource limit reached")
            return
        }
        
        do {
            try await card.connect()
            
            if !activeCards.contains(where: { $0.id == card.id }) {
                addCard(card)
            }
            
            print("[CameraCardManager] ✅ Card connected: \(card.name)")
        } catch {
            print("[CameraCardManager] ❌ Failed to connect card \(card.name): \(error)")
        }
    }
    
    func disconnectCard(_ card: CameraCard) async {
        await card.disconnect()
        removeCard(card)
    }
    
    func suspendCard(_ card: CameraCard) async {
        await card.suspend()
        
        if let index = activeCards.firstIndex(where: { $0.id == card.id }) {
            let suspendedCard = activeCards.remove(at: index)
            suspendedCards.append(suspendedCard)
        }
        
        updateStatistics()
        print("[CameraCardManager] ⏸️ Card suspended: \(card.name)")
    }
    
    func resumeCard(_ card: CameraCard) async {
        guard activeCards.count < maxConcurrentCards else {
            print("[CameraCardManager] ⚠️ Cannot resume card: active cards limit reached")
            return
        }
        
        do {
            try await card.resume()
            
            if let index = suspendedCards.firstIndex(where: { $0.id == card.id }) {
                let resumedCard = suspendedCards.remove(at: index)
                activeCards.append(resumedCard)
            }
            
            updateStatistics()
            print("[CameraCardManager] ▶️ Card resumed: \(card.name)")
        } catch {
            print("[CameraCardManager] ❌ Failed to resume card \(card.name): \(error)")
        }
    }
    
    private func suspendOldestCard() {
        guard let oldestCard = activeCards.min(by: { $0.state.lastActiveTime < $1.state.lastActiveTime }) else {
            return
        }
        
        Task {
            await suspendCard(oldestCard)
        }
    }
    
    // MARK: - Card Selection and Focus
    
    func setActiveCard(_ card: CameraCard) {
        // 取消之前活跃卡片的焦点
        currentActiveCard?.state.unfocus()
        
        // 设置新的活跃卡片
        currentActiveCard = card
        card.state.focus()
        
        print("[CameraCardManager] 🎯 Active card changed to: \(card.name)")
    }
    
    func setPrimaryCard(_ card: CameraCard) {
        primaryCard = card
        setActiveCard(card)
        print("[CameraCardManager] ⭐ Primary card set to: \(card.name)")
    }
    
    // MARK: - Device Discovery
    
    func startDiscovery() {
        serviceDiscovery.startManualScan()
        isDiscoveryVisible = true
        print("[CameraCardManager] 🔍 Device discovery started")
    }
    
    func stopDiscovery() {
        serviceDiscovery.stopScanning()
        isDiscoveryVisible = false
        print("[CameraCardManager] 🛑 Device discovery stopped")
    }
    
    func toggleDiscovery() {
        if isDiscoveryVisible {
            stopDiscovery()
        } else {
            startDiscovery()
        }
    }
    
    // MARK: - PTZ Control Delegation
    
    func executePTZMove(panSpeed: Float, tiltSpeed: Float) {
        guard let activeCard = currentActiveCard else {
            print("[CameraCardManager] ⚠️ No active card for PTZ control")
            return
        }
        
        activeCard.executePTZMove(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
    }
    
    func executeZoomAction(_ direction: ZoomDirection, speed: Float = 0.5) {
        guard let activeCard = currentActiveCard else {
            print("[CameraCardManager] ⚠️ No active card for zoom control")
            return
        }
        
        activeCard.executeZoomAction(direction, speed: speed)
    }
    
    func stopPTZ() {
        currentActiveCard?.stopPTZ()
    }
    
    // MARK: - Resource Management
    
    private func updateResourceUsage() {
        totalConnectedCameras = activeCards.filter { $0.isConnected }.count
        totalBandwidthUsage = activeCards.reduce(0) { $0 + $1.state.resourceUsage.bandwidthUsage }
        
        // 更新系统资源使用情况
        let totalMemory = activeCards.reduce(0) { $0 + $1.state.resourceUsage.memoryUsage }
        let totalCPU = activeCards.reduce(0) { $0 + $1.state.resourceUsage.cpuUsage }
        let totalBattery = activeCards.reduce(0) { $0 + $1.state.resourceUsage.batteryImpact }
        
        systemResourceUsage = ResourceUsage(
            memoryUsage: totalMemory,
            cpuUsage: min(totalCPU, 100.0),
            bandwidthUsage: totalBandwidthUsage,
            batteryImpact: min(totalBattery, 100.0)
        )
        
        // 更新各卡片的资源使用情况
        for card in activeCards {
            card.updateResourceUsage()
        }
    }
    
    private func updateStatistics() {
        totalConnectedCameras = activeCards.filter { $0.isConnected }.count
    }
    
    private func setupCardStateBinding(_ card: CameraCard) {
        // 监听卡片连接状态变化
        card.state.$connectionState
            .sink { [weak self] state in
                if case .error = state {
                    // 连接错误时的处理逻辑
                    self?.handleCardConnectionError(card)
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleCardConnectionError(_ card: CameraCard) {
        print("[CameraCardManager] ❌ Card connection error: \(card.name)")
        
        // 如果启用了自动重连
        if card.configuration.autoReconnect {
            Task {
                try? await Task.sleep(nanoseconds: 3_000_000_000) // 3秒后重试
                await connectCard(card)
            }
        }
    }
    
    // MARK: - Convenience Methods
    
    func getAllCards() -> [CameraCard] {
        return activeCards + suspendedCards
    }
    
    func getCard(by id: UUID) -> CameraCard? {
        return getAllCards().first { $0.id == id }
    }
    
    func getCard(by ipAddress: String) -> CameraCard? {
        return getAllCards().first { $0.ipAddress == ipAddress }
    }
    
    func disconnectAllCards() async {
        await withTaskGroup(of: Void.self) { group in
            for card in getAllCards() {
                group.addTask {
                    await card.disconnect()
                }
            }
        }
        
        activeCards.removeAll()
        suspendedCards.removeAll()
        primaryCard = nil
        currentActiveCard = nil
        
        updateStatistics()
        print("[CameraCardManager] 🔌 All cards disconnected")
    }
}

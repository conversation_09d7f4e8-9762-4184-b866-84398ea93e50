//
//  CameraCard.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import Foundation
import Combine
import SwiftUI

// MARK: - Camera Card

/// 摄像机卡片模型 - 每张卡片代表一台P2-R1摄像机
class CameraCard: ObservableObject, Identifiable {
    let id = UUID()
    let deviceInfo: CameraDevice
    
    // 卡片状态
    @Published var state: CardState
    @Published var configuration: CardConfiguration
    
    // 独立的连接组件
    private var apiClient: CardAPIClient?
    private var webSocketManager: CardWebSocketManager?
    private var webRTCClient: CardWebRTCClient?
    private var ptzManager: CardPTZManager?
    
    // 便捷访问属性
    var ipAddress: String { deviceInfo.ipAddress }
    var name: String { deviceInfo.name }
    var isConnected: Bool { state.connectionState.isConnected }
    var isActive: Bool { state.isActive }
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init(device: CameraDevice, configuration: CardConfiguration = CardConfiguration()) {
        self.deviceInfo = device
        self.state = CardState()
        self.configuration = configuration
        
        setupComponents()
        setupStateBindings()
    }
    
    deinit {
        Task {
            await disconnect()
        }
    }
    
    private func setupComponents() {
        // 创建独立的网络组件实例
        self.apiClient = CardAPIClient(cardId: id, ipAddress: deviceInfo.ipAddress, port: deviceInfo.port)
        self.webSocketManager = CardWebSocketManager(cardId: id, ipAddress: deviceInfo.ipAddress)
        self.webRTCClient = CardWebRTCClient(cardId: id, ipAddress: deviceInfo.ipAddress)
        
        // 创建PTZ管理器
        if let apiClient = self.apiClient {
            self.ptzManager = CardPTZManager(cardId: id, apiClient: apiClient)
        }
    }
    
    private func setupStateBindings() {
        // 监听各组件状态变化并更新卡片状态
        setupAPIClientBindings()
        setupWebSocketBindings()
        setupWebRTCBindings()
    }
    
    private func setupAPIClientBindings() {
        guard let apiClient = apiClient else { return }
        
        apiClient.$isConnected
            .sink { [weak self] isConnected in
                DispatchQueue.main.async {
                    if isConnected {
                        self?.state.connectionState = .connected
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupWebSocketBindings() {
        guard let webSocketManager = webSocketManager else { return }
        
        webSocketManager.$connectionState
            .sink { [weak self] wsState in
                DispatchQueue.main.async {
                    // WebSocket状态变化不直接影响卡片连接状态
                    // 只在完全断开时更新
                    if case .disconnected = wsState {
                        // 仅在其他连接也断开时才标记为断开
                        self?.updateOverallConnectionState()
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupWebRTCBindings() {
        guard let webRTCClient = webRTCClient else { return }
        
        webRTCClient.$connectionState
            .sink { [weak self] rtcState in
                DispatchQueue.main.async {
                    switch rtcState {
                    case .connected:
                        self?.state.videoState = .streaming(quality: self?.configuration.maxVideoQuality ?? .medium)
                    case .connecting:
                        self?.state.videoState = .loading
                    case .disconnected:
                        self?.state.videoState = .noVideo
                    case .failed(let error):
                        self?.state.videoState = .error(AppError.networkError(error))
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func updateOverallConnectionState() {
        let apiConnected = apiClient?.isConnected ?? false
        let webRTCConnected = webRTCClient?.isConnected ?? false
        
        if apiConnected || webRTCConnected {
            state.connectionState = .connected
        } else {
            state.connectionState = .disconnected
        }
    }
    
    // MARK: - Connection Management
    
    @MainActor
    func connect() async throws {
        guard state.connectionState != .connected && state.connectionState != .connecting else {
            print("[CameraCard] \(name) already connected or connecting")
            return
        }
        
        print("[CameraCard] 🔗 Connecting to \(name) at \(ipAddress)")
        state.connectionState = .connecting
        
        do {
            // 并发连接各个组件
            async let apiConnection = connectAPI()
            async let webSocketConnection = connectWebSocket()
            async let webRTCConnection = connectWebRTC()
            
            // 等待所有连接完成
            let (apiResult, _, rtcResult) = await (
                try? apiConnection,
                try? webSocketConnection,
                try? webRTCConnection
            )
            
            // 检查连接结果
            if apiResult == true || rtcResult == true {
                state.connectionState = .connected
                state.markAsActive()
                print("[CameraCard] ✅ \(name) connected successfully")
            } else {
                throw AppError.connectionTimeout
            }
            
        } catch {
            state.connectionState = .error(AppError.networkError(error))
            print("[CameraCard] ❌ Failed to connect \(name): \(error)")
            throw error
        }
    }
    
    @MainActor
    func disconnect() async {
        print("[CameraCard] 🔌 Disconnecting \(name)")
        
        state.connectionState = .disconnected
        state.markAsInactive()
        
        // 断开所有连接
        await withTaskGroup(of: Void.self) { group in
            group.addTask { [weak self] in
                await self?.apiClient?.disconnect()
            }
            group.addTask { [weak self] in
                await self?.webSocketManager?.disconnect()
            }
            group.addTask { [weak self] in
                await self?.webRTCClient?.disconnect()
            }
        }
        
        print("[CameraCard] ✅ \(name) disconnected")
    }
    
    @MainActor
    func suspend() async {
        print("[CameraCard] ⏸️ Suspending \(name)")
        
        state.connectionState = .suspended
        state.markAsInactive()
        
        // 暂停视频流但保持API连接
        await webRTCClient?.disconnect()
        
        print("[CameraCard] ✅ \(name) suspended")
    }
    
    @MainActor
    func resume() async throws {
        print("[CameraCard] ▶️ Resuming \(name)")
        
        guard state.connectionState == .suspended else {
            print("[CameraCard] ⚠️ \(name) is not in suspended state")
            return
        }
        
        state.connectionState = .connecting
        
        do {
            // 恢复WebRTC连接
            let rtcResult = try await connectWebRTC()
            
            if rtcResult {
                state.connectionState = .connected
                state.markAsActive()
                print("[CameraCard] ✅ \(name) resumed successfully")
            } else {
                throw AppError.connectionTimeout
            }
        } catch {
            state.connectionState = .error(AppError.networkError(error))
            throw error
        }
    }
    
    // MARK: - Private Connection Methods
    
    private func connectAPI() async throws -> Bool {
        guard let apiClient = apiClient else { return false }
        return await apiClient.connect()
    }
    
    private func connectWebSocket() async throws -> Bool {
        guard let webSocketManager = webSocketManager else { return false }
        return await webSocketManager.connect()
    }
    
    private func connectWebRTC() async throws -> Bool {
        guard let webRTCClient = webRTCClient else { return false }
        return await webRTCClient.connect()
    }
    
    // MARK: - PTZ Control

    func executePTZMove(panSpeed: Float, tiltSpeed: Float) {
        guard isConnected else {
            print("[CameraCard] ⚠️ \(name) not connected, PTZ command ignored")
            return
        }

        ptzManager?.executePTZMove(panSpeed: panSpeed, tiltSpeed: tiltSpeed)

        // 更新PTZ状态
        if abs(panSpeed) > 0.01 || abs(tiltSpeed) > 0.01 {
            let direction = determinePTZDirection(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
            state.ptzState = .moving(direction: direction)
        } else {
            state.ptzState = .idle
        }
    }

    func executeZoomAction(_ direction: ZoomDirection, speed: Float = 0.5) {
        guard isConnected else {
            print("[CameraCard] ⚠️ \(name) not connected, zoom command ignored")
            return
        }

        ptzManager?.executeZoomAction(direction, speed: speed)
        state.ptzState = .zooming(direction: direction)
    }

    func stopPTZ() {
        ptzManager?.stopMovement()
        state.ptzState = .idle
    }

    private func determinePTZDirection(panSpeed: Float, tiltSpeed: Float) -> PTZDirection {
        if abs(panSpeed) > abs(tiltSpeed) {
            return panSpeed > 0 ? .right : .left
        } else {
            return tiltSpeed > 0 ? .up : .down
        }
    }

    // MARK: - Video Stream Access

    func getVideoView() -> AnyView? {
        guard let webRTCClient = webRTCClient else { return nil }
        return webRTCClient.getVideoView()
    }
    
    // MARK: - Resource Management
    
    func updateResourceUsage() {
        // 计算当前资源使用情况
        let memoryUsage = calculateMemoryUsage()
        let cpuUsage = calculateCPUUsage()
        let bandwidthUsage = calculateBandwidthUsage()
        let batteryImpact = calculateBatteryImpact()
        
        let usage = ResourceUsage(
            memoryUsage: memoryUsage,
            cpuUsage: cpuUsage,
            bandwidthUsage: bandwidthUsage,
            batteryImpact: batteryImpact
        )
        
        state.updateResourceUsage(usage)
    }
    
    private func calculateMemoryUsage() -> Double {
        // 估算内存使用（实际实现中可以使用系统API获取）
        var usage: Double = 10.0 // 基础内存使用
        
        if state.videoState.isStreaming {
            usage += configuration.maxVideoQuality.bandwidth * 2.0 // 视频缓冲
        }
        
        return usage
    }
    
    private func calculateCPUUsage() -> Double {
        // 估算CPU使用
        var usage: Double = 2.0 // 基础CPU使用
        
        if state.videoState.isStreaming {
            usage += configuration.maxVideoQuality.bandwidth * 0.5
        }
        
        return min(usage, 100.0)
    }
    
    private func calculateBandwidthUsage() -> Double {
        if state.videoState.isStreaming {
            return configuration.maxVideoQuality.bandwidth
        }
        return 0.1 // 基础网络使用
    }
    
    private func calculateBatteryImpact() -> Double {
        var impact: Double = 1.0 // 基础电池影响
        
        if state.videoState.isStreaming {
            impact += configuration.maxVideoQuality.bandwidth * 0.3
        }
        
        return min(impact, 100.0)
    }
}

// MARK: - Card Configuration

/// 卡片配置
struct CardConfiguration: Codable {
    var maxVideoQuality: VideoQuality = .medium
    var enableAIDetection: Bool = true
    var ptzSensitivity: Float = 0.5
    var autoReconnect: Bool = true
    var resourcePriority: CardPriority = .normal
    var customAPIEndpoints: [String: String] = [:]
    
    init(
        maxVideoQuality: VideoQuality = .medium,
        enableAIDetection: Bool = true,
        ptzSensitivity: Float = 0.5,
        autoReconnect: Bool = true,
        resourcePriority: CardPriority = .normal,
        customAPIEndpoints: [String: String] = [:]
    ) {
        self.maxVideoQuality = maxVideoQuality
        self.enableAIDetection = enableAIDetection
        self.ptzSensitivity = ptzSensitivity
        self.autoReconnect = autoReconnect
        self.resourcePriority = resourcePriority
        self.customAPIEndpoints = customAPIEndpoints
    }
}

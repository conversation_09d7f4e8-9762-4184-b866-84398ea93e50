//
//  SettingsViews.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI

// MARK: - Camera Settings View

struct CameraSettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    
    var body: some View {
        Form {
            Section("Connection") {
                FormTextField(
                    title: "Default IP Address",
                    text: $appSettings.defaultCameraIP,
                    placeholder: "192.168.x.x"
                )
                
                HStack {
                    Text("Default Port")
                    Spacer()
                    TextField("Port", value: $appSettings.defaultCameraPort, format: .number)
                        .keyboardType(.numberPad)
                        .multilineTextAlignment(.trailing)
                }
                
                FormToggle(
                    title: "Auto Connect",
                    description: "Automatically connect to the last used camera",
                    isOn: $appSettings.autoConnectCamera
                )
            }
            
            Section("Timeout") {
                HStack {
                    Text("Connection Timeout")
                    Spacer()
                    Text("\(Int(appSettings.cameraConnectionTimeout))s")
                        .foregroundColor(.secondary)
                }
            }
        }
        .navigationTitle("Camera Settings")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Connection Settings View

struct ConnectionSettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    
    var body: some View {
        Form {
            Section("Network") {
                FormToggle(
                    title: "Use HTTPS",
                    description: "Use secure connection when available",
                    isOn: $appSettings.useHTTPS
                )
                
                FormToggle(
                    title: "Verify SSL Certificate",
                    description: "Verify server SSL certificates",
                    isOn: $appSettings.verifySSLCertificate
                )
            }
            
            Section("Retry Settings") {
                HStack {
                    Text("Max Retry Attempts")
                    Spacer()
                    Text("\(appSettings.maxRetryAttempts)")
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("Retry Delay")
                    Spacer()
                    Text("\(Int(appSettings.retryDelay))s")
                        .foregroundColor(.secondary)
                }
            }
        }
        .navigationTitle("Connection Settings")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Stream Quality Settings View

struct StreamQualitySettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    
    private let qualityOptions = ["low", "medium", "high", "ultra"]
    
    var body: some View {
        Form {
            Section("Video Quality") {
                Picker("Default Quality", selection: $appSettings.defaultStreamQuality) {
                    ForEach(qualityOptions, id: \.self) { quality in
                        Text(quality.capitalized).tag(quality)
                    }
                }
                .pickerStyle(.segmented)
                
                FormToggle(
                    title: "Hardware Acceleration",
                    description: "Use hardware acceleration for video decoding",
                    isOn: $appSettings.enableHardwareAcceleration
                )
            }
            
            Section("Performance") {
                FormToggle(
                    title: "Adaptive Quality",
                    description: "Automatically adjust quality based on network conditions",
                    isOn: $appSettings.enableAdaptiveQuality
                )
                
                HStack {
                    Text("Buffer Size")
                    Spacer()
                    Text("\(appSettings.bufferSize)MB")
                        .foregroundColor(.secondary)
                }
            }
        }
        .navigationTitle("Stream Quality")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Download Settings View

struct DownloadSettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    
    var body: some View {
        Form {
            Section("Download Location") {
                HStack {
                    Text("Save to Photos")
                    Spacer()
                    Toggle("", isOn: $appSettings.saveToPhotos)
                }
                
                if !appSettings.saveToPhotos {
                    HStack {
                        Text("Custom Path")
                        Spacer()
                        Text("Documents/wbCamer")
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Section("Download Options") {
                FormToggle(
                    title: "Auto Download",
                    description: "Automatically download new files",
                    isOn: $appSettings.autoDownload
                )
                
                FormToggle(
                    title: "Download Over Cellular",
                    description: "Allow downloads when using cellular data",
                    isOn: $appSettings.allowCellularDownload
                )
            }
        }
        .navigationTitle("Download Settings")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Storage Management View

struct StorageManagementView: View {
    @State private var storageInfo: StorageInfo?
    @State private var isLoading = true
    
    var body: some View {
        Form {
            if isLoading {
                Section {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Loading storage information...")
                            .foregroundColor(.secondary)
                    }
                }
            } else if let storage = storageInfo {
                Section("Storage Usage") {
                    StorageUsageRow(title: "Total Space", size: storage.totalSpace)
                    StorageUsageRow(title: "Used Space", size: storage.usedSpace)
                    StorageUsageRow(title: "Available Space", size: storage.availableSpace)
                }
                
                Section("Cleanup") {
                    Button("Clear Cache") {
                        clearCache()
                    }
                    
                    Button("Delete Downloaded Files") {
                        deleteDownloadedFiles()
                    }
                    .foregroundColor(.red)
                }
            }
        }
        .navigationTitle("Storage Management")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            loadStorageInfo()
        }
    }
    
    private func loadStorageInfo() {
        // Simulate loading storage info
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            storageInfo = StorageInfo(
                totalSpace: 64_000_000_000,
                usedSpace: 32_000_000_000,
                availableSpace: 32_000_000_000,
                recordingTimeRemaining: 3600.0 // 1 hour of recording time remaining
            )
            isLoading = false
        }
    }
    
    private func clearCache() {
        // Implement cache clearing
    }
    
    private func deleteDownloadedFiles() {
        // Implement file deletion
    }
}

struct StorageUsageRow: View {
    let title: String
    let size: Int64
    
    var body: some View {
        HStack {
            Text(title)
            Spacer()
            Text(formatFileSize(size))
                .foregroundColor(.secondary)
        }
    }
    
    private func formatFileSize(_ size: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useGB, .useTB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }
}

// MARK: - Appearance Settings View

struct AppearanceSettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    
    private let themeOptions = ["system", "light", "dark"]
    
    var body: some View {
        Form {
            Section("Theme") {
                Picker("App Theme", selection: $appSettings.appTheme) {
                    ForEach(themeOptions, id: \.self) { theme in
                        Text(theme.capitalized).tag(theme)
                    }
                }
                .pickerStyle(.segmented)
            }
            
            Section("Interface") {
                FormToggle(
                    title: "Show Status Bar",
                    description: "Display connection status in the status bar",
                    isOn: $appSettings.showStatusBar
                )
                
                FormToggle(
                    title: "Haptic Feedback",
                    description: "Provide haptic feedback for interactions",
                    isOn: $appSettings.enableHapticFeedback
                )
            }
        }
        .navigationTitle("Appearance")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Notification Settings View

struct NotificationSettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    
    var body: some View {
        Form {
            Section("Recording Notifications") {
                FormToggle(
                    title: "Recording Started",
                    description: "Notify when recording starts",
                    isOn: $appSettings.notifyRecordingStart
                )
                
                FormToggle(
                    title: "Recording Stopped",
                    description: "Notify when recording stops",
                    isOn: $appSettings.notifyRecordingStop
                )
            }
            
            Section("Connection Notifications") {
                FormToggle(
                    title: "Connection Lost",
                    description: "Notify when camera connection is lost",
                    isOn: $appSettings.notifyConnectionLost
                )
                
                FormToggle(
                    title: "Connection Restored",
                    description: "Notify when camera connection is restored",
                    isOn: $appSettings.notifyConnectionRestored
                )
            }
        }
        .navigationTitle("Notifications")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Privacy Settings View

struct PrivacySettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    
    var body: some View {
        Form {
            Section("Data Collection") {
                FormToggle(
                    title: "Analytics",
                    description: "Help improve the app by sharing anonymous usage data",
                    isOn: $appSettings.enableAnalytics
                )
                
                FormToggle(
                    title: "Crash Reports",
                    description: "Automatically send crash reports to help fix issues",
                    isOn: $appSettings.enableCrashReports
                )
            }
            
            Section("Security") {
                FormToggle(
                    title: "Require Authentication",
                    description: "Require Face ID/Touch ID to access the app",
                    isOn: $appSettings.requireAuthentication
                )
            }
        }
        .navigationTitle("Privacy & Security")
        .navigationBarTitleDisplayMode(.inline)
    }
}



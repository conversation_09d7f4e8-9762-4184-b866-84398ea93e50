//
//  LandscapeMainView.swift
//  wbCamer
//
//  Created by AI Assistant on 2025/6/30.
//  Updated for Multi-Camera Card Architecture on 2025/7/16.
//

import SwiftUI

// MARK: - 横屏主界面 (多摄像机卡片架构)

struct LandscapeMainView: View {
    @StateObject private var cardManager = CameraCardManager.shared
    @StateObject private var networkPermissionManager = NetworkPermissionManager()

    var body: some View {
        ZStack {
            // 主要内容 - 多卡片布局
            MultiCardLayoutView(cardManager: cardManager)

            // 网络权限提示（如果需要）
            if networkPermissionManager.hasCompletedInitialCheck && !networkPermissionManager.hasLocalNetworkPermission {
                NetworkPermissionOverlay(networkPermissionManager: networkPermissionManager)
                    .zIndex(1)
            }
        }
        .environment(\.deviceType, DeviceTypeDetector.shared.deviceType)
        .onAppear {
            // 应用启动时的初始化
            setupInitialState()
        }
    }

    // MARK: - Private Methods

    private func setupInitialState() {
        // 延迟执行权限检查
        networkPermissionManager.performDelayedInitialCheck()

        // 初始化资源管理器
        _ = ResourceManager.shared
        _ = NetworkBandwidthManager.shared

        print("[LandscapeMainView] 🚀 Multi-camera card architecture initialized")
    }
}

// MARK: - Network Permission Overlay

/// 网络权限覆盖层
struct NetworkPermissionOverlay: View {
    @ObservedObject var networkPermissionManager: NetworkPermissionManager

    var body: some View {
        ZStack {
            Color.black.opacity(0.7)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                Image(systemName: "wifi.exclamationmark")
                    .font(.system(size: 60))
                    .foregroundColor(.orange)

                VStack(spacing: 12) {
                    Text("需要本地网络访问权限")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text("wbCamer需要访问本地网络来发现和连接摄像机设备")
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.horizontal, 40)
                }

                VStack(spacing: 12) {
                    Button("检查权限") {
                        networkPermissionManager.requestPermissionIfNeeded()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)

                    Button("稍后设置") {
                        // 暂时关闭覆盖层
                        networkPermissionManager.hasCompletedInitialCheck = false
                    }
                    .buttonStyle(.bordered)
                    .foregroundColor(.white)
                }
            }
            .padding(40)
            .background(Color(.systemBackground).opacity(0.95))
            .cornerRadius(20)
            .shadow(radius: 20)
            .padding(40)
        }
    }
}

#Preview {
    LandscapeMainView()
}

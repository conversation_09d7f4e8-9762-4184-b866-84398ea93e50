//
//  LandscapeMainView.swift
//  wbCamer
//
//  Created by AI Assistant on 2025/6/30.
//

import SwiftUI

// MARK: - 横屏主界面

struct LandscapeMainView: View {
    @StateObject private var deviceDetector = DeviceTypeDetector.shared
    @StateObject private var cameraManager = CameraManager.shared
    @ObservedObject private var serviceDiscovery = EagleServiceDiscovery.shared
    @StateObject private var networkPermissionManager = NetworkPermissionManager()
    
    var body: some View {
        GeometryReader { geometry in
            Group {
                if cameraManager.shouldShowVideoInterface {
                    // 视频界面 - 基于WebRTC连接状态显示
                    if deviceDetector.isPhone {
                        iPhoneLiveView(geometry: geometry)
                    } else {
                        iPadLiveView(geometry: geometry)
                    }
                } else {
                    // 设备发现界面 - WebRTC未连接时显示
                    if deviceDetector.isPhone {
                        iPhoneDiscoveryView(geometry: geometry)
                    } else {
                        iPadDiscoveryView(geometry: geometry)
                    }

                    // 如果相机已连接但WebRTC未连接，显示连接状态提示
                    if cameraManager.isConnected && !cameraManager.shouldShowVideoInterface {
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                VStack(spacing: 8) {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(1.2)
                                    Text("正在建立视频连接...")
                                        .font(.caption)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(Color.black.opacity(0.7))
                                        .cornerRadius(8)
                                }
                                Spacer()
                            }
                            Spacer()
                        }
                    }
                }
            }
        }
        .forceLandscape()
        .ignoresSafeArea(.all)
        .background(Color.black)
        .onAppear {
            // 延迟执行权限检查
            networkPermissionManager.performDelayedInitialCheck()
        }
    }
}

// MARK: - iPhone 布局

extension LandscapeMainView {
    
    @ViewBuilder
    private func iPhoneLiveView(geometry: GeometryProxy) -> some View {
        ZStack {
            // 视频预览区域 - 全屏
            CameraStreamView()
                .frame(width: geometry.size.width, height: geometry.size.height)

            // 顶部状态栏
            VStack {
                HStack {
                    Spacer()

                    // 右上角扫描控制按钮
                    ScanToggleButton()
                        .padding(.top, 20)
                        .padding(.trailing, 20)
                }
                Spacer()
            }
            
            // 底部虚拟摇杆
            VStack {
                Spacer()
                HStack {
                    // 左下角 PTZ 摇杆
                    VStack(spacing: 4) {
                        VirtualJoystick(
                            size: deviceDetector.joystickSize,
                            style: .ptz,
                            onDirectionChange: { direction in
                                handlePTZControl(direction: direction)
                            },
                            onSpeedChange: { panSpeed, tiltSpeed in
                                handlePTZSpeedControl(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
                            }
                        )
                        Text("Pan-Tilt")
                            .font(.caption2)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }
                    .padding(.leading, deviceDetector.joystickPadding + 100)  // 向中间移动100像素
                    .padding(.bottom, deviceDetector.joystickPadding)

                    Spacer()

                    // 右下角变焦摇杆
                    VStack(spacing: 4) {
                        VirtualJoystick(
                            size: deviceDetector.joystickSize,
                            style: .zoom,
                            onDirectionChange: { direction in
                                handleZoomControl(direction: direction)
                            },
                            onSpeedChange: { panSpeed, tiltSpeed in
                                handleZoomSpeedControl(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
                            }
                        )
                        Text("Zoom")
                            .font(.caption2)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }
                    .padding(.trailing, deviceDetector.joystickPadding + 100)  // 向中间移动100像素
                    .padding(.bottom, deviceDetector.joystickPadding)
                }
            }
            
            // 注意：扫描控制已通过右上角的ScanToggleButton处理
        }
    }
    
    @ViewBuilder
    private func iPhoneDiscoveryView(geometry: GeometryProxy) -> some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // 居中的设备发现区域
            VStack(spacing: 24) {
                Spacer()

                // 标题
                VStack(spacing: 12) {
                    Image(systemName: "camera.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.blue)

                    Text("Eagle Camera")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                }

                // 扫描控制
                ScanControlSection()
                    .padding(.horizontal, 40)

                // 设备列表
                DeviceListSection()
                    .frame(maxHeight: geometry.size.height * 0.4)
                    .padding(.horizontal, 40)

                Spacer()
            }
            .frame(maxWidth: .infinity)
        }
    }
}

// MARK: - iPad 布局

extension LandscapeMainView {

    @ViewBuilder
    private func iPadLiveView(geometry: GeometryProxy) -> some View {
        ZStack {
            VStack(spacing: 0) {
                // 上部：视频预览区域 (16:9比例)
                let videoHeight = min(geometry.size.height * 0.7, geometry.size.width * 9 / 16)
                CameraStreamView()
                    .frame(width: videoHeight * 16 / 9, height: videoHeight)
                    .frame(maxWidth: geometry.size.width)
                    .clipped()

                Spacer()

                // 下部：控制区域
                HStack(spacing: 0) {
                    // 左下角 PTZ 摇杆
                    VStack {
                        VStack(spacing: 4) {
                            VirtualJoystick(
                                size: deviceDetector.joystickSize,
                                style: .ptz,
                                onDirectionChange: { direction in
                                    handlePTZControl(direction: direction)
                                },
                                onSpeedChange: { panSpeed, tiltSpeed in
                                    handlePTZSpeedControl(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
                                }
                            )
                            Text("PTZ")
                                .font(.caption2)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                        }
                        .padding(.leading, deviceDetector.joystickPadding + 60)  // 减少向中间移动的距离

                        Spacer()
                    }
                    .frame(width: geometry.size.width * 0.25)

                    // 中央控制面板
                    VStack(spacing: 16) {
                        // 扫描控制
                        ScanControlSection()

                        // 设备列表（紧凑模式，完全居中显示）
                        CompactDeviceListSection()
                    }
                    .frame(width: geometry.size.width * 0.5)
                    .padding(.vertical, 16)

                    // 右下角变焦摇杆
                    VStack {
                        VStack(spacing: 4) {
                            VirtualJoystick(
                                size: deviceDetector.joystickSize,
                                style: .zoom,
                                onDirectionChange: { direction in
                                    handleZoomControl(direction: direction)
                                },
                                onSpeedChange: { panSpeed, tiltSpeed in
                                    handleZoomSpeedControl(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
                                }
                            )
                            Text("ZOOM")
                                .font(.caption2)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                        }
                        .padding(.trailing, deviceDetector.joystickPadding + 60)  // 减少向中间移动的距离

                        Spacer()
                    }
                    .frame(width: geometry.size.width * 0.25)
                }
                .frame(height: geometry.size.height * 0.3)
                .background(Color.black.opacity(0.8))
            }
        }
    }
}

// MARK: - iPad 发现视图

extension LandscapeMainView {

    @ViewBuilder
    private func iPadDiscoveryView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // 上部：欢迎区域
            VStack(spacing: 20) {
                Spacer()
                
                Image(systemName: "camera.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)
                
                Text("Eagle Camera Discovery")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("在局域网中发现并连接Eagle摄像头设备")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Spacer()
            }
            .frame(height: geometry.size.height * 0.6)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            
            // 下部：控制区域
            HStack(spacing: 0) {
                // 左侧装饰
                VStack {
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 120, height: 120)
                        .overlay(
                            Text("PTZ\n云台控制")
                                .font(.caption)
                                .multilineTextAlignment(.center)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: geometry.size.width * 0.25)
                
                // 中央控制面板
                VStack(spacing: 20) {
                    // 扫描控制
                    ScanControlSection()
                    
                    // 设备列表
                    DeviceListSection()
                        .frame(maxHeight: 120)
                }
                .frame(width: geometry.size.width * 0.5)
                .padding(.vertical, 20)
                
                // 右侧装饰
                VStack {
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 120, height: 120)
                        .overlay(
                            Text("ZOOM\n变焦控制")
                                .font(.caption)
                                .multilineTextAlignment(.center)
                                .foregroundColor(.white)
                        )
                }
                .frame(width: geometry.size.width * 0.25)
            }
            .frame(height: geometry.size.height * 0.4)
            .background(Color.black.opacity(0.8))
        }
    }
}

// MARK: - 控制处理

extension LandscapeMainView {
    
    private func handlePTZControl(direction: JoystickDirection?) {
        guard direction != nil else {
            // 停止PTZ控制
            //print("[LandscapeMainView] 🛑 PTZ Control: Stop movement")
            PTZManager.shared.stopMovement()
            return
        }

        // 执行PTZ控制 (保留用于兼容性，但主要使用速度控制)
        //print("[LandscapeMainView] 🎮 PTZ Control: \(direction) -> \(direction.ptzAction)")
        // PTZManager.shared.executePTZAction(direction.ptzAction)  // 注释掉，使用速度控制
    }

    // 新的PTZ速度控制方法 (基于P2-R1源码)
    private func handlePTZSpeedControl(panSpeed: Float, tiltSpeed: Float) {
        // PTZ日志已屏蔽以减少高频输出
        // print("[LandscapeMainView] 🎮 PTZ Speed Control: pan=\(panSpeed), tilt=\(tiltSpeed)")
        PTZManager.shared.moveWithJoystick(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
    }

    private func handleZoomControl(direction: JoystickDirection?) {
        guard direction != nil else {
            // 停止变焦控制
            //print("[LandscapeMainView] 🛑 Zoom Control: Stop zoom")
            PTZManager.shared.stopZoom()
            return
        }

        // 执行变焦控制 (保留用于兼容性，但主要使用速度控制)
        //print("[LandscapeMainView] 🔍 Zoom Control: \(direction)")
        // 注释掉原有逻辑，使用速度控制
    }

    // 新的变焦速度控制方法 (基于P2-R1源码) - 已交换方向
    private func handleZoomSpeedControl(panSpeed: Float, tiltSpeed: Float) {
        // 对于变焦摇杆，我们主要使用Y轴(tiltSpeed)来控制变焦
        let zoomSpeed = abs(tiltSpeed)

        if abs(tiltSpeed) < 0.01 {
            // 停止变焦
            // Zoom日志已屏蔽以减少高频输出
            // print("[LandscapeMainView] 🛑 Zoom Speed Control: Stop")
            PTZManager.shared.stopZoom()
        } else if tiltSpeed > 0 {
            // 向下推摇杆 = 放大 (交换后)
            // Zoom日志已屏蔽以减少高频输出
            // print("[LandscapeMainView] 🔍 Zoom Speed Control: Zoom In, fspeed=\(zoomSpeed)")
            PTZManager.shared.zoomIn(fspeed: zoomSpeed)
        } else {
            // 向上推摇杆 = 缩小 (交换后)
            // Zoom日志已屏蔽以减少高频输出
            // print("[LandscapeMainView] 🔍 Zoom Speed Control: Zoom Out, fspeed=\(zoomSpeed)")
            PTZManager.shared.zoomOut(fspeed: zoomSpeed)
        }
    }
}

// MARK: - Preview

#Preview {
    LandscapeMainView()
}

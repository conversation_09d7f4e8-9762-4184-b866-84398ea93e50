//
//  FileViews.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI

// MARK: - File Filter View

struct FileFilterView: View {
    @Binding var selectedFilter: FileFilter
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(FileFilter.allCases, id: \.self) { filter in
                    FilterChip(
                        title: filter.displayName,
                        isSelected: selectedFilter == filter
                    ) {
                        selectedFilter = filter
                    }
                }
            }
            .padding(.horizontal)
        }
    }
}

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.blue : Color(.systemGray5))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - File Grid View

struct FileGridView: View {
    let files: [CameraFile]
    let onFileSelected: (CameraFile) -> Void
    
    private let columns = [
        GridItem(.adaptive(minimum: 150), spacing: 12)
    ]
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: 12) {
                ForEach(files) { file in
                    FileGridItem(file: file) {
                        onFileSelected(file)
                    }
                }
            }
            .padding()
        }
    }
}

struct FileGridItem: View {
    let file: CameraFile
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                // Thumbnail
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray5))
                    .aspectRatio(16/9, contentMode: .fit)
                    .overlay(
                        VStack {
                            Image(systemName: file.type == .video ? "play.circle" : "photo")
                                .font(.title)
                                .foregroundColor(.secondary)
                            
                            if file.type == .video {
                                Text(formatDuration(file.duration ?? 0))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    )
                
                // File info
                VStack(alignment: .leading, spacing: 2) {
                    Text(file.name)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(1)
                    
                    Text(formatFileSize(file.size))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    private func formatFileSize(_ size: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }
}

// MARK: - File Detail View

struct FileDetailView: View {
    let file: CameraFile
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    @State private var showingDeleteAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // File preview
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                        .aspectRatio(16/9, contentMode: .fit)
                        .overlay(
                            VStack {
                                Image(systemName: file.type == .video ? "play.circle.fill" : "photo")
                                    .font(.system(size: 48))
                                    .foregroundColor(.secondary)
                                
                                Text(file.name)
                                    .font(.headline)
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)
                            }
                        )
                    
                    // File information
                    VStack(spacing: 16) {
                        InfoRow(title: "Type", value: file.type.rawValue.capitalized)
                        InfoRow(title: "Size", value: formatFileSize(file.size))
                        InfoRow(title: "Created", value: formatDate(file.createdAt))
                        
                        if let duration = file.duration, file.type == .video {
                            InfoRow(title: "Duration", value: formatDuration(duration))
                        }
                        
                        if let resolution = file.resolution {
                            InfoRow(title: "Resolution", value: "\(resolution.width)×\(resolution.height)")
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Action buttons
                    VStack(spacing: 12) {
                        Button("Download") {
                            downloadFile()
                        }
                        .buttonStyle(.borderedProminent)
                        .frame(maxWidth: .infinity)
                        
                        Button("Share") {
                            showingShareSheet = true
                        }
                        .buttonStyle(.bordered)
                        .frame(maxWidth: .infinity)
                        
                        Button("Delete") {
                            showingDeleteAlert = true
                        }
                        .buttonStyle(.bordered)
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity)
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("File Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .alert("Delete File", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteFile()
            }
        } message: {
            Text("Are you sure you want to delete this file? This action cannot be undone.")
        }
    }
    
    private func downloadFile() {
        // Implement file download
        print("Downloading file: \(file.name)")
    }
    
    private func deleteFile() {
        // Implement file deletion
        print("Deleting file: \(file.name)")
        dismiss()
    }
    
    private func formatFileSize(_ size: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        let seconds = Int(duration) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

// MARK: - Preview

#Preview {
    let sampleFile = CameraFile(
        id: UUID(),
        name: "IMG_001.jpg",
        type: .photo,
        size: 2048000,
        createdAt: Date(),
        path: "/photos/IMG_001.jpg"
    )
    
    return FileDetailView(file: sampleFile)
}

//
//  CameraViews.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import AVFoundation
import WebRTC

// MARK: - WebRTC Video View

struct WebRTCVideoView: View {
    @ObservedObject var webRTCStreamerClient: OptimizedWebRTCStreamerClient
    let camera: CameraDevice

    var body: some View {
        Group {
            if webRTCStreamerClient.remoteVideoTrack != nil {
                let _ = print("[WebRTCVideoView] ✅ Video track available, showing optimized video")
                OptimizedVideoPlayerView(videoTrack: webRTCStreamerClient.remoteVideoTrack)
            } else {
                let _ = print("[WebRTCVideoView] ⏳ Waiting for video track...")
                Rectangle()
                    .fill(Color.black)
                    .overlay(
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)
                            Text("准备连接视频流...")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding(.top, 16)
                        }
                    )
            }
        }
    }
}

// MARK: - Camera Stream View

struct CameraStreamView: View {
    @ObservedObject private var cameraManager = CameraManager.shared

    var body: some View {
        ZStack {
            // 添加实时状态监控
            let _ = print("[CameraViews] Body refresh - isConnected: \(cameraManager.isConnected), currentCamera: \(String(describing: cameraManager.currentCamera?.name)), webRTCClient: \(cameraManager.webRTCStreamerClient != nil)")
            let _ = print("[CameraViews] WebRTC remoteVideoTrack: \(cameraManager.webRTCStreamerClient?.remoteVideoTrack != nil)")

            // WebRTC Video Stream - 优化：基于WebRTC连接状态而不是WebSocket连接状态
            if let camera = cameraManager.currentCamera, 
               let webRTCStreamerClient = cameraManager.webRTCStreamerClient,
               (cameraManager.isConnected || webRTCStreamerClient.remoteVideoTrack != nil) {
                let _ = print("[CameraViews] ✅ Condition met: camera=\(camera.name), webRTC available, hasVideoTrack=\(webRTCStreamerClient.remoteVideoTrack != nil)")
                let _ = print("[CameraViews] ✅ WebRTC client available, creating ProductionVideoView")

                    // 使用 WebRTCVideoView 来自动响应视频轨道变化
                    WebRTCVideoView(webRTCStreamerClient: webRTCStreamerClient, camera: camera)
                        .id("webrtc-video-\(camera.ipAddress)")  // 移除 refreshTrigger，使用稳定的ID
                        .ignoresSafeArea(.all)
                        .onAppear {
                            print("[CameraViews] WebRTCVideoView appeared")
                            // 视图出现时不需要强制检查WebRTC连接
                            // WebRTC连接状态由CameraManager自主管理
                        }
            } else if let camera = cameraManager.currentCamera, cameraManager.webRTCStreamerClient == nil {
                let _ = print("[CameraViews] ❌ WebRTC client not available for camera: \(camera.name)")
                // WebRTC 客户端未初始化
                Rectangle()
                    .fill(Color.black)
                    .overlay(
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            Text("Initializing WebRTC...")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                                .padding(.top, 8)
                        }
                    )
                    .onAppear {
                        print("[CameraViews] WebRTC client not available")
                        cameraManager.initializeWebRTC(for: camera)
                    }
            } else {
                let _ = print("[CameraViews] ❌ Condition not met: currentCamera=\(String(describing: cameraManager.currentCamera)), webRTCClient=\(cameraManager.webRTCStreamerClient != nil), isConnected=\(cameraManager.isConnected)")
                // Camera stream placeholder
                Rectangle()
                    .fill(Color.black)
                    .overlay(
                        VStack {
                            Image(systemName: "video")
                                .font(.system(size: 48))
                                .foregroundColor(.white.opacity(0.6))
                            Text("Camera Stream")
                                .font(.headline)
                                .foregroundColor(.white.opacity(0.8))
                            
                            if let camera = cameraManager.currentCamera {
                                if cameraManager.isConnected {
                                    Text("Connected to \(camera.name)")
                                        .font(.caption)
                                        .foregroundColor(.white.opacity(0.6))
                                } else {
                                    Text("Reconnecting to \(camera.name)...")
                                        .font(.caption)
                                        .foregroundColor(.orange.opacity(0.8))
                                }
                            } else {
                                Text("No camera connected")
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.6))
                            }
                        }
                    )
                    .onAppear {
                        print("[CameraViews] Camera not connected: isConnected=\(cameraManager.isConnected), currentCamera=\(String(describing: cameraManager.currentCamera))")
                    }
            }
            
            // Stream controls overlay
            VStack {
                // 顶部居中连接状态指示器
                HStack {
                    Spacer()

                    // 连接状态指示器 - 居中显示
                    HStack(spacing: 8) {
                        Circle()
                            .fill(connectionStatusColor)
                            .frame(width: 10, height: 10)
                            .animation(.easeInOut(duration: 0.3), value: connectionStatusColor)

                        Text(connectionStatusText)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(Color.black.opacity(0.7))
                            .overlay(
                                Capsule()
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                    )

                    Spacer()
                }
                .padding(.top, 20)

                Spacer()
            }

            // 注意：PTZ控制现在由LandscapeMainView中的虚拟摇杆处理
        }
        .cornerRadius(12)
    }

    // MARK: - Helper Methods

    private var connectionStatusColor: Color {
        if let webRTCStreamerClient = cameraManager.webRTCStreamerClient {
            if cameraManager.isConnected && webRTCStreamerClient.connectionState == .connected {
                return .green
            } else if cameraManager.isConnecting || webRTCStreamerClient.connectionState == .connecting {
                return .orange
            } else {
                return .red
            }
        } else if cameraManager.isConnected {
            return .orange  // 摄像头已连接但WebRTC未连接
        } else if cameraManager.isConnecting {
            return .orange
        } else {
            return .red
        }
    }

    private var connectionStatusText: String {
        if let webRTCStreamerClient = cameraManager.webRTCStreamerClient {
            if cameraManager.isConnected && webRTCStreamerClient.connectionState == .connected {
                return "已连接"
            } else if cameraManager.isConnecting || webRTCStreamerClient.connectionState == .connecting {
                return "连接中"
            } else if webRTCStreamerClient.connectionState == .failed {
                return "连接失败"
            } else {
                return "未连接"
            }
        } else if cameraManager.isConnected {
            return "摄像头已连接"
        } else if cameraManager.isConnecting {
            return "连接中"
        } else {
            return "未连接"
        }
    }

    private func connectionStatusText(for state: RTCPeerConnectionState) -> String {
        switch state {
        case .new:
            return "准备连接"
        case .connecting:
            return "连接中"
        case .connected:
            return "已连接"
        case .disconnected:
            return "已断开"
        case .failed:
            return "连接失败"
        case .closed:
            return "已关闭"
        @unknown default:
            return "未知状态"
        }
    }
}

// MARK: - Camera Controls View

struct CameraControlsView: View {
    @StateObject private var cameraManager = CameraManager.shared
    @State private var showingPTZControls = false
    
    var body: some View {
        VStack(spacing: 16) {
            // Recording controls
            HStack(spacing: 20) {
                Button(action: {
                    if cameraManager.isRecording {
                        cameraManager.stopRecording()
                    } else {
                        cameraManager.startRecording()
                    }
                }) {
                    HStack {
                        Image(systemName: cameraManager.isRecording ? "stop.circle.fill" : "record.circle")
                            .font(.title2)
                        Text(cameraManager.isRecording ? "Stop" : "Record")
                    }
                    .foregroundColor(cameraManager.isRecording ? .red : .white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(cameraManager.isRecording ? Color.white : Color.red)
                    .cornerRadius(20)
                }
                
                Button(action: {
                    cameraManager.capturePhoto()
                }) {
                    HStack {
                        Image(systemName: "camera")
                            .font(.title2)
                        Text("Photo")
                    }
                    .foregroundColor(.blue)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.white)
                    .cornerRadius(20)
                }
                
                Button(action: {
                    showingPTZControls.toggle()
                }) {
                    HStack {
                        Image(systemName: "move.3d")
                            .font(.title2)
                        Text("PTZ")
                    }
                    .foregroundColor(.green)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.white)
                    .cornerRadius(20)
                }
            }
            
            // Recording duration
            if cameraManager.isRecording {
                Text(formatDuration(cameraManager.recordingDuration))
                    .font(.headline)
                    .foregroundColor(.red)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(Color.white)
                    .cornerRadius(8)
            }
        }
        .sheet(isPresented: $showingPTZControls) {
            PTZControlsView()
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - Camera Connection View

struct CameraConnectionView: View {
    @StateObject private var cameraManager = CameraManager.shared
    @ObservedObject private var serviceDiscovery = EagleServiceDiscovery.shared
    @State private var showingManualConnection = false
    
    var body: some View {
        VStack(spacing: 24) {
            // Connection status
            VStack(spacing: 12) {
                Image(systemName: "camera.badge.ellipsis")
                    .font(.system(size: 64))
                    .foregroundColor(.secondary)
                
                Text("Connect to Camera")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Select a camera from the list below or add one manually")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // Available cameras
            if !cameraManager.availableCameras.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Available Cameras")
                        .font(.headline)
                    
                    ForEach(cameraManager.availableCameras) { camera in
                        CameraListItem(camera: camera) {
                            cameraManager.connect(to: camera)
                        }
                    }
                }
            }
            
            // Discovered Eagle devices
            if !serviceDiscovery.discoveredIPs.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Discovered Eagle Devices")
                        .font(.headline)
                        .onAppear {
                            print("📋 Displaying \(serviceDiscovery.discoveredIPs.count) discovered devices")
                            print("📋 Device IPs: \(serviceDiscovery.discoveredIPs)")
                        }

                    ForEach(serviceDiscovery.discoveredIPs, id: \.self) { ip in
                        Button(action: {
                            print("👆 User clicked on IP: \(ip)")

                            // 从 IP 字符串中提取纯 IP 地址（去除端口信息）
                            let cleanIP = ip.components(separatedBy: ":").first ?? ip
                            print("📱 Creating CameraDevice for IP: \(cleanIP) - HTTP API: port 80, WebRTC: port 8000")

                            let camera = CameraDevice(
                                name: "Eagle Camera",
                                ipAddress: cleanIP,
                                port: 80  // P2-R1 HTTP API 使用端口 80，WebRTC Streamer 使用端口 8000
                            )

                            print("🚀 Calling cameraManager.connect() for \(cleanIP):80 (HTTP API)")
                            cameraManager.connect(to: camera)
                        }) {
                            HStack {
                                Image(systemName: "camera")
                                    .foregroundColor(.blue)
                                VStack(alignment: .leading) {
                                    Text("Eagle Camera")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    Text(ip)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                            .onAppear {
                                print("🔘 Button created for IP: \(ip)")
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
            
            // Manual connection button
            Button("Add Camera Manually") {
                showingManualConnection = true
            }
            .buttonStyle(.borderedProminent)
            
            Spacer()
        }
        .padding()
        .onAppear {
            print("🏠 CameraConnectionView appeared")
            print("📊 Current discovered IPs count: \(serviceDiscovery.discoveredIPs.count)")
            print("📊 Current discovered IPs: \(serviceDiscovery.discoveredIPs)")

            cameraManager.discoverCameras()

            // 注意：不再手动触发扫描，因为EagleServiceDiscovery单例会自动执行初始扫描
            print("ℹ️ Using EagleServiceDiscovery singleton - initial scan handled automatically")
        }
        .sheet(isPresented: $showingManualConnection) {
            ManualCameraConnectionView()
        }
    }
}

// MARK: - Camera Selection View

struct CameraSelectionView: View {
    @StateObject private var cameraManager = CameraManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(cameraManager.availableCameras) { camera in
                    CameraListItem(camera: camera) {
                        cameraManager.connect(to: camera)
                        dismiss()
                    }
                }
            }
            .navigationTitle("Select Camera")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Refresh") {
                        cameraManager.discoverCameras()
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct CameraListItem: View {
    let camera: CameraDevice
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: "camera")
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(camera.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Text("\(camera.ipAddress):\(camera.port)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if camera.isOnline {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 8, height: 8)
                }
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct PTZControlsView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("PTZ Controls")
                    .font(.title2)
                    .padding()
                
                // PTZ control implementation would go here
                Text("PTZ controls coming soon...")
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .navigationTitle("PTZ Controls")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ManualCameraConnectionView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var ipAddress = ""
    @State private var port = "80"
    @State private var cameraName = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section("Camera Details") {
                    TextField("Camera Name", text: $cameraName)
                    TextField("IP Address", text: $ipAddress)
                        .keyboardType(.numbersAndPunctuation)
                    TextField("Port", text: $port)
                        .keyboardType(.numberPad)
                }
            }
            .navigationTitle("Add Camera")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Connect") {
                        // Connect to camera
                        dismiss()
                    }
                    .disabled(ipAddress.isEmpty || port.isEmpty)
                }
            }
        }
    }
}

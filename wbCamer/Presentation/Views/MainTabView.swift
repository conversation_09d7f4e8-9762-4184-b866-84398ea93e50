//
//  MainTabView.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI

struct MainTabView: View {
    @StateObject private var appSettings = AppSettings.shared
    @StateObject private var networkMonitor = NetworkMonitor.shared
    @State private var selectedTab: TabItem = .live
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Live View Tab
            LiveView()
                .tabItem {
                    Image(systemName: selectedTab == .live ? "video.fill" : "video")
                    Text("Live")
                }
                .tag(TabItem.live)
            
            // Gallery Tab
            GalleryView()
                .tabItem {
                    Image(systemName: selectedTab == .gallery ? "photo.fill.on.rectangle.fill" : "photo.on.rectangle")
                    Text("Gallery")
                }
                .tag(TabItem.gallery)
            
            // Settings Tab
            SettingsView()
                .tabItem {
                    Image(systemName: selectedTab == .settings ? "gearshape.fill" : "gearshape")
                    Text("Settings")
                }
                .tag(TabItem.settings)
        }
        .accentColor(.primary)
        .onAppear {
            setupTabBarAppearance()
            networkMonitor.startMonitoring()
        }
        .onDisappear {
            networkMonitor.stopMonitoring()
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        
        // Configure colors based on theme
        if appSettings.appTheme == "dark" {
            appearance.backgroundColor = UIColor.systemBackground
        } else if appSettings.appTheme == "light" {
            appearance.backgroundColor = UIColor.systemBackground
        } else {
            appearance.backgroundColor = UIColor.systemBackground
        }
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Tab Items

enum TabItem: String, CaseIterable {
    case live = "live"
    case gallery = "gallery"
    case settings = "settings"
    
    var title: String {
        switch self {
        case .live:
            return "Live"
        case .gallery:
            return "Gallery"
        case .settings:
            return "Settings"
        }
    }
    
    var iconName: String {
        switch self {
        case .live:
            return "video"
        case .gallery:
            return "photo.on.rectangle"
        case .settings:
            return "gearshape"
        }
    }
    
    var selectedIconName: String {
        switch self {
        case .live:
            return "video.fill"
        case .gallery:
            return "photo.fill.on.rectangle.fill"
        case .settings:
            return "gearshape.fill"
        }
    }
}

// MARK: - Live View

struct LiveView: View {
    @StateObject private var cameraManager = CameraManager.shared
    @StateObject private var networkMonitor = NetworkMonitor.shared
    @State private var showingCameraSelection = false
    @State private var showingConnectionError = false

    var body: some View {
        let _ = print("[LiveView] Body refresh - isConnected: \(cameraManager.isConnected), currentCamera: \(String(describing: cameraManager.currentCamera?.name))")

        VStack(spacing: 0) {
            // 顶部工具栏
            HStack {
                Button(action: {
                    showingCameraSelection = true
                }) {
                    Image(systemName: "camera")
                        .font(.title2)
                        .foregroundColor(.primary)
                }

                Spacer()

                Text("Live")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                NetworkStatusIndicator()
            }
            .padding(.horizontal)
            .padding(.vertical, 12)
            .background(Color(UIColor.systemBackground))

            // 主内容区域
            if cameraManager.isConnected {
                let _ = print("[LiveView] ✅ Camera connected, showing CameraStreamView")

                // Camera stream view - 占据大部分空间
                CameraStreamView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)

                // Camera controls - 底部控制栏
                CameraControlsView()
                    .padding()
                    .background(Color(UIColor.systemBackground))
            } else {
                let _ = print("[LiveView] ❌ Camera not connected, showing CameraConnectionView")
                // Connection view
                CameraConnectionView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .ignoresSafeArea(.all, edges: .bottom) // 忽略底部安全区域，让视频全屏显示
        .sheet(isPresented: $showingCameraSelection) {
            CameraSelectionView()
        }
        .alert("Connection Error", isPresented: $showingConnectionError) {
            Button("OK") { }
            Button("Retry") {
                cameraManager.reconnect()
            }
        } message: {
            Text("Failed to connect to camera. Please check your network connection and camera settings.")
        }
    }
}

// MARK: - Gallery View

struct GalleryView: View {
    @StateObject private var galleryManager = GalleryManager.shared
    @State private var selectedFilter: FileFilter = .all
    @State private var showingFileDetails = false
    @State private var selectedFile: CameraFile?
    
    var body: some View {
        NavigationView {
            VStack {
                // Filter controls
                FileFilterView(selectedFilter: $selectedFilter)
                    .padding(.horizontal)
                
                // File grid
                if galleryManager.files.isEmpty {
                    EmptyGalleryView()
                } else {
                    FileGridView(
                        files: galleryManager.filteredFiles(for: selectedFilter),
                        onFileSelected: { file in
                            selectedFile = file
                            showingFileDetails = true
                        }
                    )
                }
            }
            .navigationTitle("Gallery")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Refresh") {
                        galleryManager.refreshFiles()
                    }
                }
            }
        }
        .sheet(isPresented: $showingFileDetails) {
            if let file = selectedFile {
                FileDetailView(file: file)
            }
        }
        .onAppear {
            galleryManager.loadFiles()
        }
    }
}

// MARK: - Settings View

struct SettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    @State private var showingAbout = false
    
    var body: some View {
        NavigationView {
            List {
                // Camera Settings Section
                Section("Camera") {
                    NavigationLink("Camera Settings") {
                        CameraSettingsView()
                    }
                    
                    NavigationLink("Connection Settings") {
                        ConnectionSettingsView()
                    }
                    
                    NavigationLink("Stream Quality") {
                        StreamQualitySettingsView()
                    }
                }
                
                // Storage Settings Section
                Section("Storage") {
                    NavigationLink("Download Settings") {
                        DownloadSettingsView()
                    }
                    
                    NavigationLink("Storage Management") {
                        StorageManagementView()
                    }
                }
                
                // App Settings Section
                Section("App") {
                    NavigationLink("Appearance") {
                        AppearanceSettingsView()
                    }
                    
                    NavigationLink("Notifications") {
                        NotificationSettingsView()
                    }
                    
                    NavigationLink("Privacy & Security") {
                        PrivacySettingsView()
                    }
                }
                
                // Debug Section (only in debug builds)
                #if DEBUG
                Section("Debug") {
                    NavigationLink("Debug Settings") {
                        DebugSettingsView()
                    }
                    
                    NavigationLink("Network Diagnostics") {
                        NetworkDiagnosticsView()
                    }
                }
                #endif
                
                // About Section
                Section {
                    Button("About") {
                        showingAbout = true
                    }
                    
                    Button("Reset All Settings") {
                        resetAllSettings()
                    }
                    .foregroundColor(.red)
                } footer: {
                    VStack(alignment: .center, spacing: 4) {
                        Text("wbCamer")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String,
                           let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
                            Text("Version \(version) (\(build))")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingAbout) {
            AboutView()
        }
    }
    
    private func resetAllSettings() {
        // Show confirmation alert first
        let alert = UIAlertController(
            title: "Reset All Settings",
            message: "This will reset all app settings to their default values. This action cannot be undone.",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Reset", style: .destructive) { _ in
            appSettings.reset()
            SecureStorage.shared.clearAllSecureData()
        })
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(alert, animated: true)
        }
    }
}



// MARK: - Preview

#Preview {
    MainTabView()
}
//
//  DebugViews.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI

// MARK: - Debug Settings View

struct DebugSettingsView: View {
    @StateObject private var appSettings = AppSettings.shared
    
    var body: some View {
        Form {
            Section("Logging") {
                FormToggle(
                    title: "Enable Debug Logging",
                    description: "Enable detailed logging for debugging",
                    isOn: $appSettings.enableDebugLogging
                )
                
                FormToggle(
                    title: "Log Network Requests",
                    description: "Log all network requests and responses",
                    isOn: $appSettings.logNetworkRequests
                )
                
                Button("Export Logs") {
                    exportLogs()
                }
            }
            
            Section("Development") {
                Button("Reset All Settings") {
                    resetAllSettings()
                }
                .foregroundColor(.red)
                
                Button("Clear All Data") {
                    clearAllData()
                }
                .foregroundColor(.red)
                
                Button("Simulate Connection Error") {
                    simulateConnectionError()
                }
                .foregroundColor(.orange)
            }
            
            Section("App Information") {
                InfoRow(title: "Version", value: appVersion)
                InfoRow(title: "Build", value: buildNumber)
                InfoRow(title: "Bundle ID", value: bundleIdentifier)
                InfoRow(title: "Launch Count", value: "\(appSettings.appLaunchCount)")
            }
        }
        .navigationTitle("Debug Settings")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    }
    
    private var buildNumber: String {
        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
    }
    
    private var bundleIdentifier: String {
        Bundle.main.bundleIdentifier ?? "Unknown"
    }
    
    private func exportLogs() {
        // Implement log export
        print("Exporting logs...")
    }
    
    private func resetAllSettings() {
        appSettings.reset()
    }
    
    private func clearAllData() {
        // Implement data clearing
        print("Clearing all data...")
    }
    
    private func simulateConnectionError() {
        // Simulate connection error for testing
        print("Simulating connection error...")
    }
}

// MARK: - Network Diagnostics View

struct NetworkDiagnosticsView: View {
    @StateObject private var networkMonitor = NetworkMonitor.shared
    @State private var pingResults: [PingResult] = []
    @State private var isRunningDiagnostics = false
    
    var body: some View {
        Form {
            Section("Network Status") {
                InfoRow(title: "Connection Type", value: networkMonitor.networkInfo.connectionType)
                InfoRow(title: "Status", value: networkStatusText)
                InfoRow(title: "IPv4 Support", value: networkMonitor.networkInfo.supportsIPv4 ? "Yes" : "No")
                InfoRow(title: "IPv6 Support", value: networkMonitor.networkInfo.supportsIPv6 ? "Yes" : "No")
                InfoRow(title: "Expensive", value: networkMonitor.networkInfo.isExpensive ? "Yes" : "No")
                InfoRow(title: "Constrained", value: networkMonitor.networkInfo.isConstrained ? "Yes" : "No")
            }
            
            Section("Diagnostics") {
                Button(isRunningDiagnostics ? "Running Diagnostics..." : "Run Network Diagnostics") {
                    runNetworkDiagnostics()
                }
                .disabled(isRunningDiagnostics)
                
                if !pingResults.isEmpty {
                    ForEach(pingResults, id: \.host) { result in
                        PingResultRow(result: result)
                    }
                }
            }
            
            Section("Camera Discovery") {
                Button("Scan for Cameras") {
                    scanForCameras()
                }
                
                Button("Test mDNS Resolution") {
                    testMDNSResolution()
                }
            }
        }
        .navigationTitle("Network Diagnostics")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private var networkStatusText: String {
        switch networkMonitor.networkInfo.status {
        case .reachable:
            return "Connected"
        case .notReachable:
            return "Not Connected"
        case .unknown:
            return "Unknown"
        }
    }
    
    private func runNetworkDiagnostics() {
        isRunningDiagnostics = true
        pingResults.removeAll()
        
        let hosts = ["*******", "*******", "***********"]
        
        // Simulate ping results
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            for host in hosts {
                let result = PingResult(
                    host: host,
                    success: Bool.random(),
                    responseTime: Double.random(in: 10...100),
                    error: nil
                )
                pingResults.append(result)
            }
            isRunningDiagnostics = false
        }
    }
    
    private func scanForCameras() {
        // Implement camera scanning
        print("Scanning for cameras...")
    }
    
    private func testMDNSResolution() {
        // Implement mDNS testing
        print("Testing mDNS resolution...")
    }
}

struct PingResultRow: View {
    let result: PingResult
    
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text(result.host)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                if result.success {
                    Text("\(Int(result.responseTime))ms")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else {
                    Text("Failed")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
            
            Spacer()
            
            Circle()
                .fill(result.success ? Color.green : Color.red)
                .frame(width: 8, height: 8)
        }
    }
}

// MARK: - About View

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // App icon and name
                    VStack(spacing: 12) {
                        Image(systemName: "camera.fill")
                            .font(.system(size: 64))
                            .foregroundColor(.blue)
                        
                        Text("wbCamer")
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("Professional Camera Control")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    // Version information
                    VStack(spacing: 8) {
                        Text("Version \(appVersion)")
                            .font(.subheadline)
                        Text("Build \(buildNumber)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Description
                    VStack(alignment: .leading, spacing: 12) {
                        Text("About")
                            .font(.headline)
                        
                        Text("wbCamer is a professional camera control application that allows you to connect to and control Eagle cameras over your local network. Stream live video, capture photos, record videos, and manage your camera files with ease.")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Features
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Features")
                            .font(.headline)
                        
                        FeatureRow(icon: "video", title: "Live Streaming", description: "Real-time video streaming from your camera")
                        FeatureRow(icon: "camera", title: "Photo Capture", description: "Capture high-quality photos remotely")
                        FeatureRow(icon: "record.circle", title: "Video Recording", description: "Record videos directly to your device")
                        FeatureRow(icon: "folder", title: "File Management", description: "Browse and manage camera files")
                        FeatureRow(icon: "wifi", title: "Network Discovery", description: "Automatic camera discovery on your network")
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // Links
                    VStack(spacing: 12) {
                        Button("Privacy Policy") {
                            // Open privacy policy
                        }
                        
                        Button("Terms of Service") {
                            // Open terms of service
                        }
                        
                        Button("Support") {
                            // Open support
                        }
                    }
                    .font(.subheadline)
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("About")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    }
    
    private var buildNumber: String {
        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Supporting Models

struct PingResult {
    let host: String
    let success: Bool
    let responseTime: Double
    let error: Error?
}

// MARK: - Preview

#Preview {
    AboutView()
}

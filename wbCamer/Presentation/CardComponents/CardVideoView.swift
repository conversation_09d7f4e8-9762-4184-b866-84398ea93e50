//
//  CardVideoView.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import SwiftUI
import WebRTC

// MARK: - Card Video View

/// 卡片视频视图 - 显示单个摄像机卡片的视频流
struct CardVideoView: UIViewRepresentable {
    let videoTrack: RTCVideoTrack
    let cardId: UUID
    
    func makeUIView(context: Context) -> RTCMTLVideoView {
        let videoView = RTCMTLVideoView(frame: .zero)
        videoView.videoContentMode = .scaleAspectFit
        videoView.delegate = context.coordinator
        
        // 设置视频轨道
        videoTrack.add(videoView)
        
        return videoView
    }
    
    func updateUIView(_ uiView: RTCMTLVideoView, context: Context) {
        // 视频轨道变化时更新
        videoTrack.add(uiView)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(cardId: cardId)
    }
    
    class Coordinator: NSObject, RTCVideoViewDelegate {
        let cardId: UUID
        
        init(cardId: UUID) {
            self.cardId = cardId
        }
        
        func videoView(_ videoView: RTCVideo<PERSON><PERSON><PERSON>, didChangeVideoSize size: CGSize) {
            print("[CardVideoView] \(cardId.uuidString.prefix(8)) video size changed: \(size)")
        }
    }
}

// MARK: - Card Video Player View

/// 卡片视频播放器视图 - 包含视频显示和控制元素
struct CardVideoPlayerView: View {
    @ObservedObject var card: CameraCard
    @State private var showControls = false
    @State private var controlsTimer: Timer?
    
    var body: some View {
        ZStack {
            // 视频背景
            Color.black
            
            // 视频内容
            if let videoView = card.getVideoView() {
                videoView
                    .clipped()
            } else {
                // 无视频时的占位符
                VStack(spacing: 16) {
                    Image(systemName: "video.slash")
                        .font(.system(size: 48))
                        .foregroundColor(.gray)
                    
                    Text("No Video Signal")
                        .font(.headline)
                        .foregroundColor(.gray)
                    
                    if case .loading = card.state.videoState {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }
                }
            }
            
            // 视频控制层
            if showControls {
                VideoControlOverlay(card: card)
                    .transition(.opacity)
            }
            
            // 状态指示器
            VStack {
                HStack {
                    Spacer()
                    CardStatusIndicator(card: card)
                        .padding(.top, 8)
                        .padding(.trailing, 8)
                }
                Spacer()
            }
        }
        .onTapGesture {
            toggleControls()
        }
        .onAppear {
            // 初始显示控制元素
            showControls = true
            scheduleControlsHiding()
        }
    }
    
    private func toggleControls() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showControls.toggle()
        }
        
        if showControls {
            scheduleControlsHiding()
        } else {
            controlsTimer?.invalidate()
        }
    }
    
    private func scheduleControlsHiding() {
        controlsTimer?.invalidate()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                showControls = false
            }
        }
    }
}

// MARK: - Video Control Overlay

/// 视频控制覆盖层
struct VideoControlOverlay: View {
    @ObservedObject var card: CameraCard
    
    var body: some View {
        VStack {
            // 顶部控制栏
            HStack {
                Text(card.name)
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.black.opacity(0.6))
                    .cornerRadius(8)
                
                Spacer()
                
                // 视频质量指示器
                VideoQualityIndicator(quality: card.configuration.maxVideoQuality)
            }
            .padding()
            
            Spacer()
            
            // 底部控制栏
            HStack {
                // 录制按钮
                Button(action: {
                    // TODO: 实现录制功能
                }) {
                    Image(systemName: "record.circle")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(12)
                        .background(Color.red.opacity(0.8))
                        .clipShape(Circle())
                }
                
                Spacer()
                
                // 截图按钮
                Button(action: {
                    // TODO: 实现截图功能
                }) {
                    Image(systemName: "camera")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(12)
                        .background(Color.black.opacity(0.6))
                        .clipShape(Circle())
                }
                
                Spacer()
                
                // 全屏按钮
                Button(action: {
                    // TODO: 实现全屏功能
                }) {
                    Image(systemName: "arrow.up.left.and.arrow.down.right")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(12)
                        .background(Color.black.opacity(0.6))
                        .clipShape(Circle())
                }
            }
            .padding()
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black.opacity(0.6),
                    Color.clear,
                    Color.black.opacity(0.6)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
}

// MARK: - Video Quality Indicator

/// 视频质量指示器
struct VideoQualityIndicator: View {
    let quality: VideoQuality
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(qualityColor)
                .frame(width: 8, height: 8)
            
            Text(quality.resolution)
                .font(.caption)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.black.opacity(0.6))
        .cornerRadius(12)
    }
    
    private var qualityColor: Color {
        switch quality {
        case .low:
            return .red
        case .medium:
            return .orange
        case .high:
            return .green
        case .ultra:
            return .blue
        }
    }
}

// MARK: - Card Status Indicator

/// 卡片状态指示器
struct CardStatusIndicator: View {
    @ObservedObject var card: CameraCard
    
    var body: some View {
        HStack(spacing: 6) {
            // 连接状态指示器
            Circle()
                .fill(connectionStatusColor)
                .frame(width: 10, height: 10)
            
            // 状态文本
            Text(statusText)
                .font(.caption2)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.black.opacity(0.7))
        .cornerRadius(12)
    }
    
    private var connectionStatusColor: Color {
        switch card.state.connectionState {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .disconnected:
            return .gray
        case .error:
            return .red
        case .suspended:
            return .yellow
        }
    }
    
    private var statusText: String {
        switch card.state.connectionState {
        case .connected:
            return "已连接"
        case .connecting:
            return "连接中"
        case .disconnected:
            return "未连接"
        case .error:
            return "错误"
        case .suspended:
            return "已暂停"
        }
    }
}

#Preview {
    // 预览用的模拟卡片
    let device = CameraDevice(name: "Test Camera", ipAddress: "*************", port: 80)
    let card = CameraCard(device: device)
    
    return CardVideoPlayerView(card: card)
        .frame(width: 400, height: 300)
        .background(Color.black)
}

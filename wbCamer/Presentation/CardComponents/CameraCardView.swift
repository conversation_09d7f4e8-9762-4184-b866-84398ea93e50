//
//  CameraCardView.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import SwiftUI

// MARK: - Camera Card View

/// 单个摄像机卡片视图 - 显示摄像机的视频、状态和控制
struct CameraCardView: View {
    @ObservedObject var card: CameraCard
    @ObservedObject var cardManager: CameraCardManager
    @State private var isExpanded: Bool = false
    @State private var showingControls: Bool = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 卡片头部
            CardHeaderView(card: card, cardManager: cardManager, isExpanded: $isExpanded)
            
            // 视频预览区域
            if card.isConnected {
                CardVideoPlayerView(card: card)
                    .aspectRatio(16/9, contentMode: .fit)
                    .clipped()
                    .onTapGesture {
                        cardManager.setActiveCard(card)
                    }
            } else {
                CardPlaceholderView(card: card)
                    .aspectRatio(16/9, contentMode: .fit)
            }
            
            // 扩展控制面板
            if isExpanded {
                CardControlPanelView(card: card)
                    .transition(.slide)
            }
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: shadowColor, radius: shadowRadius, x: 0, y: 2)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .scaleEffect(card.state.isFocused ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: card.state.isFocused)
        .animation(.easeInOut(duration: 0.3), value: isExpanded)
    }
    
    // MARK: - Computed Properties
    
    private var shadowColor: Color {
        if card.state.isFocused {
            return .blue.opacity(0.3)
        } else if card.isConnected {
            return .green.opacity(0.2)
        } else {
            return .gray.opacity(0.1)
        }
    }
    
    private var shadowRadius: CGFloat {
        card.state.isFocused ? 8 : 4
    }
    
    private var borderColor: Color {
        if card.state.isFocused {
            return .blue
        } else if card.isConnected {
            return .green
        } else {
            return .gray.opacity(0.3)
        }
    }
    
    private var borderWidth: CGFloat {
        card.state.isFocused ? 2 : 1
    }
}

// MARK: - Card Header View

/// 卡片头部视图
struct CardHeaderView: View {
    @ObservedObject var card: CameraCard
    @ObservedObject var cardManager: CameraCardManager
    @Binding var isExpanded: Bool
    
    var body: some View {
        HStack {
            // 设备信息
            VStack(alignment: .leading, spacing: 2) {
                Text(card.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(card.ipAddress)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 状态指示器
            HStack(spacing: 8) {
                // 连接状态
                ConnectionStatusDot(state: card.state.connectionState)
                
                // 视频质量
                if card.isConnected {
                    VideoQualityBadge(quality: card.configuration.maxVideoQuality)
                }
                
                // 主卡片标识
                if cardManager.primaryCard?.id == card.id {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.caption)
                }
            }
            
            // 展开/收起按钮
            Button(action: {
                withAnimation {
                    isExpanded.toggle()
                }
            }) {
                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
}

// MARK: - Card Placeholder View

/// 卡片占位符视图（未连接时显示）
struct CardPlaceholderView: View {
    @ObservedObject var card: CameraCard
    
    var body: some View {
        ZStack {
            Color(.systemGray5)
            
            VStack(spacing: 12) {
                // 状态图标
                Image(systemName: statusIcon)
                    .font(.system(size: 32))
                    .foregroundColor(statusColor)
                
                // 状态文本
                Text(statusText)
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                // 连接按钮
                if case .disconnected = card.state.connectionState {
                    Button("连接") {
                        Task {
                            await CameraCardManager.shared.connectCard(card)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                
                // 进度指示器
                if case .connecting = card.state.connectionState {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                }
            }
        }
    }
    
    private var statusIcon: String {
        switch card.state.connectionState {
        case .disconnected:
            return "video.slash"
        case .connecting:
            return "antenna.radiowaves.left.and.right"
        case .error:
            return "exclamationmark.triangle"
        case .suspended:
            return "pause.circle"
        default:
            return "video"
        }
    }
    
    private var statusColor: Color {
        switch card.state.connectionState {
        case .disconnected:
            return .gray
        case .connecting:
            return .orange
        case .error:
            return .red
        case .suspended:
            return .yellow
        default:
            return .green
        }
    }
    
    private var statusText: String {
        switch card.state.connectionState {
        case .disconnected:
            return "未连接"
        case .connecting:
            return "连接中..."
        case .error:
            return "连接错误"
        case .suspended:
            return "已暂停"
        default:
            return "已连接"
        }
    }
}

// MARK: - Card Control Panel View

/// 卡片控制面板视图
struct CardControlPanelView: View {
    @ObservedObject var card: CameraCard
    
    var body: some View {
        VStack(spacing: 12) {
            // 连接控制
            HStack {
                if card.isConnected {
                    Button("断开连接") {
                        Task {
                            await CameraCardManager.shared.disconnectCard(card)
                        }
                    }
                    .buttonStyle(.bordered)
                    
                    Button("暂停") {
                        Task {
                            await CameraCardManager.shared.suspendCard(card)
                        }
                    }
                    .buttonStyle(.bordered)
                } else {
                    Button("连接") {
                        Task {
                            await CameraCardManager.shared.connectCard(card)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                
                Spacer()
                
                Button("设为主卡片") {
                    CameraCardManager.shared.setPrimaryCard(card)
                }
                .buttonStyle(.bordered)
                .disabled(CameraCardManager.shared.primaryCard?.id == card.id)
            }
            
            // 质量控制
            VStack(alignment: .leading, spacing: 8) {
                Text("视频质量")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Picker("视频质量", selection: Binding(
                    get: { card.configuration.maxVideoQuality },
                    set: { newQuality in
                        card.configuration.maxVideoQuality = newQuality
                        // 更新资源分配
                        Task {
                            _ = ResourceManager.shared.updateResourceAllocation(card.id, newQuality: newQuality)
                        }
                    }
                )) {
                    ForEach(VideoQuality.allCases, id: \.self) { quality in
                        Text(quality.displayName).tag(quality)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            // 资源使用情况
            ResourceUsageView(usage: card.state.resourceUsage)
        }
        .padding(12)
        .background(Color(.systemGray6))
    }
}

// MARK: - Supporting Views

/// 连接状态指示点
struct ConnectionStatusDot: View {
    let state: CardConnectionState
    
    var body: some View {
        Circle()
            .fill(statusColor)
            .frame(width: 8, height: 8)
    }
    
    private var statusColor: Color {
        switch state {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .disconnected:
            return .gray
        case .error:
            return .red
        case .suspended:
            return .yellow
        }
    }
}

/// 视频质量徽章
struct VideoQualityBadge: View {
    let quality: VideoQuality
    
    var body: some View {
        Text(quality.resolution)
            .font(.caption2)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(quality.color)
            .foregroundColor(.white)
            .cornerRadius(4)
    }
}

extension VideoQuality {
    var color: Color {
        switch self {
        case .low: return .red
        case .medium: return .orange
        case .high: return .green
        case .ultra: return .blue
        }
    }
}

/// 资源使用视图
struct ResourceUsageView: View {
    let usage: ResourceUsage
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("资源使用")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack {
                ResourceBar(label: "内存", value: usage.memoryUsage, unit: "MB", color: .blue)
                ResourceBar(label: "CPU", value: usage.cpuUsage, unit: "%", color: .orange)
                ResourceBar(label: "带宽", value: usage.bandwidthUsage, unit: "Mbps", color: .green)
            }
        }
    }
}

/// 资源使用条
struct ResourceBar: View {
    let label: String
    let value: Double
    let unit: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Text("\(value, specifier: "%.1f")\(unit)")
                .font(.caption2)
                .foregroundColor(.primary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

#Preview {
    let device = CameraDevice(name: "Test Camera", ipAddress: "*************", port: 80)
    let card = CameraCard(device: device)
    let cardManager = CameraCardManager.shared
    
    return CameraCardView(card: card, cardManager: cardManager)
        .frame(width: 300, height: 200)
        .padding()
}

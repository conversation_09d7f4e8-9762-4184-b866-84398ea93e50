//
//  CardGridView.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import SwiftUI

// MARK: - Card Grid View

/// 卡片网格视图 - 以网格形式显示多个摄像机卡片
struct CardGridView: View {
    let cards: [CameraCard]
    @ObservedObject var cardManager: CameraCardManager
    let availableSize: CGSize
    
    @State private var selectedCard: CameraCard?
    @State private var showingFullScreen = false
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: gridColumns, spacing: gridSpacing) {
                ForEach(cards) { card in
                    CameraCardView(card: card, cardManager: cardManager)
                        .frame(height: cardHeight)
                        .onTapGesture {
                            cardManager.setActiveCard(card)
                        }
                        .onLongPressGesture {
                            selectedCard = card
                            showingFullScreen = true
                        }
                        .contextMenu {
                            CardContextMenu(card: card, cardManager: cardManager)
                        }
                }
            }
            .padding(gridPadding)
        }
        .fullScreenCover(isPresented: $showingFullScreen) {
            if let card = selectedCard {
                FullScreenCardView(card: card, cardManager: cardManager)
            }
        }
    }
    
    // MARK: - Grid Configuration
    
    private var gridColumns: [GridItem] {
        let cardCount = cards.count
        let columnsCount = calculateOptimalColumns(for: cardCount)
        
        return Array(repeating: GridItem(.flexible(), spacing: gridSpacing), count: columnsCount)
    }
    
    private func calculateOptimalColumns(for cardCount: Int) -> Int {
        let availableWidth = availableSize.width - (gridPadding * 2)
        let minCardWidth: CGFloat = 300
        let maxColumns = max(1, Int(availableWidth / minCardWidth))
        
        switch cardCount {
        case 1:
            return 1
        case 2:
            return min(2, maxColumns)
        case 3...4:
            return min(2, maxColumns)
        case 5...6:
            return min(3, maxColumns)
        default:
            return min(4, maxColumns)
        }
    }
    
    private var cardHeight: CGFloat {
        let columnsCount = gridColumns.count
        let availableHeight = availableSize.height - (gridPadding * 2)
        let rowsCount = max(1, Int(ceil(Double(cards.count) / Double(columnsCount))))
        
        let totalSpacing = CGFloat(rowsCount - 1) * gridSpacing
        let cardHeight = (availableHeight - totalSpacing) / CGFloat(rowsCount)
        
        return max(200, min(cardHeight, 400))
    }
    
    private var gridSpacing: CGFloat {
        return 16
    }
    
    private var gridPadding: CGFloat {
        return 20
    }
}

// MARK: - Card Context Menu

/// 卡片右键菜单
struct CardContextMenu: View {
    @ObservedObject var card: CameraCard
    @ObservedObject var cardManager: CameraCardManager
    
    var body: some View {
        Group {
            // 连接控制
            if card.isConnected {
                Button("断开连接") {
                    Task {
                        await cardManager.disconnectCard(card)
                    }
                }
                
                Button("暂停") {
                    Task {
                        await cardManager.suspendCard(card)
                    }
                }
            } else {
                Button("连接") {
                    Task {
                        await cardManager.connectCard(card)
                    }
                }
            }
            
            Divider()
            
            // 主卡片设置
            if cardManager.primaryCard?.id != card.id {
                Button("设为主卡片") {
                    cardManager.setPrimaryCard(card)
                }
            }
            
            // 质量控制
            Menu("视频质量") {
                ForEach(VideoQuality.allCases, id: \.self) { quality in
                    Button(quality.displayName) {
                        card.configuration.maxVideoQuality = quality
                        Task {
                            _ = ResourceManager.shared.updateResourceAllocation(card.id, newQuality: quality)
                        }
                    }
                }
            }
            
            Divider()
            
            // 移除卡片
            Button("移除", role: .destructive) {
                cardManager.removeCard(card)
            }
        }
    }
}

// MARK: - Full Screen Card View

/// 全屏卡片视图
struct FullScreenCardView: View {
    @ObservedObject var card: CameraCard
    @ObservedObject var cardManager: CameraCardManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 顶部工具栏
                FullScreenToolbar(card: card, onDismiss: {
                    dismiss()
                })
                
                // 视频内容
                if let videoView = card.getVideoView() {
                    videoView
                        .aspectRatio(16/9, contentMode: .fit)
                        .clipped()
                } else {
                    CardPlaceholderView(card: card)
                        .aspectRatio(16/9, contentMode: .fit)
                }
                
                Spacer()
            }
            
            // 全屏虚拟摇杆
            FullScreenVirtualJoysticks(card: card)
        }
        .onAppear {
            cardManager.setActiveCard(card)
        }
    }
}

// MARK: - Full Screen Toolbar

/// 全屏工具栏
struct FullScreenToolbar: View {
    @ObservedObject var card: CameraCard
    let onDismiss: () -> Void
    
    var body: some View {
        HStack {
            // 返回按钮
            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
            }
            
            Spacer()
            
            // 卡片信息
            VStack(alignment: .center, spacing: 2) {
                Text(card.name)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text(card.ipAddress)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
            
            // 工具按钮
            HStack(spacing: 16) {
                // 录制按钮
                Button(action: {
                    // TODO: 实现录制功能
                }) {
                    Image(systemName: "record.circle")
                        .font(.title2)
                        .foregroundColor(.red)
                }
                
                // 截图按钮
                Button(action: {
                    // TODO: 实现截图功能
                }) {
                    Image(systemName: "camera")
                        .font(.title2)
                        .foregroundColor(.white)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 10)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.black.opacity(0.8), Color.clear]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
}

// MARK: - Full Screen Virtual Joysticks

/// 全屏虚拟摇杆
struct FullScreenVirtualJoysticks: View {
    @ObservedObject var card: CameraCard
    
    var body: some View {
        VStack {
            Spacer()
            
            HStack {
                // 左侧PTZ摇杆
                VStack(spacing: 8) {
                    VirtualJoystick(
                        size: 120,
                        style: .ptz,
                        onDirectionChange: { direction in
                            // 处理方向控制
                        },
                        onSpeedChange: { panSpeed, tiltSpeed in
                            card.executePTZMove(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
                        }
                    )
                    
                    Text("PTZ")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                }
                .padding(.leading, 40)
                
                Spacer()
                
                // 右侧变焦摇杆
                VStack(spacing: 8) {
                    VirtualJoystick(
                        size: 120,
                        style: .zoom,
                        onDirectionChange: { direction in
                            if let direction = direction {
                                switch direction {
                                case .up:
                                    card.executeZoomAction(.zoomIn)
                                case .down:
                                    card.executeZoomAction(.zoomOut)
                                default:
                                    break
                                }
                            } else {
                                card.stopPTZ()
                            }
                        },
                        onSpeedChange: { _, _ in }
                    )
                    
                    Text("ZOOM")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                }
                .padding(.trailing, 40)
            }
            .padding(.bottom, 40)
        }
    }
}

// MARK: - Adaptive Grid Layout

/// 自适应网格布局计算器
struct AdaptiveGridLayout {
    let availableSize: CGSize
    let cardCount: Int
    let minCardSize: CGSize
    let spacing: CGFloat
    let padding: CGFloat
    
    var columns: Int {
        let availableWidth = availableSize.width - (padding * 2)
        let maxColumns = max(1, Int(availableWidth / (minCardSize.width + spacing)))
        
        switch cardCount {
        case 1: return 1
        case 2: return min(2, maxColumns)
        case 3...4: return min(2, maxColumns)
        case 5...9: return min(3, maxColumns)
        default: return min(4, maxColumns)
        }
    }
    
    var rows: Int {
        return max(1, Int(ceil(Double(cardCount) / Double(columns))))
    }
    
    var cardSize: CGSize {
        let availableWidth = availableSize.width - (padding * 2) - (CGFloat(columns - 1) * spacing)
        let availableHeight = availableSize.height - (padding * 2) - (CGFloat(rows - 1) * spacing)
        
        let cardWidth = availableWidth / CGFloat(columns)
        let cardHeight = availableHeight / CGFloat(rows)
        
        return CGSize(
            width: max(minCardSize.width, cardWidth),
            height: max(minCardSize.height, min(cardHeight, cardWidth * 0.75)) // 保持16:9比例
        )
    }
}

#Preview {
    let cardManager = CameraCardManager.shared
    
    // 创建一些测试卡片
    let testCards = (1...4).map { index in
        let device = CameraDevice(
            name: "Camera \(index)",
            ipAddress: "192.168.1.\(100 + index)",
            port: 80
        )
        return CameraCard(device: device)
    }
    
    return CardGridView(
        cards: testCards,
        cardManager: cardManager,
        availableSize: CGSize(width: 800, height: 600)
    )
}

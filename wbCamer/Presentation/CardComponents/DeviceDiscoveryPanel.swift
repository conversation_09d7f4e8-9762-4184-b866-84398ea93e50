//
//  DeviceDiscoveryPanel.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import SwiftUI

// MARK: - Device Discovery Panel

/// 设备发现面板 - 显示扫描到的摄像机设备
struct DeviceDiscoveryPanel: View {
    @ObservedObject var cardManager: CameraCardManager
    @ObservedObject private var serviceDiscovery = EagleServiceDiscovery.shared
    @State private var isScanning = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 面板头部
            DiscoveryPanelHeader(
                isScanning: $isScanning,
                onClose: {
                    cardManager.stopDiscovery()
                },
                onRefresh: {
                    refreshDevices()
                }
            )
            
            // 设备列表
            ScrollView {
                LazyVStack(spacing: 12) {
                    if cardManager.availableDevices.isEmpty && !isScanning {
                        EmptyDiscoveryView()
                    } else {
                        ForEach(cardManager.availableDevices) { device in
                            DiscoveredDeviceRow(
                                device: device,
                                cardManager: cardManager,
                                isConnected: isDeviceConnected(device)
                            )
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(maxHeight: 400)
        }
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
        .padding(.horizontal, 40)
        .padding(.top, 60)
        .onAppear {
            startScanning()
        }
        .onDisappear {
            stopScanning()
        }
    }
    
    private func isDeviceConnected(_ device: CameraDevice) -> Bool {
        return cardManager.getAllCards().contains { $0.ipAddress == device.ipAddress }
    }
    
    private func startScanning() {
        isScanning = true
        serviceDiscovery.startScanning()
        
        // 模拟扫描完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            isScanning = false
        }
    }
    
    private func stopScanning() {
        isScanning = false
        serviceDiscovery.stopScanning()
    }
    
    private func refreshDevices() {
        isScanning = true
        serviceDiscovery.stopScanning()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            serviceDiscovery.startScanning()
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                isScanning = false
            }
        }
    }
}

// MARK: - Discovery Panel Header

/// 发现面板头部
struct DiscoveryPanelHeader: View {
    @Binding var isScanning: Bool
    let onClose: () -> Void
    let onRefresh: () -> Void
    
    var body: some View {
        HStack {
            // 标题和状态
            VStack(alignment: .leading, spacing: 4) {
                Text("设备发现")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                HStack(spacing: 8) {
                    if isScanning {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("扫描中...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.caption)
                        Text("扫描完成")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 控制按钮
            HStack(spacing: 12) {
                // 刷新按钮
                Button(action: onRefresh) {
                    Image(systemName: "arrow.clockwise")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
                .disabled(isScanning)
                
                // 关闭按钮
                Button(action: onClose) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(Color(.systemGray6))
    }
}

// MARK: - Discovered Device Row

/// 发现的设备行
struct DiscoveredDeviceRow: View {
    let device: CameraDevice
    @ObservedObject var cardManager: CameraCardManager
    let isConnected: Bool
    @State private var isConnecting = false
    
    var body: some View {
        HStack(spacing: 16) {
            // 设备图标
            Image(systemName: "video")
                .font(.title2)
                .foregroundColor(isConnected ? .green : .blue)
                .frame(width: 32, height: 32)
                .background(Color(.systemGray5))
                .clipShape(Circle())
            
            // 设备信息
            VStack(alignment: .leading, spacing: 4) {
                Text(device.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(device.ipAddress)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                // 设备状态
                HStack(spacing: 8) {
                    StatusBadge(
                        text: isConnected ? "已连接" : "未连接",
                        color: isConnected ? .green : .gray
                    )
                    
                    if let capabilities = device.capabilities {
                        if capabilities.supportsPTZ {
                            StatusBadge(text: "PTZ", color: .blue)
                        }
                        if capabilities.supportsZoom {
                            StatusBadge(text: "变焦", color: .purple)
                        }
                    }
                }
            }
            
            Spacer()
            
            // 连接按钮
            if isConnected {
                Button("已连接") {
                    // 可以选择断开连接
                    if let card = cardManager.getCard(by: device.ipAddress) {
                        Task {
                            await cardManager.disconnectCard(card)
                        }
                    }
                }
                .buttonStyle(.bordered)
                .foregroundColor(.green)
            } else {
                Button(isConnecting ? "连接中..." : "连接") {
                    connectToDevice()
                }
                .buttonStyle(.borderedProminent)
                .disabled(isConnecting)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    private func connectToDevice() {
        isConnecting = true
        
        Task {
            let card = cardManager.createCard(for: device)
            await cardManager.connectCard(card)
            
            DispatchQueue.main.async {
                isConnecting = false
            }
        }
    }
}

// MARK: - Status Badge

/// 状态徽章
struct StatusBadge: View {
    let text: String
    let color: Color
    
    var body: some View {
        Text(text)
            .font(.caption2)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 2)
            .background(color.opacity(0.2))
            .foregroundColor(color)
            .cornerRadius(8)
    }
}

// MARK: - Empty Discovery View

/// 空发现视图
struct EmptyDiscoveryView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("未发现设备")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("请确保摄像机设备已连接到同一局域网")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 8) {
                Text("支持的设备类型:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 12) {
                    DeviceTypeBadge(name: "P2-R1", icon: "video")
                    DeviceTypeBadge(name: "Eagle", icon: "camera")
                }
            }
        }
        .padding(40)
    }
}

// MARK: - Device Type Badge

/// 设备类型徽章
struct DeviceTypeBadge: View {
    let name: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
            
            Text(name)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - Network Info Panel

/// 网络信息面板
struct NetworkInfoPanel: View {
    @ObservedObject private var bandwidthManager = NetworkBandwidthManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("网络状态")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack {
                // 网络类型
                HStack(spacing: 4) {
                    Image(systemName: networkIcon)
                        .foregroundColor(.blue)
                    Text(networkTypeName)
                        .font(.caption)
                }
                
                Spacer()
                
                // 网络质量
                HStack(spacing: 4) {
                    Circle()
                        .fill(qualityColor)
                        .frame(width: 8, height: 8)
                    Text(bandwidthManager.networkQuality.displayName)
                        .font(.caption)
                }
            }
        }
        .padding(12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    private var networkIcon: String {
        switch bandwidthManager.connectionType {
        case .wifi:
            return "wifi"
        case .cellular:
            return "antenna.radiowaves.left.and.right"
        case .wiredEthernet:
            return "cable.connector"
        default:
            return "network"
        }
    }
    
    private var networkTypeName: String {
        switch bandwidthManager.connectionType {
        case .wifi:
            return "WiFi"
        case .cellular:
            return "蜂窝网络"
        case .wiredEthernet:
            return "有线网络"
        default:
            return "未知网络"
        }
    }
    
    private var qualityColor: Color {
        switch bandwidthManager.networkQuality {
        case .excellent: return .green
        case .good: return .yellow
        case .fair: return .orange
        case .poor: return .red
        }
    }
}

#Preview {
    let cardManager = CameraCardManager.shared
    return DeviceDiscoveryPanel(cardManager: cardManager)
        .frame(width: 600, height: 500)
        .background(Color(.systemGray5))
}

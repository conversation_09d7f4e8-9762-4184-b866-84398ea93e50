//
//  MultiCardLayoutView.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import SwiftUI

// MARK: - Multi Card Layout View

/// 多卡片布局视图 - 根据设备类型显示不同的布局
struct MultiCardLayoutView: View {
    @ObservedObject var cardManager: CameraCardManager
    @Environment(\.deviceType) var deviceType
    
    var body: some View {
        Group {
            switch deviceType {
            case .iPad:
                iPadMultiCardLayout()
            case .iPhone:
                iPhoneMultiCardLayout()
            }
        }
        .onAppear {
            // 启动时开始设备发现
            cardManager.startDiscovery()
        }
    }
}

// MARK: - iPad Layout

extension MultiCardLayoutView {
    /// iPad布局 - 网格式卡片布局
    @ViewBuilder
    private func iPadMultiCardLayout() -> some View {
        GeometryReader { geometry in
            ZStack {
                // 主要内容区域
                VStack(spacing: 0) {
                    // 顶部工具栏
                    iPadToolbar()
                        .frame(height: 60)
                    
                    // 卡片网格区域
                    if !cardManager.activeCards.isEmpty {
                        CardGridView(
                            cards: cardManager.activeCards,
                            cardManager: cardManager,
                            availableSize: CGSize(
                                width: geometry.size.width,
                                height: geometry.size.height - 100
                            )
                        )
                    } else {
                        CameraEmptyStateView(cardManager: cardManager)
                    }
                    
                    // 底部状态栏
                    iPadStatusBar()
                        .frame(height: 40)
                }
                
                // 设备发现面板（覆盖层）
                if cardManager.isDiscoveryVisible {
                    DeviceDiscoveryPanel(cardManager: cardManager)
                        .transition(.opacity)
                        .zIndex(1)
                }
                
                // 共享的虚拟摇杆（底部）
                if let activeCard = cardManager.currentActiveCard {
                    SharedVirtualJoysticks(
                        activeCard: activeCard,
                        availableSize: geometry.size
                    )
                    .zIndex(2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.3), value: cardManager.isDiscoveryVisible)
    }
    
    @ViewBuilder
    private func iPadToolbar() -> some View {
        HStack {
            // 应用标题
            Text("wbCamer")
                .font(.title2)
                .fontWeight(.bold)
            
            Spacer()
            
            // 系统状态指示器
            SystemStatusIndicator()
            
            Spacer()
            
            // 工具按钮
            HStack(spacing: 16) {
                // 设备发现按钮
                Button(action: {
                    cardManager.toggleDiscovery()
                }) {
                    Image(systemName: cardManager.isDiscoveryVisible ? "minus.circle" : "plus.circle")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
                
                // 全部断开按钮
                if !cardManager.activeCards.isEmpty {
                    Button(action: {
                        Task {
                            await cardManager.disconnectAllCards()
                        }
                    }) {
                        Image(systemName: "power")
                            .font(.title2)
                            .foregroundColor(.red)
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .background(Color(.systemBackground))
        .shadow(radius: 1)
    }
    
    @ViewBuilder
    private func iPadStatusBar() -> some View {
        HStack {
            // 连接统计
            Text("已连接: \(cardManager.totalConnectedCameras)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            // 资源使用情况
            ResourceUsageSummary(usage: cardManager.systemResourceUsage)
            
            Spacer()
            
            // 当前活跃卡片
            if let activeCard = cardManager.currentActiveCard {
                Text("活跃: \(activeCard.name)")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 20)
        .background(Color(.systemGray6))
    }
}

// MARK: - iPhone Layout

extension MultiCardLayoutView {
    /// iPhone布局 - 轮播式卡片布局
    @ViewBuilder
    private func iPhoneMultiCardLayout() -> some View {
        VStack(spacing: 0) {
            // 顶部工具栏
            iPhoneToolbar()
                .frame(height: 50)
            
            // 主要内容区域
            if !cardManager.activeCards.isEmpty {
                TabView(selection: Binding(
                    get: { cardManager.currentActiveCard?.id ?? UUID() },
                    set: { cardId in
                        if let card = cardManager.getCard(by: cardId) {
                            cardManager.setActiveCard(card)
                        }
                    }
                )) {
                    ForEach(cardManager.activeCards) { card in
                        CameraCardView(card: card, cardManager: cardManager)
                            .tag(card.id)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
            } else {
                CameraEmptyStateView(cardManager: cardManager)
            }
            
            // 底部虚拟摇杆
            if let activeCard = cardManager.currentActiveCard {
                iPhoneVirtualJoysticks(activeCard: activeCard)
                    .frame(height: 120)
            }
        }
        .overlay(
            // 设备发现面板
            Group {
                if cardManager.isDiscoveryVisible {
                    DeviceDiscoveryPanel(cardManager: cardManager)
                        .transition(.move(edge: .top))
                }
            },
            alignment: .top
        )
        .animation(.easeInOut(duration: 0.3), value: cardManager.isDiscoveryVisible)
    }
    
    @ViewBuilder
    private func iPhoneToolbar() -> some View {
        HStack {
            // 卡片切换器
            if !cardManager.activeCards.isEmpty {
                CardSwitcher(
                    cards: cardManager.activeCards,
                    currentCard: cardManager.currentActiveCard,
                    onCardSelected: { card in
                        cardManager.setActiveCard(card)
                    }
                )
            } else {
                Text("wbCamer")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            Spacer()
            
            // 设备发现按钮
            Button(action: {
                cardManager.toggleDiscovery()
            }) {
                Image(systemName: cardManager.isDiscoveryVisible ? "xmark.circle" : "plus.circle")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 16)
        .background(Color(.systemBackground))
        .shadow(radius: 1)
    }
    
    @ViewBuilder
    private func iPhoneVirtualJoysticks(activeCard: CameraCard) -> some View {
        HStack {
            // 左侧PTZ摇杆
            VStack(spacing: 4) {
                VirtualJoystick(
                    size: 80,
                    style: .ptz,
                    onDirectionChange: { direction in
                        // 处理方向控制
                    },
                    onSpeedChange: { panSpeed, tiltSpeed in
                        activeCard.executePTZMove(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
                    }
                )
                Text("PTZ")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 右侧变焦摇杆
            VStack(spacing: 4) {
                VirtualJoystick(
                    size: 80,
                    style: .zoom,
                    onDirectionChange: { direction in
                        // 处理变焦控制
                        if let direction = direction {
                            switch direction {
                            case .up:
                                activeCard.executeZoomAction(.zoomIn)
                            case .down:
                                activeCard.executeZoomAction(.zoomOut)
                            default:
                                break
                            }
                        }
                    },
                    onSpeedChange: { _, _ in }
                )
                Text("ZOOM")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 40)
        .background(Color(.systemBackground))
    }
}

// MARK: - Supporting Views

/// 空状态视图
struct CameraEmptyStateView: View {
    @ObservedObject var cardManager: CameraCardManager
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "video.badge.plus")
                .font(.system(size: 64))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("没有连接的摄像机")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("点击 + 按钮开始扫描局域网中的摄像机设备")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button("开始扫描") {
                cardManager.startDiscovery()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding(40)
    }
}

/// 系统状态指示器
struct SystemStatusIndicator: View {
    @ObservedObject private var resourceManager = ResourceManager.shared
    @ObservedObject private var bandwidthManager = NetworkBandwidthManager.shared
    
    var body: some View {
        HStack(spacing: 12) {
            // 网络质量
            HStack(spacing: 4) {
                Circle()
                    .fill(networkQualityColor)
                    .frame(width: 8, height: 8)
                Text("网络")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 系统负载
            HStack(spacing: 4) {
                Circle()
                    .fill(systemLoadColor)
                    .frame(width: 8, height: 8)
                Text("系统")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var networkQualityColor: Color {
        switch bandwidthManager.networkQuality {
        case .excellent: return .green
        case .good: return .yellow
        case .fair: return .orange
        case .poor: return .red
        }
    }
    
    private var systemLoadColor: Color {
        if resourceManager.systemCPUUsage > 0.8 {
            return .red
        } else if resourceManager.systemCPUUsage > 0.6 {
            return .orange
        } else if resourceManager.systemCPUUsage > 0.4 {
            return .yellow
        } else {
            return .green
        }
    }
}

/// 资源使用摘要
struct ResourceUsageSummary: View {
    let usage: ResourceUsage
    
    var body: some View {
        HStack(spacing: 16) {
            UsageIndicator(label: "内存", value: usage.memoryUsage, unit: "GB", color: .blue)
            UsageIndicator(label: "CPU", value: usage.cpuUsage, unit: "%", color: .orange)
            UsageIndicator(label: "带宽", value: usage.bandwidthUsage, unit: "Mbps", color: .green)
        }
    }
}

/// 使用指示器
struct UsageIndicator: View {
    let label: String
    let value: Double
    let unit: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .center, spacing: 2) {
            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
            Text("\(value, specifier: "%.1f")\(unit)")
                .font(.caption)
                .foregroundColor(color)
                .fontWeight(.medium)
        }
    }
}

/// 卡片切换器（iPhone用）
struct CardSwitcher: View {
    let cards: [CameraCard]
    let currentCard: CameraCard?
    let onCardSelected: (CameraCard) -> Void
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(cards.prefix(3)) { card in
                Button(action: {
                    onCardSelected(card)
                }) {
                    VStack(spacing: 2) {
                        Circle()
                            .fill(card.id == currentCard?.id ? .blue : .gray)
                            .frame(width: 8, height: 8)
                        
                        Text(card.name.prefix(6))
                            .font(.caption2)
                            .foregroundColor(card.id == currentCard?.id ? .blue : .secondary)
                    }
                }
            }
            
            if cards.count > 3 {
                Text("+\(cards.count - 3)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
}

#Preview {
    let cardManager = CameraCardManager.shared
    return MultiCardLayoutView(cardManager: cardManager)
}

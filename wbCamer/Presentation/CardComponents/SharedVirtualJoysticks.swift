//
//  SharedVirtualJoysticks.swift
//  wbCamer
//
//  Created by REKROW on 2025/7/16.
//

import SwiftUI

// MARK: - Shared Virtual Joysticks

/// 共享虚拟摇杆 - 为当前活跃的摄像机卡片提供PTZ控制
struct SharedVirtualJoysticks: View {
    @ObservedObject var activeCard: CameraCard
    let availableSize: CGSize
    @Environment(\.deviceType) var deviceType
    
    var body: some View {
        Group {
            switch deviceType {
            case .iPad:
                iPadJoystickLayout()
            case .iPhone:
                iPhoneJoystickLayout()
            }
        }
        .opacity(activeCard.isConnected ? 1.0 : 0.3)
        .disabled(!activeCard.isConnected)
    }
}

// MARK: - iPad Layout

extension SharedVirtualJoysticks {
    @ViewBuilder
    private func iPadJoystickLayout() -> some View {
        VStack {
            Spacer()
            
            HStack {
                // 左侧PTZ摇杆
                VStack(spacing: 8) {
                    VirtualJoystick(
                        size: joystickSize,
                        style: .ptz,
                        onDirectionChange: { direction in
                            handleJoystickDirectionChange(direction)
                        },
                        onSpeedChange: { panSpeed, tiltSpeed in
                            activeCard.executePTZMove(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
                        }
                    )
                    
                    JoystickLabel(text: "PTZ", isActive: isPTZActive)
                }
                .padding(.leading, joystickPadding)
                
                Spacer()
                
                // 中央活跃卡片指示器
                ActiveCardIndicator(card: activeCard)
                
                Spacer()
                
                // 右侧变焦摇杆
                VStack(spacing: 8) {
                    VirtualJoystick(
                        size: joystickSize,
                        style: .zoom,
                        onDirectionChange: { direction in
                            handleJoystickZoomDirection(direction)
                        },
                        onSpeedChange: { _, _ in }
                    )
                    
                    JoystickLabel(text: "ZOOM", isActive: isZoomActive)
                }
                .padding(.trailing, joystickPadding)
            }
            .padding(.bottom, 40)
        }
    }
    
    @ViewBuilder
    private func iPhoneJoystickLayout() -> some View {
        HStack {
            // 左侧PTZ摇杆
            VStack(spacing: 4) {
                VirtualJoystick(
                    size: 80,
                    style: .ptz,
                    onDirectionChange: { direction in
                        handleJoystickDirectionChange(direction)
                    },
                    onSpeedChange: { panSpeed, tiltSpeed in
                        activeCard.executePTZMove(panSpeed: panSpeed, tiltSpeed: tiltSpeed)
                    }
                )
                JoystickLabel(text: "PTZ", isActive: isPTZActive)
            }
            
            Spacer()
            
            // 右侧变焦摇杆
            VStack(spacing: 4) {
                VirtualJoystick(
                    size: 80,
                    style: .zoom,
                    onDirectionChange: { direction in
                        handleJoystickZoomDirection(direction)
                    },
                    onSpeedChange: { _, _ in }
                )
                JoystickLabel(text: "ZOOM", isActive: isZoomActive)
            }
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - Computed Properties
    
    private var joystickSize: CGFloat {
        switch deviceType {
        case .iPad:
            return min(120, availableSize.width * 0.12)
        case .iPhone:
            return 80
        }
    }
    
    private var joystickPadding: CGFloat {
        switch deviceType {
        case .iPad:
            return max(40, availableSize.width * 0.05)
        case .iPhone:
            return 20
        }
    }
    
    private var isPTZActive: Bool {
        if case .moving = activeCard.state.ptzState {
            return true
        }
        return false
    }
    
    private var isZoomActive: Bool {
        if case .zooming = activeCard.state.ptzState {
            return true
        }
        return false
    }
    
    // MARK: - Event Handlers

    private func handleJoystickDirectionChange(_ direction: JoystickDirection?) {
        // 将JoystickDirection转换为PTZDirection
        let ptzDirection: PTZDirection? = direction?.toPTZDirection()

        // 可以在这里添加方向变化的额外处理逻辑
        // 比如触觉反馈、音效等
        if ptzDirection != nil {
            // 提供触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }
    }

    private func handleJoystickZoomDirection(_ direction: JoystickDirection?) {
        guard let direction = direction else {
            activeCard.stopPTZ()
            return
        }

        switch direction {
        case .up:
            activeCard.executeZoomAction(.zoomIn)
        case .down:
            activeCard.executeZoomAction(.zoomOut)
        default:
            break
        }

        // 提供触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Joystick Label

/// 摇杆标签
struct JoystickLabel: View {
    let text: String
    let isActive: Bool
    
    var body: some View {
        Text(text)
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(isActive ? .blue : .secondary)
            .scaleEffect(isActive ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isActive)
    }
}

// MARK: - Active Card Indicator

/// 活跃卡片指示器
struct ActiveCardIndicator: View {
    @ObservedObject var card: CameraCard
    
    var body: some View {
        VStack(spacing: 8) {
            // 卡片图标
            ZStack {
                Circle()
                    .fill(connectionColor.opacity(0.2))
                    .frame(width: 60, height: 60)
                
                Image(systemName: "video")
                    .font(.title2)
                    .foregroundColor(connectionColor)
            }
            
            // 卡片信息
            VStack(spacing: 2) {
                Text(card.name)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(card.ipAddress)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            // 状态指示器
            HStack(spacing: 4) {
                Circle()
                    .fill(connectionColor)
                    .frame(width: 6, height: 6)
                
                Text(statusText)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground).opacity(0.9))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private var connectionColor: Color {
        switch card.state.connectionState {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .disconnected:
            return .gray
        case .error:
            return .red
        case .suspended:
            return .yellow
        }
    }
    
    private var statusText: String {
        switch card.state.connectionState {
        case .connected:
            return "已连接"
        case .connecting:
            return "连接中"
        case .disconnected:
            return "未连接"
        case .error:
            return "错误"
        case .suspended:
            return "已暂停"
        }
    }
}

// MARK: - Virtual Joystick (使用 VirtualJoystick.swift 中的定义)

// VirtualJoystick 已在 VirtualJoystick.swift 中定义，此处移除重复定义
/*
struct VirtualJoystick: View {
    let size: CGFloat
    let style: JoystickStyle
    let onDirectionChange: (PTZDirection?) -> Void
    let onSpeedChange: (Float, Float) -> Void

    @State private var knobPosition: CGPoint = .zero
    @State private var isDragging: Bool = false

    private let knobSize: CGFloat
    private let maxDistance: CGFloat

    init(size: CGFloat, style: JoystickStyle, onDirectionChange: @escaping (PTZDirection?) -> Void, onSpeedChange: @escaping (Float, Float) -> Void) {
        self.size = size
        self.style = style
        self.onDirectionChange = onDirectionChange
        self.onSpeedChange = onSpeedChange
        self.knobSize = size * 0.3
        self.maxDistance = (size - knobSize) / 2
    }

    var body: some View {
        ZStack {
            // 外圈
            Circle()
                .fill(style.outerColor.opacity(0.3))
                .frame(width: size, height: size)
                .overlay(
                    Circle()
                        .stroke(style.outerColor, lineWidth: 2)
                )

            // 内圈（摇杆）
            Circle()
                .fill(style.knobColor)
                .frame(width: knobSize, height: knobSize)
                .offset(x: knobPosition.x, y: knobPosition.y)
                .scaleEffect(isDragging ? 1.1 : 1.0)
                .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
        }
        .gesture(
            DragGesture()
                .onChanged { value in
                    handleDragChanged(value)
                }
                .onEnded { _ in
                    handleDragEnded()
                }
        )
        .animation(.easeOut(duration: 0.2), value: knobPosition)
        .animation(.easeInOut(duration: 0.1), value: isDragging)
    }*/
/*
    private func handleDragChanged(_ value: DragGesture.Value) {
        isDragging = true

        let distance = sqrt(pow(value.translation.x, 2) + pow(value.translation.y, 2))

        if distance <= maxDistance {
            knobPosition = value.translation
        } else {
            let angle = atan2(value.translation.y, value.translation.x)
            knobPosition = CGPoint(
                x: cos(angle) * maxDistance,
                y: sin(angle) * maxDistance
            )
        }

        // 计算速度和方向
        let normalizedX = Float(knobPosition.x / maxDistance)
        let normalizedY = Float(-knobPosition.y / maxDistance) // Y轴反转

        // 根据摇杆样式处理不同的控制逻辑
        switch style {
        case .ptz:
            onSpeedChange(normalizedX, normalizedY)

            // 确定方向
            if abs(normalizedX) > 0.1 || abs(normalizedY) > 0.1 {
                let direction = determineDirection(x: normalizedX, y: normalizedY)
                onDirectionChange(direction)
            } else {
                onDirectionChange(nil)
            }

        case .zoom:
            // 变焦摇杆只处理垂直方向
            if abs(normalizedY) > 0.1 {
                let direction: PTZDirection = normalizedY > 0 ? .up : .down
                onDirectionChange(direction)
            } else {
                onDirectionChange(nil)
            }
        }
    }

    private func handleDragEnded() {
        isDragging = false
        knobPosition = .zero
        onDirectionChange(nil)
        onSpeedChange(0, 0)
    }

    private func determineDirection(x: Float, y: Float) -> PTZDirection {
        let threshold: Float = 0.5

        if abs(x) > threshold && abs(y) > threshold {
            // 对角线方向
            if x > 0 && y > 0 {
                return .upRight
            } else if x > 0 && y < 0 {
                return .downRight
            } else if x < 0 && y > 0 {
                return .upLeft
            } else {
                return .downLeft
            }
        } else {
            // 主要方向
            if abs(x) > abs(y) {
                return x > 0 ? .right : .left
            } else {
                return y > 0 ? .up : .down
            }
        }
    }
}*/

// MARK: - Joystick Style (使用 VirtualJoystick.swift 中的定义)

// JoystickStyle 已在 VirtualJoystick.swift 中定义，此处移除重复定义
/*
/// 摇杆样式
enum JoystickStyle {
    case ptz
    case zoom

    var outerColor: Color {
        switch self {
        case .ptz:
            return .blue
        case .zoom:
            return .green
        }
    }

    var knobColor: Color {
        switch self {
        case .ptz:
            return .blue
        case .zoom:
            return .green
        }
    }
}*/

#Preview {
    let device = CameraDevice(name: "Test Camera", ipAddress: "*************", port: 80)
    let card = CameraCard(device: device)
    
    return SharedVirtualJoysticks(
        activeCard: card,
        availableSize: CGSize(width: 800, height: 600)
    )
    .frame(width: 800, height: 600)
    .background(Color(.systemGray5))
}

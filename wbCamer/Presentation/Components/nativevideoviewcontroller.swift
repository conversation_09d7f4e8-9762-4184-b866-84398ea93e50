//
//  NativeVideoViewController.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import UIKit
import SwiftUI
import WebRTC

// MARK: - 完全原生的视频显示控制器

class NativeVideoViewController: UIViewController {
    
    // MARK: - Properties
    private var videoView: RTCMTLVideoView?
    private var currentVideoTrack: RTCVideoTrack?
    private var statusLabel: UILabel!
    private var connectionIndicator: UIView!
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        print("[NativeVideoViewController] 🎬 viewDidLoad")
        setupUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        print("[NativeVideoViewController] 🎬 viewWillAppear")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        print("[NativeVideoViewController] 🎬 viewDidAppear")
        print("[NativeVideoViewController] 🔍 View hierarchy check:")
        print("[NativeVideoViewController] - View frame: \(view.frame)")
        print("[NativeVideoViewController] - View bounds: \(view.bounds)")
        print("[NativeVideoViewController] - View window: \(view.window != nil ? "EXISTS" : "NIL")")
        print("[NativeVideoViewController] - View superview: \(view.superview != nil ? "EXISTS" : "NIL")")
        
        // 延迟检查
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.performDetailedCheck()
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        print("[NativeVideoViewController] 🎬 viewDidLayoutSubviews")
        print("[NativeVideoViewController] - View frame: \(view.frame)")
        print("[NativeVideoViewController] - View bounds: \(view.bounds)")
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        print("[NativeVideoViewController] 🎨 Setting up UI")
        
        // 设置背景色为明显的颜色
        view.backgroundColor = UIColor.systemOrange
        
        // 添加标题标签
        let titleLabel = UILabel()
        titleLabel.text = "🎥 NATIVE VIDEO CONTROLLER 🎥"
        titleLabel.textColor = .white
        titleLabel.font = UIFont.boldSystemFont(ofSize: 20)
        titleLabel.textAlignment = .center
        titleLabel.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        titleLabel.layer.cornerRadius = 8
        titleLabel.clipsToBounds = true
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(titleLabel)
        
        // 添加状态标签
        statusLabel = UILabel()
        statusLabel.text = "等待视频连接..."
        statusLabel.textColor = .white
        statusLabel.font = UIFont.systemFont(ofSize: 16)
        statusLabel.textAlignment = .center
        statusLabel.backgroundColor = UIColor.blue.withAlphaComponent(0.8)
        statusLabel.layer.cornerRadius = 6
        statusLabel.clipsToBounds = true
        statusLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(statusLabel)
        
        // 添加连接指示器
        connectionIndicator = UIView()
        connectionIndicator.backgroundColor = UIColor.red
        connectionIndicator.layer.cornerRadius = 10
        connectionIndicator.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(connectionIndicator)
        
        // 创建视频视图
        createVideoView()
        
        // 设置约束
        setupConstraints(titleLabel: titleLabel)
        
        print("[NativeVideoViewController] ✅ UI setup complete")
    }
    
    private func createVideoView() {
        print("[NativeVideoViewController] 📺 Creating video view")
        
        let videoView = RTCMTLVideoView()
        videoView.backgroundColor = UIColor.systemPurple
        videoView.contentMode = .scaleAspectFit
        videoView.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加明显的边框
        videoView.layer.borderColor = UIColor.systemYellow.cgColor
        videoView.layer.borderWidth = 4.0
        videoView.layer.cornerRadius = 8
        
        view.addSubview(videoView)
        self.videoView = videoView
        
        print("[NativeVideoViewController] ✅ Video view created and added")
    }
    
    private func setupConstraints(titleLabel: UILabel) {
        guard let videoView = self.videoView else { return }
        
        NSLayoutConstraint.activate([
            // 标题标签
            titleLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            titleLabel.heightAnchor.constraint(equalToConstant: 50),
            
            // 状态标签
            statusLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
            statusLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(equalTo: connectionIndicator.leadingAnchor, constant: -10),
            statusLabel.heightAnchor.constraint(equalToConstant: 30),
            
            // 连接指示器
            connectionIndicator.centerYAnchor.constraint(equalTo: statusLabel.centerYAnchor),
            connectionIndicator.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            connectionIndicator.widthAnchor.constraint(equalToConstant: 20),
            connectionIndicator.heightAnchor.constraint(equalToConstant: 20),
            
            // 视频视图
            videoView.topAnchor.constraint(equalTo: statusLabel.bottomAnchor, constant: 20),
            videoView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            videoView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            videoView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20)
        ])
    }
    
    // MARK: - Video Track Management
    
    func setVideoTrack(_ videoTrack: RTCVideoTrack?) {
        print("[NativeVideoViewController] 🎥 setVideoTrack called")
        print("[NativeVideoViewController] - New track: \(videoTrack != nil ? "EXISTS" : "NIL")")
        
        // 移除旧的视频轨道
        if let oldTrack = currentVideoTrack, let videoView = self.videoView {
            oldTrack.remove(videoView)
            print("[NativeVideoViewController] ❌ Removed old video track")
        }
        
        // 添加新的视频轨道
        if let newTrack = videoTrack, let videoView = self.videoView {
            newTrack.add(videoView)
            currentVideoTrack = newTrack
            
            // 更新状态
            DispatchQueue.main.async {
                self.statusLabel.text = "视频已连接"
                self.connectionIndicator.backgroundColor = UIColor.green
                print("[NativeVideoViewController] ✅ Video track added and UI updated")
            }
        } else {
            currentVideoTrack = nil
            
            // 更新状态
            DispatchQueue.main.async {
                self.statusLabel.text = "等待视频连接..."
                self.connectionIndicator.backgroundColor = UIColor.red
                print("[NativeVideoViewController] ⏳ Waiting for video track")
            }
        }
    }
    
    // MARK: - Diagnostics
    
    private func performDetailedCheck() {
        print("[NativeVideoViewController] 🔍 Performing detailed check:")
        print("[NativeVideoViewController] - Main view:")
        print("[NativeVideoViewController]   * frame: \(view.frame)")
        print("[NativeVideoViewController]   * bounds: \(view.bounds)")
        print("[NativeVideoViewController]   * window: \(view.window != nil ? "EXISTS" : "NIL")")
        print("[NativeVideoViewController]   * superview: \(view.superview != nil ? "EXISTS" : "NIL")")
        print("[NativeVideoViewController]   * backgroundColor: \(view.backgroundColor?.description ?? "NIL")")
        print("[NativeVideoViewController]   * isHidden: \(view.isHidden)")
        print("[NativeVideoViewController]   * alpha: \(view.alpha)")
        
        if let videoView = self.videoView {
            print("[NativeVideoViewController] - Video view:")
            print("[NativeVideoViewController]   * frame: \(videoView.frame)")
            print("[NativeVideoViewController]   * bounds: \(videoView.bounds)")
            print("[NativeVideoViewController]   * window: \(videoView.window != nil ? "EXISTS" : "NIL")")
            print("[NativeVideoViewController]   * superview: \(videoView.superview != nil ? "EXISTS" : "NIL")")
            print("[NativeVideoViewController]   * backgroundColor: \(videoView.backgroundColor?.description ?? "NIL")")
            print("[NativeVideoViewController]   * isHidden: \(videoView.isHidden)")
            print("[NativeVideoViewController]   * alpha: \(videoView.alpha)")
        }
        
        print("[NativeVideoViewController] - Status label:")
        print("[NativeVideoViewController]   * frame: \(statusLabel.frame)")
        print("[NativeVideoViewController]   * text: \(statusLabel.text ?? "NIL")")
        print("[NativeVideoViewController]   * window: \(statusLabel.window != nil ? "EXISTS" : "NIL")")
        
        print("[NativeVideoViewController] - Connection indicator:")
        print("[NativeVideoViewController]   * frame: \(connectionIndicator.frame)")
        print("[NativeVideoViewController]   * backgroundColor: \(connectionIndicator.backgroundColor?.description ?? "NIL")")
        print("[NativeVideoViewController]   * window: \(connectionIndicator.window != nil ? "EXISTS" : "NIL")")
    }
}

// MARK: - SwiftUI 包装器

struct NativeVideoView: UIViewControllerRepresentable {
    let webRTCStreamerClient: WebRTCStreamerClient

    func makeUIViewController(context: Context) -> NativeVideoViewController {
        print("[NativeVideoView] 🎬 Creating NativeVideoViewController")
        let controller = NativeVideoViewController()

        // 立即设置视频轨道（如果有的话）
        if let videoTrack = webRTCStreamerClient.remoteVideoTrack {
            controller.setVideoTrack(videoTrack)
        }

        return controller
    }

    func updateUIViewController(_ uiViewController: NativeVideoViewController, context: Context) {
        print("[NativeVideoView] 🔄 updateUIViewController called")
        print("[NativeVideoView] - Current video track: \(webRTCStreamerClient.remoteVideoTrack != nil ? "EXISTS" : "NIL")")

        // 更新视频轨道
        uiViewController.setVideoTrack(webRTCStreamerClient.remoteVideoTrack)
    }
}

// MARK: - Preview

struct NativeVideoView_Previews: PreviewProvider {
    static var previews: some View {
        NativeVideoView(webRTCStreamerClient: WebRTCStreamerClient())
    }
}

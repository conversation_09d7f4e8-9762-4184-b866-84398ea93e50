//
//  NetworkStatusIndicator.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI

// MARK: - Network Status Indicator

struct NetworkStatusIndicator: View {
    @StateObject private var networkMonitor = NetworkMonitor.shared
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(statusColor)
                .frame(width: 8, height: 8)
            
            Text(networkMonitor.networkInfo.connectionType)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var statusColor: Color {
        switch networkMonitor.networkInfo.status {
        case .reachable:
            return .green
        case .notReachable:
            return .red
        case .unknown:
            return .orange
        }
    }
}

#Preview {
    NetworkStatusIndicator()
}

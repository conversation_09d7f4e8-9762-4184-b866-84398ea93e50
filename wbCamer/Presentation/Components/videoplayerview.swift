//
//  VideoPlayerView.swift
//  wbCamer
//
//  Created by Assistant on 2024-12-19.
//

import SwiftUI
import WebRTC
import MetalKit

// VideoPlayerView已被OptimizedVideoPlayerView替代，此处保留VideoPlayerControlView

/// 带有控制界面的视频播放器 (支持 WebRTC Streamer)
struct VideoPlayerControlView: View {
    @ObservedObject var webRTCStreamerClient: OptimizedWebRTCStreamerClient
    @State private var isFullscreen = false
    @State private var showControls = true
    @State private var controlsTimer: Timer?

    var body: some View {
        let _ = print("[VideoPlayerControlView] Body refresh - remoteVideoTrack: \(webRTCStreamerClient.remoteVideoTrack != nil), isConnected: \(webRTCStreamerClient.isConnected)")

        ZStack {
            // 测试用的背景 - 应该能看到这个
            Rectangle()
                .fill(Color.yellow)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .overlay(
                    Text("🎥 VIDEO AREA 🎥")
                        .font(.title)
                        .foregroundColor(.black)
                )

            // 视频播放器 - 使用 id 来防止不必要的重建
            OptimizedVideoPlayerView(videoTrack: webRTCStreamerClient.remoteVideoTrack)
                .id("video-player-main") // 固定 ID 防止重建
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.blue) // 改为蓝色以便调试
                .clipped() // 不裁剪
                .allowsHitTesting(true) // 允许触摸
                .onTapGesture {
                    toggleControls()
                }
                .onChange(of: webRTCStreamerClient.remoteVideoTrack) { oldValue, newValue in
                    print("[VideoPlayerControlView] Video track changed from \(String(describing: oldValue)) to \(String(describing: newValue))")
                }
                .onAppear {
                    print("[VideoPlayerControlView] VideoPlayerControlView appeared")
                    print("[VideoPlayerControlView] Current remoteVideoTrack: \(String(describing: webRTCStreamerClient.remoteVideoTrack))")
                }

            // AI检测叠加层
            AIDetectionOverlayView(
                webSocketManager: WebSocketManager.shared,
                videoSize: CGSize(width: 640, height: 480)
            )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .allowsHitTesting(false) // 不拦截触摸事件

            // 连接状态覆盖层
            if !webRTCStreamerClient.isConnected {
                ConnectionStatusOverlay(connectionState: webRTCStreamerClient.connectionState)
            }

            // 控制界面
            if showControls {
                VideoControlsOverlay(
                    webRTCStreamerClient: webRTCStreamerClient,
                    isFullscreen: $isFullscreen,
                    onHideControls: {
                        hideControls()
                    }
                )
            }
        }
        .background(Color.black)
        .onAppear {
            startControlsTimer()
        }
        .onDisappear {
            stopControlsTimer()
        }
    }
    
    private func toggleControls() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showControls.toggle()
        }
        
        if showControls {
            startControlsTimer()
        } else {
            stopControlsTimer()
        }
    }
    
    private func hideControls() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showControls = false
        }
        stopControlsTimer()
    }
    
    private func startControlsTimer() {
        stopControlsTimer()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            hideControls()
        }
    }
    
    private func stopControlsTimer() {
        controlsTimer?.invalidate()
        controlsTimer = nil
    }
}

/// 连接状态覆盖层
struct ConnectionStatusOverlay: View {
    let connectionState: RTCPeerConnectionState
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.7)
            
            VStack(spacing: 16) {
                // 状态图标
                Image(systemName: statusIcon)
                    .font(.system(size: 48))
                    .foregroundColor(statusColor)
                
                // 状态文本
                Text(statusText)
                    .font(.headline)
                    .foregroundColor(.white)
                
                // 加载指示器
                if isConnecting {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                }
            }
        }
    }
    
    private var statusIcon: String {
        switch connectionState {
        case .new, .connecting:
            return "wifi.circle"
        case .connected:
            return "checkmark.circle.fill"
        case .disconnected:
            return "wifi.slash"
        case .failed:
            return "exclamationmark.triangle.fill"
        case .closed:
            return "xmark.circle.fill"
        @unknown default:
            return "questionmark.circle"
        }
    }
    
    private var statusColor: Color {
        switch connectionState {
        case .new, .connecting:
            return .blue
        case .connected:
            return .green
        case .disconnected, .closed:
            return .gray
        case .failed:
            return .red
        @unknown default:
            return .gray
        }
    }
    
    private var statusText: String {
        switch connectionState {
        case .new:
            return "准备连接"
        case .connecting:
            return "正在连接..."
        case .connected:
            return "已连接"
        case .disconnected:
            return "连接断开"
        case .failed:
            return "连接失败"
        case .closed:
            return "连接已关闭"
        @unknown default:
            return "未知状态"
        }
    }
    
    private var isConnecting: Bool {
        connectionState == .connecting
    }
}

/// 视频控制覆盖层 (支持 WebRTC Streamer)
struct VideoControlsOverlay: View {
    @ObservedObject var webRTCStreamerClient: OptimizedWebRTCStreamerClient
    @Binding var isFullscreen: Bool
    let onHideControls: () -> Void
    
    var body: some View {
        VStack {
            // 顶部控制栏
            HStack {
                // 连接状态指示器
                HStack(spacing: 8) {
                    Circle()
                        .fill(webRTCStreamerClient.isConnected ? Color.green : Color.red)
                        .frame(width: 8, height: 8)

                    Text(webRTCStreamerClient.isConnected ? "已连接" : "未连接")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.black.opacity(0.6))
                .cornerRadius(16)
                
                Spacer()
                
                // 全屏按钮
                Button(action: {
                    isFullscreen.toggle()
                }) {
                    Image(systemName: isFullscreen ? "arrow.down.right.and.arrow.up.left" : "arrow.up.left.and.arrow.down.right")
                        .font(.title2)
                        .foregroundColor(.white)
                        .padding(8)
                        .background(Color.black.opacity(0.6))
                        .clipShape(Circle())
                }
            }
            .padding(.horizontal)
            .padding(.top)
            
            Spacer()
            
            // 底部控制栏
            HStack {
                // 断开连接按钮
                Button(action: {
                    webRTCStreamerClient.disconnect()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "wifi.slash")
                        Text("断开连接")
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(Color.red.opacity(0.8))
                    .cornerRadius(20)
                }
                
                Spacer()
                
                // 质量信息
                if webRTCStreamerClient.isConnected {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("WebRTC")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.8))
                        
                        HStack(spacing: 4) {
                            Image(systemName: "wifi")
                                .font(.caption2)
                            Text(iceConnectionStateText)
                                .font(.caption2)
                        }
                        .foregroundColor(.white.opacity(0.8))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.black.opacity(0.6))
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal)
            .padding(.bottom)
        }
        .background(
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color.black.opacity(0.6), location: 0),
                    .init(color: Color.clear, location: 0.3),
                    .init(color: Color.clear, location: 0.7),
                    .init(color: Color.black.opacity(0.6), location: 1)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
    }
    
    private var iceConnectionStateText: String {
        switch webRTCStreamerClient.iceConnectionState {
        case .new:
            return "新建"
        case .checking:
            return "检查中"
        case .connected:
            return "已连接"
        case .completed:
            return "完成"
        case .failed:
            return "失败"
        case .disconnected:
            return "断开"
        case .closed:
            return "关闭"
        case .count:
            return "计数"
        @unknown default:
            return "未知"
        }
    }
}

// MARK: - Preview

struct VideoPlayerView_Previews: PreviewProvider {
    static var previews: some View {
        VideoPlayerControlView(webRTCStreamerClient: OptimizedWebRTCStreamerClient())
            .preferredColorScheme(.dark)
    }
}
//
//  OptimizedVideoPlayerView.swift
//  wbCamer
//
//  优化的WebRTC视频播放器视图 - 减少延迟版本
//

import SwiftUI
import WebRTC
import MetalKit

/// 优化的WebRTC视频播放器视图 - 减少延迟版本
struct OptimizedVideoPlayerView: View {
    let videoTrack: RTCVideoTrack?
    let contentMode: UIView.ContentMode
    @State private var videoSize: CGSize = .zero
    
    init(videoTrack: RTCVideoTrack?, contentMode: UIView.ContentMode = .scaleAspectFit) {
        self.videoTrack = videoTrack
        self.contentMode = contentMode
    }
    
    var body: some View {
        ZStack {
            // 底层视频播放器
            OptimizedVideoPlayerUIView(videoTrack: videoTrack, contentMode: contentMode)
                .onReceive(NotificationCenter.default.publisher(for: .videoSizeChanged)) { notification in
                    if let size = notification.object as? CGSize {
                        videoSize = size
                    }
                }
            
            // AI检测叠加层
            AIDetectionOverlayView(
                webSocketManager: CameraManager.shared.webSocketManager,
                videoSize: videoSize
            )
        }
    }
}

/// 内部UIViewRepresentable视频播放器
struct OptimizedVideoPlayerUIView: UIViewRepresentable {
    let videoTrack: RTCVideoTrack?
    let contentMode: UIView.ContentMode
    
    init(videoTrack: RTCVideoTrack?, contentMode: UIView.ContentMode = .scaleAspectFit) {
        self.videoTrack = videoTrack
        self.contentMode = contentMode
    }
    
    func makeUIView(context: Context) -> RTCMTLVideoView {
        let videoView = RTCMTLVideoView(frame: .zero)
        videoView.contentMode = contentMode
        videoView.delegate = context.coordinator
        
        // 最小化配置，避免不必要的操作
        videoView.backgroundColor = UIColor.black
        videoView.translatesAutoresizingMaskIntoConstraints = false
        
        return videoView
    }
    
    func updateUIView(_ uiView: RTCMTLVideoView, context: Context) {
        // 简化更新逻辑，只在必要时更新
        guard let videoTrack = videoTrack,
              context.coordinator.currentVideoTrack !== videoTrack else {
            return
        }
        
        // 原子操作：先移除再添加
        context.coordinator.removeVideoTrack(from: uiView)
        videoTrack.add(uiView)
        context.coordinator.setCurrentVideoTrack(videoTrack)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator()
    }
    
    class Coordinator: NSObject, RTCVideoViewDelegate {
        private var _currentVideoTrack: RTCVideoTrack?
        
        var currentVideoTrack: RTCVideoTrack? {
            return _currentVideoTrack
        }
        
        func setCurrentVideoTrack(_ track: RTCVideoTrack?) {
            _currentVideoTrack = track
        }
        
        func removeVideoTrack(from videoView: RTCMTLVideoView) {
            _currentVideoTrack?.remove(videoView)
            _currentVideoTrack = nil
        }
        
        // RTCVideoViewDelegate - 处理视频尺寸变化并发送通知
        func videoView(_ videoView: RTCVideoRenderer, didChangeVideoSize size: CGSize) {
            DispatchQueue.main.async {
                NotificationCenter.default.post(
                    name: .videoSizeChanged,
                    object: size
                )
            }
        }
    }
}

// MARK: - Notification Extension
extension Notification.Name {
    static let videoSizeChanged = Notification.Name("videoSizeChanged")
}
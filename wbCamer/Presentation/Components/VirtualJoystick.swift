//
//  VirtualJoystick.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI

// MARK: - Joystick Style

enum JoystickStyle {
    case standard
    case ptz
    case zoom

    var backgroundColor: Color {
        switch self {
        case .standard:
            return Color.black.opacity(0.3)
        case .ptz:
            return Color.blue.opacity(0.2)
        case .zoom:
            return Color.green.opacity(0.2)
        }
    }

    var borderColor: Color {
        switch self {
        case .standard:
            return Color.white.opacity(0.5)
        case .ptz:
            return Color.blue.opacity(0.8)
        case .zoom:
            return Color.green.opacity(0.8)
        }
    }

    var borderWidth: CGFloat {
        return 2
    }

    var knobColor: Color {
        switch self {
        case .standard:
            return Color.white.opacity(0.8)
        case .ptz:
            return Color.blue.opacity(0.9)
        case .zoom:
            return Color.green.opacity(0.9)
        }
    }

    var knobBorderColor: Color {
        return Color.white
    }

    var knobBorderWidth: CGFloat {
        return 2
    }

    var knobCenterColor: Color {
        return Color.white.opacity(0.6)
    }

    var activeScale: CGFloat {
        return 1.1
    }

    var showDirectionIndicators: Bool {
        switch self {
        case .standard:
            return false
        case .ptz, .zoom:
            return true
        }
    }
}

// MARK: - Direction Indicators

struct DirectionIndicators: View {
    let size: CGFloat
    let style: JoystickStyle

    var body: some View {
        ZStack {
            // 上方向指示
            Image(systemName: "chevron.up")
                .font(.system(size: size * 0.08, weight: .semibold))
                .foregroundColor(style.borderColor)
                .offset(y: -size * 0.35)

            // 下方向指示
            Image(systemName: "chevron.down")
                .font(.system(size: size * 0.08, weight: .semibold))
                .foregroundColor(style.borderColor)
                .offset(y: size * 0.35)

            // 左方向指示
            Image(systemName: "chevron.left")
                .font(.system(size: size * 0.08, weight: .semibold))
                .foregroundColor(style.borderColor)
                .offset(x: -size * 0.35)

            // 右方向指示
            Image(systemName: "chevron.right")
                .font(.system(size: size * 0.08, weight: .semibold))
                .foregroundColor(style.borderColor)
                .offset(x: size * 0.35)
        }
    }
}

// MARK: - Virtual Joystick Component

struct VirtualJoystick: View {
    let size: CGFloat
    let onDirectionChange: (JoystickDirection?) -> Void
    let onSpeedChange: (Float, Float) -> Void  // (panSpeed, tiltSpeed)
    let style: JoystickStyle

    @State private var knobPosition: CGPoint = .zero
    @State private var isDragging = false
    @State private var currentDirection: JoystickDirection?

    private let knobSize: CGFloat
    private let maxDistance: CGFloat

    init(size: CGFloat = 240,
         style: JoystickStyle = .standard,
         onDirectionChange: @escaping (JoystickDirection?) -> Void = { _ in },
         onSpeedChange: @escaping (Float, Float) -> Void) {
        self.size = size
        self.style = style
        self.onDirectionChange = onDirectionChange
        self.onSpeedChange = onSpeedChange
        self.knobSize = size * 0.25 
        self.maxDistance = (size - knobSize) / 2
    }
    
    var body: some View {
        ZStack {
            // 外圈背景
            Circle()
                .fill(style.backgroundColor)
                .frame(width: size, height: size)
                .overlay(
                    Circle()
                        .stroke(style.borderColor, lineWidth: style.borderWidth)
                )

            // 方向指示器（可选）
            if style.showDirectionIndicators {
                DirectionIndicators(size: size, style: style)
            }

            // 内圈摇杆
            Circle()
                .fill(style.knobColor)
                .frame(width: knobSize, height: knobSize)
                .overlay(
                    Circle()
                        .stroke(style.knobBorderColor, lineWidth: style.knobBorderWidth)
                )
                .overlay(
                    // 摇杆中心点
                    Circle()
                        .fill(style.knobCenterColor)
                        .frame(width: knobSize * 0.3, height: knobSize * 0.3)
                )
                .offset(x: knobPosition.x, y: knobPosition.y)
                .scaleEffect(isDragging ? style.activeScale : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isDragging)
                .id("joystick-knob-\(isDragging ? "active" : "inactive")")
        }
        .gesture(
            DragGesture()
                .onChanged { value in
                    if !isDragging {
                        isDragging = true
                    }
                    
                    let translation = value.translation
                    let distance = sqrt(translation.width * translation.width + translation.height * translation.height)

                    if distance <= maxDistance {
                        knobPosition = CGPoint(x: translation.width, y: translation.height)
                    } else {
                        let angle = atan2(translation.height, translation.width)
                        knobPosition = CGPoint(
                            x: cos(angle) * maxDistance,
                            y: sin(angle) * maxDistance
                        )
                    }
                    
                    updateDirection()
                }
                .onEnded { _ in
                    isDragging = false
                    knobPosition = .zero
                    currentDirection = nil
                    onDirectionChange(nil)
                    onSpeedChange(0.0, 0.0)  // 停止移动
                }
        )
    }
    
    private func updateDirection() {
        let x = knobPosition.x
        let y = knobPosition.y

        // 计算pan和tilt速度 (基于P2-R1源码算法)
        // 将位置转换为-1到1的范围，然后映射到0-1的fspeed
        let panSpeed = Float(max(-1, min(1, x / maxDistance)))
        let tiltSpeed = Float(max(-1, min(1, -y / maxDistance)))  // Y轴反向

        // 发送速度变化
        onSpeedChange(panSpeed, tiltSpeed)

        // 保持原有的方向检测逻辑（用于兼容性）
        let threshold: CGFloat = maxDistance * 0.3
        var newDirection: JoystickDirection?

        if abs(x) > threshold || abs(y) > threshold {
            if abs(x) > abs(y) {
                newDirection = x > 0 ? .right : .left
            } else {
                newDirection = y > 0 ? .down : .up
            }
        }

        if newDirection != currentDirection {
            currentDirection = newDirection
            //print("[VirtualJoystick] Direction: \(String(describing: newDirection)), Pan: \(panSpeed), Tilt: \(tiltSpeed)")
            onDirectionChange(newDirection)
        }
    }
}

// MARK: - Joystick Direction

enum JoystickDirection {
    case up
    case down
    case left
    case right
    
    var ptzAction: PTZControlRequest.PTZAction {
        switch self {
        case .up:
            return PTZControlRequest.PTZAction.up
        case .down:
            return PTZControlRequest.PTZAction.down
        case .left:
            return PTZControlRequest.PTZAction.left
        case .right:
            return PTZControlRequest.PTZAction.right
        }
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.black
            .ignoresSafeArea()

        VirtualJoystick(size: 200, style: .ptz) { direction in
            print("Joystick Direction: \(String(describing: direction))")
        } onSpeedChange: { panSpeed, tiltSpeed in
            print("Joystick Speed: pan=\(panSpeed), tilt=\(tiltSpeed)")
        }
    }
}

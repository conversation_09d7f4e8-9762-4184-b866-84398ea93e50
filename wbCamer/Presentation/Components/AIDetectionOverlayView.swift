//
//  AIDetectionOverlayView.swift
//  wbCamer
//
//  Created by Assistant on 2024-12-19.
//

import SwiftUI
import Combine

/// AI检测框叠加视图
/// 在视频预览上绘制目标检测框和目标ID
struct AIDetectionOverlayView: View {
    @ObservedObject var webSocketManager: WebSocketManager
    @State private var detectionTargets: [AIDetectionTarget] = []
    @State private var cancellables = Set<AnyCancellable>()
    
    let videoSize: CGSize
    
    init(webSocketManager: WebSocketManager, videoSize: CGSize = CGSize(width: 1280, height: 720)) {
        self.webSocketManager = webSocketManager
        self.videoSize = videoSize
    }
    
    var body: some View {
        let _ = print("🎨 [AIDetectionOverlayView] 渲染UI，当前目标数量: \(detectionTargets.count)")
        
        GeometryReader { geometry in
            ZStack {
                // 透明背景，允许触摸事件穿透
                Color.clear
                
                // 绘制所有检测目标
                ForEach(detectionTargets) { target in
                    let _ = print("🎯 [AIDetectionOverlayView] 渲染目标: ID=\(target.id), 类型=\(target.type.displayName)")
                    AIDetectionBoxView(
                        target: target,
                        containerSize: geometry.size,
                        videoSize: videoSize
                    )
                }
            }
        }
        .onAppear {
            print("👀 [AIDetectionOverlayView] 视图出现，开始设置订阅")
            setupWebSocketSubscription()
        }
        .onDisappear {
            cancellables.removeAll()
        }
    }
    
    private func setupWebSocketSubscription() {
        print("🔗 [AIDetectionOverlayView] 设置WebSocket订阅")
        webSocketManager.messagePublisher
            .receive(on: DispatchQueue.main)
            .sink { message in
                print("📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: \(message.type)")
                if message.type == .aiDetection {
                    print("🎯 [AIDetectionOverlayView] 收到AI检测消息")
                    if let data = message.data {
                        print("📦 [AIDetectionOverlayView] 消息包含数据，大小: \(data.count) 字节")
                        if let aiDetectionMessage = try? JSONDecoder().decode(AIDetectionMessage.self, from: data) {
                            print("✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 \(aiDetectionMessage.targets.count) 个目标")
                            self.detectionTargets = aiDetectionMessage.targets
                            print("🔄 [AIDetectionOverlayView] 已更新检测目标列表")
                        } else {
                            print("❌ [AIDetectionOverlayView] 解码AI检测消息失败")
                        }
                    } else {
                        print("⚠️ [AIDetectionOverlayView] AI检测消息不包含数据")
                    }
                } else {
                    print("ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: \(message.type)")
                }
            }
            .store(in: &cancellables)
        print("✅ [AIDetectionOverlayView] WebSocket订阅设置完成")
    }
    
    private func updateDetectionTargets(_ targets: [AIDetectionTarget]) {
        withAnimation(.easeInOut(duration: 0.1)) {
            detectionTargets = targets
        }
        
        // 自动清除过期的检测结果（3秒后）
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            let currentTime = Date()
            detectionTargets.removeAll { target in
                currentTime.timeIntervalSince(target.timestamp) > 3.0
            }
        }
    }
}

/// 单个AI检测框视图
struct AIDetectionBoxView: View {
    let target: AIDetectionTarget
    let containerSize: CGSize
    let videoSize: CGSize
    
    var body: some View {
        let frame = calculateTargetFrame()
        
        ZStack {
            // 检测框 - 修复位置偏左问题：使用与目标ID相同的position定位方式
            Rectangle()
                .stroke(
                    Color(hex: target.type.color),
                    lineWidth: target.isHighlighted ? 3.0 : 2.0
                )
                .background(
                    Rectangle()
                        .fill(Color(hex: target.type.color).opacity(0.1))
                )
                .frame(
                    width: max(0, frame.width.isFinite ? frame.width : 0),
                    height: max(0, frame.height.isFinite ? frame.height : 0)
                )
                .position(
                    x: frame.midX.isFinite ? frame.midX : 0,
                    y: frame.midY.isFinite ? frame.midY : 0
                )
            
            // 目标ID（居中显示在检测框顶部）
            Text("ID: \(target.id)")
                .font(.system(size: 12, weight: .bold, design: .monospaced))
                .foregroundColor(.white)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color(hex: target.type.color))
                )
                .position(x: frame.midX, y: frame.minY - 10) // 显示在检测框上方
            
            // 目标类型和置信度（左上角）- 只有非未知类型才显示
            if target.type != .unknown {
                VStack(alignment: .leading, spacing: 1) {
                    Text(target.type.displayName)
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white)
                    
                    // 修复置信度显示：如果值大于1则认为是百分比格式(0-100)，否则是小数格式(0-1)
                    Text(String(format: "%.1f%%", target.confidence > 1.0 ? target.confidence : target.confidence * 100))
                        .font(.system(size: 9, weight: .regular, design: .monospaced))
                        .foregroundColor(.white.opacity(0.9))
                }
                .padding(.horizontal, 4)
                .padding(.vertical, 2)
                .background(
                    RoundedRectangle(cornerRadius: 3)
                        .fill(Color.black.opacity(0.7))
                )
                .opacity(target.isHighlighted ? 1.0 : 0.8) // 高亮目标更明显
                .position(x: frame.minX + 30, y: frame.minY + 15) // 左上角位置
            }
        }
        .animation(.easeInOut(duration: 0.2), value: target.isHighlighted)
    }
    
    private func calculateTargetFrame() -> CGRect {
        // 计算视频在容器中的实际显示区域（保持宽高比）
        let videoAspectRatio = videoSize.width / videoSize.height
        let containerAspectRatio = containerSize.width / containerSize.height
        
        let displaySize: CGSize
        let displayOffset: CGPoint
        
        if videoAspectRatio > containerAspectRatio {
            // 视频更宽，以容器宽度为准
            let width = containerSize.width
            let height = containerSize.width / videoAspectRatio
            displaySize = CGSize(width: width, height: height)
            displayOffset = CGPoint(x: 0, y: (containerSize.height - height) / 2)
        } else {
            // 视频更高，以容器高度为准
            let width = containerSize.height * videoAspectRatio
            let height = containerSize.height
            displaySize = CGSize(width: width, height: height)
            displayOffset = CGPoint(x: (containerSize.width - width) / 2, y: 0)
        }
        
        // 将归一化坐标转换为实际像素坐标
        // 修复水平位置偏左问题：确保坐标转换正确
        // P2-R1的坐标系统：(0,0)在左上角，x向右增加，y向下增加
        let x = displayOffset.x + target.x * displaySize.width
        let y = displayOffset.y + target.y * displaySize.height
        let width = target.width * displaySize.width
        let height = target.height * displaySize.height
        
        print("🎯 [坐标转换] 目标ID=\(target.id): 归一化坐标(\(String(format: "%.3f", target.x)), \(String(format: "%.3f", target.y))), 显示区域偏移(\(String(format: "%.1f", displayOffset.x)), \(String(format: "%.1f", displayOffset.y))), 显示尺寸(\(String(format: "%.1f", displaySize.width)), \(String(format: "%.1f", displaySize.height))), 最终坐标(\(String(format: "%.1f", x)), \(String(format: "%.1f", y)))")
        
        return CGRect(x: x, y: y, width: width, height: height)
    }
}



// MARK: - Preview

struct AIDetectionOverlayView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            // 模拟视频背景
            Rectangle()
                .fill(Color.black)
                .aspectRatio(16/9, contentMode: .fit)
            
            // AI检测叠加层
            AIDetectionOverlayView(
                webSocketManager: WebSocketManager.shared,
                videoSize: CGSize(width: 1280, height: 720)
            )
        }
        .frame(width: 400, height: 300)
        .previewLayout(.sizeThatFits)
    }
}
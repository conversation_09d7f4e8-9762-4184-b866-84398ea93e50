//
//  ProductionVideoView.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import UIKit
import WebRTC

// MARK: - 生产环境视频视图

struct ProductionVideoView: View {
    let webRTCStreamerClient: WebRTCStreamerClient
    
    var body: some View {
        ZStack {
            // 黑色背景 - 标准视频背景
            Color.black
                .ignoresSafeArea(.all)
            
            // 视频容器
            ProductionVideoContainer(webRTCStreamerClient: webRTCStreamerClient)
                .ignoresSafeArea(.all)
            
            // 连接状态覆盖层
            if webRTCStreamerClient.remoteVideoTrack == nil {
                VStack {
                    Spacer()

                    VStack(spacing: 16) {
                        Image(systemName: connectionIcon)
                            .font(.system(size: 48))
                            .foregroundColor(.white.opacity(0.6))

                        Text(connectionStatusText)
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.8))

                        if webRTCStreamerClient.connectionState == .connecting ||
                           webRTCStreamerClient.connectionState == .new {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.2)
                        }
                    }
                    .padding()
                    .background(Color.black.opacity(0.7))
                    .cornerRadius(12)

                    Spacer()
                }
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.3), value: webRTCStreamerClient.remoteVideoTrack != nil)
            }
        }
        .onAppear {
            print("[ProductionVideoView] 📺 ProductionVideoView appeared")
        }
    }

    // MARK: - 计算属性

    private var connectionStatusText: String {
        switch webRTCStreamerClient.connectionState {
        case .new:
            return "准备连接视频流..."
        case .connecting:
            return "正在连接视频流..."
        case .connected:
            return webRTCStreamerClient.remoteVideoTrack == nil ? "等待视频数据..." : "已连接"
        case .disconnected:
            return "视频流已断开"
        case .failed:
            return "视频流连接失败"
        case .closed:
            return "视频流连接已关闭"
        @unknown default:
            return "未知连接状态"
        }
    }

    private var connectionIcon: String {
        switch webRTCStreamerClient.connectionState {
        case .new, .connecting:
            return "video"
        case .connected:
            return webRTCStreamerClient.remoteVideoTrack == nil ? "video.badge.ellipsis" : "video.fill"
        case .disconnected, .closed:
            return "video.slash"
        case .failed:
            return "exclamationmark.triangle"
        @unknown default:
            return "questionmark.video"
        }
    }
}

// MARK: - 生产环境视频容器

struct ProductionVideoContainer: UIViewControllerRepresentable {
    let webRTCStreamerClient: WebRTCStreamerClient
    
    func makeUIViewController(context: Context) -> ProductionVideoController {
        print("[ProductionVideoContainer] 📺 Creating ProductionVideoController")
        let controller = ProductionVideoController()
        controller.webRTCStreamerClient = webRTCStreamerClient

        // 立即设置视频轨道（如果存在）
        if let videoTrack = webRTCStreamerClient.remoteVideoTrack {
            print("[ProductionVideoContainer] 📺 Setting initial video track")
            DispatchQueue.main.async {
                controller.updateVideoTrack(videoTrack)
            }
        }

        return controller
    }
    
    func updateUIViewController(_ uiViewController: ProductionVideoController, context: Context) {
        print("[ProductionVideoContainer] 📺 updateUIViewController called")

        // 检查连接状态，如果断开则清理
        if webRTCStreamerClient.connectionState == .disconnected || webRTCStreamerClient.connectionState == .failed {
            print("[ProductionVideoContainer] 📺 Connection lost, cleaning up controller")
            uiViewController.cleanup()
            return
        }

        // 确保视图已加载
        uiViewController.loadViewIfNeeded()

        // 更新视频轨道
        uiViewController.updateVideoTrack(webRTCStreamerClient.remoteVideoTrack)

        // 确保视图布局正确
        DispatchQueue.main.async {
            uiViewController.view.setNeedsLayout()
            uiViewController.view.layoutIfNeeded()
        }
    }
}

// MARK: - 生产环境视频控制器

class ProductionVideoController: UIViewController {
    weak var webRTCStreamerClient: WebRTCStreamerClient?
    private var videoView: RTCMTLVideoView?
    private var currentVideoTrack: RTCVideoTrack?
    private var isCleanedUp = false
    
    override func viewDidLoad() {
        super.viewDidLoad()
        print("[ProductionVideoController] 📺 viewDidLoad")
        setupUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        print("[ProductionVideoController] 📺 viewWillAppear")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        print("[ProductionVideoController] 📺 viewDidAppear - SUCCESS!")
        print("[ProductionVideoController] 📺 View frame: \(view.frame)")
        print("[ProductionVideoController] 📺 View bounds: \(view.bounds)")
        print("[ProductionVideoController] 📺 View window: \(view.window != nil ? "EXISTS" : "NIL")")
        
        // 延迟检查
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.performDetailedCheck()
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 防止在清理后继续执行布局操作
        guard !isCleanedUp else {
            print("[ProductionVideoController] 📺 viewDidLayoutSubviews - SKIPPED (cleaned up)")
            return
        }

        print("[ProductionVideoController] 📺 viewDidLayoutSubviews")
        print("[ProductionVideoController] 📺 View frame: \(view.frame)")
        print("[ProductionVideoController] 📺 View bounds: \(view.bounds)")
    }
    
    private func setupUI() {
        print("[ProductionVideoController] 📺 Setting up UI")
        
        // 设置黑色背景
        view.backgroundColor = UIColor.black
        
        // 创建视频视图
        createVideoView()
        
        print("[ProductionVideoController] 📺 UI setup complete")
    }
    
    private func createVideoView() {
        if self.videoView != nil {
            print("[ProductionVideoController] 📺 Video view already exists, skipping creation")
            return
        }
        
        let videoView = RTCMTLVideoView()
        videoView.backgroundColor = UIColor.black
        videoView.contentMode = .scaleAspectFit
        videoView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(videoView)
        self.videoView = videoView
        
        // 设置约束 - 填满整个视图
        NSLayoutConstraint.activate([
            videoView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            videoView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            videoView.topAnchor.constraint(equalTo: view.topAnchor),
            videoView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        print("[ProductionVideoController] 📺 Video view created and added")
        
        // 强制立即布局
        DispatchQueue.main.async {
            self.view.setNeedsLayout()
            self.view.layoutIfNeeded()
            print("[ProductionVideoController] 📺 Forced layout update")
            print("[ProductionVideoController] 📺 Video view frame after layout: \(videoView.frame)")
            print("[ProductionVideoController] 📺 Video view bounds after layout: \(videoView.bounds)")
        }
    }
    
    func updateVideoTrack(_ videoTrack: RTCVideoTrack?) {
        print("[ProductionVideoController] 📺 updateVideoTrack called")
        print("[ProductionVideoController] 📺 New video track: \(videoTrack != nil ? "EXISTS" : "NIL")")

        // 防止在清理后继续操作
        guard !isCleanedUp else {
            print("[ProductionVideoController] 📺 updateVideoTrack - SKIPPED (cleaned up)")
            return
        }

        // 确保视图已创建
        loadViewIfNeeded()
        if videoView == nil {
            createVideoView()
        }

        // 移除旧的视频轨道
        if let oldTrack = currentVideoTrack, let videoView = self.videoView {
            oldTrack.remove(videoView)
            print("[ProductionVideoController] 📺 Removed old video track")
        }

        // 更新当前轨道引用
        currentVideoTrack = videoTrack

        // 添加新的视频轨道
        if let newTrack = videoTrack, let videoView = self.videoView {
            newTrack.add(videoView)
            print("[ProductionVideoController] 📺 Added new video track")

            // 强制布局更新
            DispatchQueue.main.async { [weak self] in
                guard let self = self, !self.isCleanedUp else {
                    print("[ProductionVideoController] 📺 Layout update - SKIPPED (cleaned up)")
                    return
                }
                self.view.setNeedsLayout()
                self.view.layoutIfNeeded()
                print("[ProductionVideoController] 📺 Forced layout update after video track added")

                // 执行详细检查
                self.performDetailedCheck()
            }
        } else {
            print("[ProductionVideoController] 📺 No video track to add")
        }
    }
    
    private func performDetailedCheck() {
        guard !isCleanedUp else {
            print("[ProductionVideoController] 📺 performDetailedCheck - SKIPPED (cleaned up)")
            return
        }

        print("[ProductionVideoController] 📺 🔍 Performing detailed check:")
        print("[ProductionVideoController] 📺 - Main view frame: \(view.frame)")
        print("[ProductionVideoController] 📺 - Main view bounds: \(view.bounds)")
        print("[ProductionVideoController] 📺 - Main view window: \(view.window != nil ? "EXISTS" : "NIL")")
        print("[ProductionVideoController] 📺 - Main view backgroundColor: \(view.backgroundColor?.description ?? "NIL")")
        print("[ProductionVideoController] 📺 - Main view isHidden: \(view.isHidden)")
        print("[ProductionVideoController] 📺 - Main view alpha: \(view.alpha)")

        if let videoView = self.videoView {
            print("[ProductionVideoController] 📺 - Video view frame: \(videoView.frame)")
            print("[ProductionVideoController] 📺 - Video view bounds: \(videoView.bounds)")
            print("[ProductionVideoController] 📺 - Video view window: \(videoView.window != nil ? "EXISTS" : "NIL")")
            print("[ProductionVideoController] 📺 - Video view superview: \(videoView.superview != nil ? "EXISTS" : "NIL")")
            print("[ProductionVideoController] 📺 - Video view backgroundColor: \(videoView.backgroundColor?.description ?? "NIL")")
            print("[ProductionVideoController] 📺 - Video view isHidden: \(videoView.isHidden)")
            print("[ProductionVideoController] 📺 - Video view alpha: \(videoView.alpha)")
            print("[ProductionVideoController] 📺 - Current video track: \(currentVideoTrack != nil ? "EXISTS" : "NIL")")
        }
    }

    // MARK: - 清理方法

    func cleanup() {
        print("[ProductionVideoController] 📺 🧹 Starting cleanup")

        // 防止重复清理
        guard !isCleanedUp else {
            print("[ProductionVideoController] 📺 Already cleaned up, skipping")
            return
        }

        isCleanedUp = true

        // 安全地移除视频轨道
        if let track = currentVideoTrack, let videoView = self.videoView {
            // 在主线程上安全移除
            DispatchQueue.main.async {
                track.remove(videoView)
                print("[ProductionVideoController] 📺 Removed video track during cleanup")
            }
        }

        // 清理引用
        currentVideoTrack = nil
        webRTCStreamerClient = nil

        // 移除视频视图
        videoView?.removeFromSuperview()
        videoView = nil

        print("[ProductionVideoController] 📺 ✅ Cleanup completed")
    }

    deinit {
        print("[ProductionVideoController] 📺 💀 deinit called")

        // 在deinit中进行同步清理，避免异步操作
        if !isCleanedUp {
            isCleanedUp = true

            // 同步移除视频轨道
            if let track = currentVideoTrack, let videoView = self.videoView {
                track.remove(videoView)
                print("[ProductionVideoController] 📺 💀 Removed video track in deinit")
            }

            // 清理引用
            currentVideoTrack = nil
            webRTCStreamerClient = nil
            videoView?.removeFromSuperview()
            videoView = nil
        }

        print("[ProductionVideoController] 📺 💀 deinit completed")
    }
}

// MARK: - Preview

struct ProductionVideoView_Previews: PreviewProvider {
    static var previews: some View {
        ProductionVideoView(webRTCStreamerClient: WebRTCStreamerClient())
    }
}

//
//  DirectVideoView.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import UIKit
import WebRTC

// MARK: - 直接使用 UIKit 的视频显示方案

struct DirectVideoView: UIViewControllerRepresentable {
    @EnvironmentObject var webRTCStreamerClient: WebRTCStreamerClient
    
    func makeUIViewController(context: Context) -> DirectVideoViewController {
        print("[DirectVideoView] Creating DirectVideoViewController")
        let controller = DirectVideoViewController()
        controller.webRTCStreamerClient = webRTCStreamerClient
        return controller
    }
    
    func updateUIViewController(_ uiViewController: DirectVideoViewController, context: Context) {
        print("[DirectVideoView] updateUIViewController called")
        uiViewController.updateVideoTrack(webRTCStreamerClient.remoteVideoTrack)
    }
}

// MARK: - 直接的 UIKit 视频控制器

class DirectVideoViewController: UIViewController {
    var webRTCStreamerClient: WebRTCStreamerClient?
    private var videoView: RTCMTLVideoView?
    private var currentVideoTrack: RTCVideoTrack?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        print("[DirectVideoViewController] viewDidLoad")
        setupUI()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        print("[DirectVideoViewController] viewDidAppear")
        print("[DirectVideoViewController] View frame: \(view.frame)")
        print("[DirectVideoViewController] View bounds: \(view.bounds)")
        print("[DirectVideoViewController] View window: \(view.window != nil ? "exists" : "nil")")
        
        // 延迟检查
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.checkViewHierarchy()
        }
    }
    
    private func setupUI() {
        print("[DirectVideoViewController] Setting up UI")
        
        // 设置背景色为明显的颜色
        view.backgroundColor = UIColor.systemPink
        
        // 添加标识标签
        let label = UILabel()
        label.text = "🎥 DIRECT VIDEO VIEW 🎥"
        label.textColor = .white
        label.font = UIFont.boldSystemFont(ofSize: 24)
        label.textAlignment = .center
        label.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        label.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(label)
        
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            label.leadingAnchor.constraint(greaterThanOrEqualTo: view.leadingAnchor, constant: 20),
            label.trailingAnchor.constraint(lessThanOrEqualTo: view.trailingAnchor, constant: -20)
        ])
        
        // 创建视频视图
        createVideoView()
        
        print("[DirectVideoViewController] UI setup complete")
    }
    
    private func createVideoView() {
        print("[DirectVideoViewController] Creating video view")
        
        let videoView = RTCMTLVideoView()
        videoView.backgroundColor = UIColor.systemBlue
        videoView.contentMode = .scaleAspectFit
        videoView.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加明显的边框
        videoView.layer.borderColor = UIColor.systemRed.cgColor
        videoView.layer.borderWidth = 3.0
        
        view.addSubview(videoView)
        
        // 设置约束
        NSLayoutConstraint.activate([
            videoView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            videoView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            videoView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 80),
            videoView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20)
        ])
        
        self.videoView = videoView
        
        print("[DirectVideoViewController] Video view created and added to hierarchy")
        print("[DirectVideoViewController] Video view frame: \(videoView.frame)")
        print("[DirectVideoViewController] Video view superview: \(videoView.superview != nil ? "exists" : "nil")")
    }
    
    func updateVideoTrack(_ videoTrack: RTCVideoTrack?) {
        print("[DirectVideoViewController] updateVideoTrack called")
        print("[DirectVideoViewController] New video track: \(videoTrack != nil ? "exists" : "nil")")
        
        // 移除旧的视频轨道
        if let oldTrack = currentVideoTrack, let videoView = self.videoView {
            oldTrack.remove(videoView)
            print("[DirectVideoViewController] Removed old video track")
        }
        
        // 添加新的视频轨道
        if let newTrack = videoTrack, let videoView = self.videoView {
            newTrack.add(videoView)
            currentVideoTrack = newTrack
            print("[DirectVideoViewController] Added new video track")
            
            // 强制布局更新
            DispatchQueue.main.async {
                self.view.setNeedsLayout()
                self.view.layoutIfNeeded()
                print("[DirectVideoViewController] Forced layout update")
                
                // 检查视图状态
                self.checkVideoViewState()
            }
        } else {
            currentVideoTrack = nil
            print("[DirectVideoViewController] No video track to add")
        }
    }
    
    private func checkViewHierarchy() {
        print("[DirectVideoViewController] 🔍 Checking view hierarchy:")
        print("[DirectVideoViewController] - Main view frame: \(view.frame)")
        print("[DirectVideoViewController] - Main view bounds: \(view.bounds)")
        print("[DirectVideoViewController] - Main view window: \(view.window != nil ? "exists" : "nil")")
        print("[DirectVideoViewController] - Main view backgroundColor: \(view.backgroundColor?.description ?? "nil")")
        
        if let videoView = self.videoView {
            print("[DirectVideoViewController] - Video view frame: \(videoView.frame)")
            print("[DirectVideoViewController] - Video view bounds: \(videoView.bounds)")
            print("[DirectVideoViewController] - Video view window: \(videoView.window != nil ? "exists" : "nil")")
            print("[DirectVideoViewController] - Video view superview: \(videoView.superview != nil ? "exists" : "nil")")
            print("[DirectVideoViewController] - Video view backgroundColor: \(videoView.backgroundColor?.description ?? "nil")")
            print("[DirectVideoViewController] - Video view isHidden: \(videoView.isHidden)")
            print("[DirectVideoViewController] - Video view alpha: \(videoView.alpha)")
        }
    }
    
    private func checkVideoViewState() {
        guard let videoView = self.videoView else { return }
        
        print("[DirectVideoViewController] 🎥 Video view state:")
        print("[DirectVideoViewController] - frame: \(videoView.frame)")
        print("[DirectVideoViewController] - bounds: \(videoView.bounds)")
        print("[DirectVideoViewController] - window: \(videoView.window != nil ? "exists" : "nil")")
        print("[DirectVideoViewController] - superview: \(videoView.superview != nil ? "exists" : "nil")")
        print("[DirectVideoViewController] - backgroundColor: \(videoView.backgroundColor?.description ?? "nil")")
        print("[DirectVideoViewController] - isHidden: \(videoView.isHidden)")
        print("[DirectVideoViewController] - alpha: \(videoView.alpha)")
        print("[DirectVideoViewController] - layer.isHidden: \(videoView.layer.isHidden)")
        print("[DirectVideoViewController] - layer.opacity: \(videoView.layer.opacity)")
        print("[DirectVideoViewController] - Current video track: \(currentVideoTrack != nil ? "exists" : "nil")")
    }
}

// MARK: - Preview

struct DirectVideoView_Previews: PreviewProvider {
    static var previews: some View {
        DirectVideoView()
            .environmentObject(WebRTCStreamerClient())
    }
}

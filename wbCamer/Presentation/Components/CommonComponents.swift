//
//  CommonComponents.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import Combine

// MARK: - Loading Components

struct LoadingView: View {
    let message: String
    @State private var isAnimating = false
    
    init(message: String = "Loading...") {
        self.message = message
    }
    
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .progressViewStyle(CircularProgressViewStyle(tint: .accentColor))
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
        )
    }
}

struct LoadingOverlay: View {
    let message: String
    
    init(message: String = "Loading...") {
        self.message = message
    }
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            LoadingView(message: message)
        }
    }
}

// MARK: - Error Components

struct ErrorView: View {
    let error: Error
    let retryAction: (() -> Void)?
    
    init(error: Error, retryAction: (() -> Void)? = nil) {
        self.error = error
        self.retryAction = retryAction
    }
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            Text("Something went wrong")
                .font(.headline)
                .multilineTextAlignment(.center)
            
            Text(errorMessage)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if let retryAction = retryAction {
                Button("Try Again") {
                    retryAction()
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding(24)
    }
    
    private var errorMessage: String {
        if let apiError = error as? APIError {
            switch apiError {
            case .networkError:
                return "Please check your internet connection and try again."
            case .noData:
                return "Received invalid response from server."
            case .decodingError:
                return "Failed to process server response."
            case .serverError(let code, let message):
                return "Server error (\(code)): \(message ?? "Unknown error")"
            case .unauthorized:
                return "Authentication failed. Please check your credentials."
            case .forbidden:
                return "Access denied. You don't have permission to perform this action."
            case .notFound:
                return "The requested resource was not found."
            case .timeout:
                return "Request timed out. Please try again."
            case .invalidURL:
                return "Invalid URL provided."
            case .sslError:
                return "SSL certificate error."
            }
        }
        return error.localizedDescription
    }
}

// MARK: - Empty State Components

struct EmptyStateView: View {
    let title: String
    let message: String
    let systemImage: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    init(
        title: String,
        message: String,
        systemImage: String,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.title = title
        self.message = message
        self.systemImage = systemImage
        self.actionTitle = actionTitle
        self.action = action
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: systemImage)
                .font(.system(size: 64))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            if let actionTitle = actionTitle, let action = action {
                Button(actionTitle, action: action)
                    .buttonStyle(.borderedProminent)
            }
        }
        .padding(40)
    }
}

struct EmptyGalleryView: View {
    var body: some View {
        EmptyStateView(
            title: "No Files",
            message: "Connect to your camera and start recording to see files here.",
            systemImage: "photo.on.rectangle",
            actionTitle: "Connect Camera",
            action: {
                // Navigate to camera connection
            }
        )
    }
}

// MARK: - Button Styles

struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .foregroundColor(.white)
            .padding(.vertical, 12)
            .padding(.horizontal, 24)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.blue)
                    .opacity(configuration.isPressed ? 0.8 : 1.0)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
            .id("primary-button-\(configuration.isPressed ? "pressed" : "normal")")
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .foregroundColor(.blue)
            .padding(.vertical, 12)
            .padding(.horizontal, 24)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(.blue, lineWidth: 1)
                    .opacity(configuration.isPressed ? 0.8 : 1.0)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
            .id("secondary-button-\(configuration.isPressed ? "pressed" : "normal")")
    }
}

struct DestructiveButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .foregroundColor(.white)
            .padding(.vertical, 12)
            .padding(.horizontal, 24)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.red)
                    .opacity(configuration.isPressed ? 0.8 : 1.0)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
            .id("destructive-button-\(configuration.isPressed ? "pressed" : "normal")")
    }
}

// MARK: - Input Components

struct FormTextField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let isSecure: Bool
    let keyboardType: UIKeyboardType
    let validation: ((String) -> String?)?
    
    @State private var isEditing = false
    @State private var validationMessage: String?
    
    init(
        title: String,
        text: Binding<String>,
        placeholder: String = "",
        isSecure: Bool = false,
        keyboardType: UIKeyboardType = .default,
        validation: ((String) -> String?)? = nil
    ) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.isSecure = isSecure
        self.keyboardType = keyboardType
        self.validation = validation
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                        .onSubmit {
                            validateInput()
                        }
                } else {
                    TextField(placeholder, text: $text)
                        .keyboardType(keyboardType)
                        .onSubmit {
                            validateInput()
                        }
                }
            }
            .textFieldStyle(.roundedBorder)
            .onChange(of: text) { _, _ in
                if !isEditing {
                    validateInput()
                }
            }
            
            if let validationMessage = validationMessage {
                Text(validationMessage)
                    .font(.caption)
                    .foregroundColor(.red)
            }
        }
    }
    
    private func validateInput() {
        validationMessage = validation?(text)
    }
}

struct FormToggle: View {
    let title: String
    let description: String?
    @Binding var isOn: Bool
    
    init(title: String, description: String? = nil, isOn: Binding<Bool>) {
        self.title = title
        self.description = description
        self._isOn = isOn
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    if let description = description {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Toggle("", isOn: $isOn)
                    .labelsHidden()
            }
        }
    }
}

struct FormPicker<T: Hashable & CustomStringConvertible>: View {
    let title: String
    let options: [T]
    @Binding var selection: T
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Picker(title, selection: $selection) {
                ForEach(options, id: \.self) { option in
                    Text(option.description)
                        .tag(option)
                }
            }
            .pickerStyle(.segmented)
        }
    }
}

// MARK: - Card Components

struct CardView<Content: View>: View {
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        content
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.regularMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
    }
}

struct InfoCard: View {
    let title: String
    let value: String
    let systemImage: String?
    let color: Color
    
    init(title: String, value: String, systemImage: String? = nil, color: Color = .accentColor) {
        self.title = title
        self.value = value
        self.systemImage = systemImage
        self.color = color
    }
    
    var body: some View {
        CardView {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(value)
                        .font(.headline)
                        .foregroundColor(.primary)
                }
                
                Spacer()
                
                if let systemImage = systemImage {
                    Image(systemName: systemImage)
                        .font(.title2)
                        .foregroundColor(color)
                }
            }
        }
    }
}

// MARK: - Progress Components

struct ProgressCard: View {
    let title: String
    let progress: Double
    let subtitle: String?
    
    init(title: String, progress: Double, subtitle: String? = nil) {
        self.title = title
        self.progress = progress
        self.subtitle = subtitle
    }
    
    var body: some View {
        CardView {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text(title)
                        .font(.headline)
                    
                    Spacer()
                    
                    Text("\(Int(progress * 100))%")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .accentColor))
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}

// MARK: - Alert Components

struct AlertBanner: View {
    let message: String
    let type: AlertType
    let action: (() -> Void)?
    
    enum AlertType {
        case info, warning, error, success
        
        var color: Color {
            switch self {
            case .info: return .blue
            case .warning: return .orange
            case .error: return .red
            case .success: return .green
            }
        }
        
        var icon: String {
            switch self {
            case .info: return "info.circle"
            case .warning: return "exclamationmark.triangle"
            case .error: return "xmark.circle"
            case .success: return "checkmark.circle"
            }
        }
    }
    
    init(message: String, type: AlertType, action: (() -> Void)? = nil) {
        self.message = message
        self.type = type
        self.action = action
    }
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: type.icon)
                .foregroundColor(type.color)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
            
            Spacer()
            
            if let action = action {
                Button("Dismiss") {
                    action()
                }
                .font(.caption)
                .foregroundColor(type.color)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(type.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(type.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Navigation Components

struct BackButton: View {
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                Text("Back")
                    .font(.body)
            }
            .foregroundColor(.accentColor)
        }
    }
}

// MARK: - Utility Extensions

extension View {
    func cardStyle() -> some View {
        self.modifier(CardModifier())
    }
    
    func alertBanner(
        message: String,
        type: AlertBanner.AlertType,
        isPresented: Binding<Bool>
    ) -> some View {
        self.overlay(
            VStack {
                if isPresented.wrappedValue {
                    AlertBanner(message: message, type: type) {
                        isPresented.wrappedValue = false
                    }
                    .padding(.horizontal)
                    .transition(.move(edge: .top).combined(with: .opacity))
                }
                Spacer()
            }
            .animation(.easeInOut(duration: 0.3), value: isPresented.wrappedValue)
            .id("alert-banner-\(isPresented.wrappedValue ? "visible" : "hidden")")
        )
    }
}

struct CardModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.regularMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
    }
}

// MARK: - Preview

#Preview {
    ScrollView {
        VStack(spacing: 16) {
            LoadingView()
            
            ErrorView(error: APIError.networkError(NSError(domain: "Preview", code: -1, userInfo: [NSLocalizedDescriptionKey: "Network error"]))) {
                print("Retry tapped")
            }
            
            EmptyStateView(
                title: "No Data",
                message: "There's nothing to show here yet.",
                systemImage: "tray",
                actionTitle: "Refresh",
                action: { print("Refresh tapped") }
            )
            
            InfoCard(
                title: "Status",
                value: "Connected",
                systemImage: "wifi",
                color: .green
            )
            
            ProgressCard(
                title: "Download Progress",
                progress: 0.65,
                subtitle: "2.1 GB of 3.2 GB"
            )
            
            AlertBanner(
                message: "Camera connection established successfully.",
                type: .success
            )
        }
        .padding()
    }
}
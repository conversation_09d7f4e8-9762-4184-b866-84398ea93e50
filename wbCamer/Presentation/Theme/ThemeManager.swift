//
//  ThemeManager.swift
//  wbCamer
//
//  Created by AI Assistant on 2024.
//

import SwiftUI
import UIKit

// MARK: - Theme Manager

class ThemeManager: ObservableObject {
    static let shared = ThemeManager()
    
    @Published var currentTheme: AppTheme = .system
    @Published var accentColor: AccentColor = .blue
    @Published var fontSize: FontSize = .medium
    
    private init() {
        loadThemeSettings()
    }
    
    func setTheme(_ theme: AppTheme) {
        currentTheme = theme
        applyTheme()
        saveThemeSettings()
    }
    
    func setAccentColor(_ color: AccentColor) {
        accentColor = color
        applyTheme()
        saveThemeSettings()
    }
    
    func setFontSize(_ size: FontSize) {
        fontSize = size
        saveThemeSettings()
    }
    
    private func applyTheme() {
        DispatchQueue.main.async {
            // Apply theme to UIKit components
            self.configureUIKitAppearance()
            
            // Notify SwiftUI views to update
            self.objectWillChange.send()
        }
    }
    
    private func configureUIKitAppearance() {
        // Configure Navigation Bar
        let navBarAppearance = UINavigationBarAppearance()
        navBarAppearance.configureWithOpaqueBackground()
        navBarAppearance.backgroundColor = UIColor.systemBackground
        navBarAppearance.titleTextAttributes = [.foregroundColor: UIColor.label]
        navBarAppearance.largeTitleTextAttributes = [.foregroundColor: UIColor.label]
        
        UINavigationBar.appearance().standardAppearance = navBarAppearance
        UINavigationBar.appearance().scrollEdgeAppearance = navBarAppearance
        UINavigationBar.appearance().compactAppearance = navBarAppearance
        
        // Configure Tab Bar
        let tabBarAppearance = UITabBarAppearance()
        tabBarAppearance.configureWithOpaqueBackground()
        tabBarAppearance.backgroundColor = UIColor.systemBackground
        
        UITabBar.appearance().standardAppearance = tabBarAppearance
        UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
        
        // Configure accent color
        UIView.appearance().tintColor = accentColor.uiColor
    }
    
    private func loadThemeSettings() {
        if let themeRawValue = UserDefaults.standard.object(forKey: "app_theme") as? String,
           let theme = AppTheme(rawValue: themeRawValue) {
            currentTheme = theme
        }
        
        if let colorRawValue = UserDefaults.standard.object(forKey: "accent_color") as? String,
           let color = AccentColor(rawValue: colorRawValue) {
            accentColor = color
        }
        
        if let sizeRawValue = UserDefaults.standard.object(forKey: "font_size") as? String,
           let size = FontSize(rawValue: sizeRawValue) {
            fontSize = size
        }
    }
    
    private func saveThemeSettings() {
        UserDefaults.standard.set(currentTheme.rawValue, forKey: "app_theme")
        UserDefaults.standard.set(accentColor.rawValue, forKey: "accent_color")
        UserDefaults.standard.set(fontSize.rawValue, forKey: "font_size")
    }
}

// MARK: - App Theme

enum AppTheme: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .light:
            return "Light"
        case .dark:
            return "Dark"
        case .system:
            return "System"
        }
    }
    
    var colorScheme: ColorScheme? {
        switch self {
        case .light:
            return .light
        case .dark:
            return .dark
        case .system:
            return nil
        }
    }
}

// MARK: - Accent Color

enum AccentColor: String, CaseIterable {
    case blue = "blue"
    case green = "green"
    case orange = "orange"
    case red = "red"
    case purple = "purple"
    case pink = "pink"
    case teal = "teal"
    case indigo = "indigo"
    
    var displayName: String {
        switch self {
        case .blue: return "Blue"
        case .green: return "Green"
        case .orange: return "Orange"
        case .red: return "Red"
        case .purple: return "Purple"
        case .pink: return "Pink"
        case .teal: return "Teal"
        case .indigo: return "Indigo"
        }
    }
    
    var color: Color {
        switch self {
        case .blue: return .blue
        case .green: return .green
        case .orange: return .orange
        case .red: return .red
        case .purple: return .purple
        case .pink: return .pink
        case .teal: return .teal
        case .indigo: return .indigo
        }
    }
    
    var uiColor: UIColor {
        switch self {
        case .blue: return .systemBlue
        case .green: return .systemGreen
        case .orange: return .systemOrange
        case .red: return .systemRed
        case .purple: return .systemPurple
        case .pink: return .systemPink
        case .teal: return .systemTeal
        case .indigo: return .systemIndigo
        }
    }
}

// MARK: - Font Size

enum FontSize: String, CaseIterable {
    case small = "small"
    case medium = "medium"
    case large = "large"
    case extraLarge = "extraLarge"
    
    var displayName: String {
        switch self {
        case .small: return "Small"
        case .medium: return "Medium"
        case .large: return "Large"
        case .extraLarge: return "Extra Large"
        }
    }
    
    var scaleFactor: CGFloat {
        switch self {
        case .small: return 0.9
        case .medium: return 1.0
        case .large: return 1.1
        case .extraLarge: return 1.2
        }
    }
}

// MARK: - App Colors

struct AppColors {
    // Primary Colors
    static let primary = Color.primary
    static let secondary = Color.secondary
    static let tertiary = Color(UIColor.tertiaryLabel)
    
    // Background Colors
    static let background = Color(UIColor.systemBackground)
    static let secondaryBackground = Color(UIColor.secondarySystemBackground)
    static let tertiaryBackground = Color(UIColor.tertiarySystemBackground)
    
    // Grouped Background Colors
    static let groupedBackground = Color(UIColor.systemGroupedBackground)
    static let secondaryGroupedBackground = Color(UIColor.secondarySystemGroupedBackground)
    static let tertiaryGroupedBackground = Color(UIColor.tertiarySystemGroupedBackground)
    
    // Semantic Colors
    static let success = Color.green
    static let warning = Color.orange
    static let error = Color.red
    static let info = Color.blue
    
    // Camera Specific Colors
    static let cameraConnected = Color.green
    static let cameraDisconnected = Color.red
    static let cameraConnecting = Color.orange
    
    // Recording Colors
    static let recording = Color.red
    static let recordingBackground = Color.red.opacity(0.1)
    
    // Stream Quality Colors
    static let highQuality = Color.green
    static let mediumQuality = Color.orange
    static let lowQuality = Color.red
    
    // Network Status Colors
    static let networkGood = Color.green
    static let networkPoor = Color.orange
    static let networkOffline = Color.red
}

// MARK: - App Fonts

struct AppFonts {
    private static let themeManager = ThemeManager.shared
    
    // Title Fonts
    static var largeTitle: Font {
        .largeTitle.weight(.bold).scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var title: Font {
        .title.weight(.semibold).scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var title2: Font {
        .title2.weight(.semibold).scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var title3: Font {
        .title3.weight(.medium).scaled(themeManager.fontSize.scaleFactor)
    }
    
    // Body Fonts
    static var headline: Font {
        .headline.scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var body: Font {
        .body.scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var callout: Font {
        .callout.scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var subheadline: Font {
        .subheadline.scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var footnote: Font {
        .footnote.scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var caption: Font {
        .caption.scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var caption2: Font {
        .caption2.scaled(themeManager.fontSize.scaleFactor)
    }
    
    // Monospace Fonts
    static var monospace: Font {
        .system(.body, design: .monospaced).scaled(themeManager.fontSize.scaleFactor)
    }
    
    static var monospacedDigit: Font {
        .monospacedDigit(.body)().scaled(themeManager.fontSize.scaleFactor)
    }
}

// MARK: - App Spacing

struct AppSpacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 16
    static let lg: CGFloat = 24
    static let xl: CGFloat = 32
    static let xxl: CGFloat = 48
    
    // Component Specific Spacing
    static let cardPadding: CGFloat = 16
    static let sectionSpacing: CGFloat = 24
    static let itemSpacing: CGFloat = 12
    static let buttonPadding: CGFloat = 16
}

// MARK: - App Corner Radius

struct AppCornerRadius {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 20
    
    // Component Specific Radius
    static let button: CGFloat = 8
    static let card: CGFloat = 12
    static let sheet: CGFloat = 16
    static let image: CGFloat = 8
}

// MARK: - App Shadows

struct AppShadows {
    static let small = Shadow(
        color: .black.opacity(0.1),
        radius: 2,
        x: 0,
        y: 1
    )
    
    static let medium = Shadow(
        color: .black.opacity(0.15),
        radius: 4,
        x: 0,
        y: 2
    )
    
    static let large = Shadow(
        color: .black.opacity(0.2),
        radius: 8,
        x: 0,
        y: 4
    )
    
    struct Shadow {
        let color: Color
        let radius: CGFloat
        let x: CGFloat
        let y: CGFloat
    }
}

// MARK: - View Extensions

extension View {
    func themedBackground() -> some View {
        self.background(AppColors.background)
    }
    
    func themedSecondaryBackground() -> some View {
        self.background(AppColors.secondaryBackground)
    }
    
    func themedGroupedBackground() -> some View {
        self.background(AppColors.groupedBackground)
    }
    
    func appShadow(_ shadow: AppShadows.Shadow = AppShadows.small) -> some View {
        self.shadow(
            color: shadow.color,
            radius: shadow.radius,
            x: shadow.x,
            y: shadow.y
        )
    }
    
    func appCornerRadius(_ radius: CGFloat = AppCornerRadius.md) -> some View {
        self.clipShape(RoundedRectangle(cornerRadius: radius))
    }
    
    func appPadding(_ padding: CGFloat = AppSpacing.md) -> some View {
        self.padding(padding)
    }
    
    func preferredColorScheme(from theme: AppTheme) -> some View {
        self.preferredColorScheme(theme.colorScheme)
    }
    
    func accentColor(from color: AccentColor) -> some View {
        self.accentColor(color.color)
    }
}

extension Font {
    func scaled(_ factor: CGFloat) -> Font {
        // This is a simplified implementation
        // In a real app, you might want to use a more sophisticated scaling system
        return self
    }
}

// MARK: - Theme Environment

struct ThemeEnvironmentKey: EnvironmentKey {
    static let defaultValue = ThemeManager.shared
}

extension EnvironmentValues {
    var themeManager: ThemeManager {
        get { self[ThemeEnvironmentKey.self] }
        set { self[ThemeEnvironmentKey.self] = newValue }
    }
}

// MARK: - Theme Modifier

struct ThemeModifier: ViewModifier {
    @StateObject private var themeManager = ThemeManager.shared
    
    func body(content: Content) -> some View {
        content
            .environment(\.themeManager, themeManager)
            .preferredColorScheme(from: themeManager.currentTheme)
            .accentColor(from: themeManager.accentColor)
            .onAppear {
                // Apply theme when view appears
                themeManager.setTheme(themeManager.currentTheme)
            }
    }
}

extension View {
    func withAppTheme() -> some View {
        self.modifier(ThemeModifier())
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: AppSpacing.lg) {
        Text("Large Title")
            .font(AppFonts.largeTitle)
            .foregroundColor(AppColors.primary)
        
        Text("Headline Text")
            .font(AppFonts.headline)
            .foregroundColor(AppColors.secondary)
        
        Text("Body Text")
            .font(AppFonts.body)
            .foregroundColor(AppColors.tertiary)
        
        HStack(spacing: AppSpacing.md) {
            ForEach(AccentColor.allCases, id: \.self) { color in
                Circle()
                    .fill(color.color)
                    .frame(width: 30, height: 30)
            }
        }
        
        VStack(spacing: AppSpacing.sm) {
            Rectangle()
                .fill(AppColors.success)
                .frame(height: 40)
                .appCornerRadius(AppCornerRadius.sm)
            
            Rectangle()
                .fill(AppColors.warning)
                .frame(height: 40)
                .appCornerRadius(AppCornerRadius.md)
            
            Rectangle()
                .fill(AppColors.error)
                .frame(height: 40)
                .appCornerRadius(AppCornerRadius.lg)
        }
    }
    .appPadding()
    .themedBackground()
    .withAppTheme()
}
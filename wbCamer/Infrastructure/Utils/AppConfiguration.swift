//
//  AppConfiguration.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import Foundation
import Combine

// MARK: - App Configuration Manager

/// 应用配置管理器
class AppConfiguration: ObservableObject {
    static let shared = AppConfiguration()
    
    // MARK: - Published Properties
    
    @Published var networkConfiguration: NetworkConfiguration
    @Published var appSettings: AppConfigurationSettings
    @Published var debugSettings: DebugSettings
    
    // MARK: - Private Properties
    
    private let userDefaults = UserDefaults.standard
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - UserDefaults Keys
    
    private enum Keys {
        static let networkConfiguration = "networkConfiguration"
        static let appSettings = "appSettings"
        static let debugSettings = "debugSettings"
        static let firstLaunch = "firstLaunch"
    }
    
    // MARK: - Initialization
    
    private init() {
        // 加载配置
        self.networkConfiguration = Self.loadNetworkConfiguration()
        self.appSettings = Self.loadAppSettings()
        self.debugSettings = Self.loadDebugSettings()
        
        // 监听配置变化并自动保存
        setupAutoSave()
        
        // 首次启动检查
        checkFirstLaunch()
    }
    
    // MARK: - Public Methods
    
    /// 重置所有配置到默认值
    func resetToDefaults() {
        networkConfiguration = NetworkConfiguration()
        appSettings = AppConfigurationSettings()
        debugSettings = DebugSettings()
        
        // 清除UserDefaults中的配置
        userDefaults.removeObject(forKey: Keys.networkConfiguration)
        userDefaults.removeObject(forKey: Keys.appSettings)
        userDefaults.removeObject(forKey: Keys.debugSettings)
    }
    
    /// 导出配置
    func exportConfiguration() -> Data? {
        let config = ExportableConfiguration(
            networkConfiguration: networkConfiguration,
            appSettings: appSettings
        )
        
        return try? JSONEncoder().encode(config)
    }
    
    /// 导入配置
    func importConfiguration(from data: Data) throws {
        let config = try JSONDecoder().decode(ExportableConfiguration.self, from: data)
        
        networkConfiguration = config.networkConfiguration
        appSettings = config.appSettings
    }
    
    /// 检查是否为首次启动
    var isFirstLaunch: Bool {
        return !userDefaults.bool(forKey: Keys.firstLaunch)
    }
    
    /// 标记首次启动完成
    func markFirstLaunchCompleted() {
        userDefaults.set(true, forKey: Keys.firstLaunch)
    }
    
    // MARK: - Private Methods
    
    private func setupAutoSave() {
        // 监听网络配置变化
        $networkConfiguration
            .dropFirst()
            .sink { [weak self] config in
                self?.saveNetworkConfiguration(config)
            }
            .store(in: &cancellables)
        
        // 监听应用设置变化
        $appSettings
            .dropFirst()
            .sink { [weak self] settings in
                self?.saveAppSettings(settings)
            }
            .store(in: &cancellables)
        
        // 监听调试设置变化
        $debugSettings
            .dropFirst()
            .sink { [weak self] settings in
                self?.saveDebugSettings(settings)
            }
            .store(in: &cancellables)
    }
    
    private func checkFirstLaunch() {
        if isFirstLaunch {
            // 首次启动时的初始化逻辑
            print("First launch detected, initializing default settings...")
        }
    }
    
    // MARK: - Configuration Loading
    
    private static func loadNetworkConfiguration() -> NetworkConfiguration {
        guard let data = UserDefaults.standard.data(forKey: Keys.networkConfiguration),
              let config = try? JSONDecoder().decode(NetworkConfiguration.self, from: data) else {
            return NetworkConfiguration()
        }
        return config
    }
    
    private static func loadAppSettings() -> AppConfigurationSettings {
        guard let data = UserDefaults.standard.data(forKey: Keys.appSettings),
              let settings = try? JSONDecoder().decode(AppConfigurationSettings.self, from: data) else {
            return AppConfigurationSettings()
        }
        return settings
    }
    
    private static func loadDebugSettings() -> DebugSettings {
        guard let data = UserDefaults.standard.data(forKey: Keys.debugSettings),
              let settings = try? JSONDecoder().decode(DebugSettings.self, from: data) else {
            return DebugSettings()
        }
        return settings
    }
    
    // MARK: - Configuration Saving
    
    private func saveNetworkConfiguration(_ config: NetworkConfiguration) {
        if let data = try? JSONEncoder().encode(config) {
            userDefaults.set(data, forKey: Keys.networkConfiguration)
        }
    }
    
    private func saveAppSettings(_ settings: AppConfigurationSettings) {
        if let data = try? JSONEncoder().encode(settings) {
            userDefaults.set(data, forKey: Keys.appSettings)
        }
    }
    
    private func saveDebugSettings(_ settings: DebugSettings) {
        if let data = try? JSONEncoder().encode(settings) {
            userDefaults.set(data, forKey: Keys.debugSettings)
        }
    }
}

// MARK: - App Configuration Settings

/// 应用配置设置
struct AppConfigurationSettings: Codable {
    var autoDiscovery: Bool
    var discoveryInterval: TimeInterval
    var keepScreenOn: Bool
    var enableHapticFeedback: Bool
    var preferredVideoQuality: VideoResolution
    var enableAudioByDefault: Bool
    var showDebugInfo: Bool
    
    init(
        autoDiscovery: Bool = true,
        discoveryInterval: TimeInterval = 10.0,
        keepScreenOn: Bool = true,
        enableHapticFeedback: Bool = true,
        preferredVideoQuality: VideoResolution = .hd1080,
        enableAudioByDefault: Bool = true,
        showDebugInfo: Bool = false
    ) {
        self.autoDiscovery = autoDiscovery
        self.discoveryInterval = discoveryInterval
        self.keepScreenOn = keepScreenOn
        self.enableHapticFeedback = enableHapticFeedback
        self.preferredVideoQuality = preferredVideoQuality
        self.enableAudioByDefault = enableAudioByDefault
        self.showDebugInfo = showDebugInfo
    }
}

// MARK: - Debug Settings

/// 调试设置
struct DebugSettings: Codable {
    var enableLogging: Bool
    var logLevel: LogLevel
    var enableNetworkLogging: Bool
    var enablePerformanceMonitoring: Bool
    var simulateNetworkDelay: Bool
    var networkDelayMs: Int
    
    init(
        enableLogging: Bool = true,
        logLevel: LogLevel = .info,
        enableNetworkLogging: Bool = false,
        enablePerformanceMonitoring: Bool = false,
        simulateNetworkDelay: Bool = false,
        networkDelayMs: Int = 100
    ) {
        self.enableLogging = enableLogging
        self.logLevel = logLevel
        self.enableNetworkLogging = enableNetworkLogging
        self.enablePerformanceMonitoring = enablePerformanceMonitoring
        self.simulateNetworkDelay = simulateNetworkDelay
        self.networkDelayMs = networkDelayMs
    }
}

// MARK: - Log Level

/// 日志级别
enum LogLevel: String, CaseIterable, Codable {
    case verbose = "verbose"
    case debug = "debug"
    case info = "info"
    case warning = "warning"
    case error = "error"
    
    var displayName: String {
        return rawValue.capitalized
    }
}

// MARK: - Exportable Configuration

/// 可导出的配置（不包含调试设置）
private struct ExportableConfiguration: Codable {
    let networkConfiguration: NetworkConfiguration
    let appSettings: AppConfigurationSettings
}
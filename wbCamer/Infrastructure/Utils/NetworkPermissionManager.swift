//
//  NetworkPermissionManager.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import Foundation
import Network
import Combine

class NetworkPermissionManager: ObservableObject {
    @Published var hasLocalNetworkPermission = false
    @Published var isCheckingPermission = false
    @Published var hasCompletedInitialCheck = false
    
    private var testConnection: NWConnection?
    private let testHost = "***********" // mDNS multicast address
    private let testPort: UInt16 = 5353   // mDNS port
    
    init() {
        // 不在初始化时立即检查权限，等待用户主动操作
    }
    
    func checkLocalNetworkPermission() {
        isCheckingPermission = true
        
        // 尝试连接到本地网络多播地址来触发权限请求
        let host = NWEndpoint.Host(testHost)
        let port = NWEndpoint.Port(rawValue: testPort)!
        
        testConnection = NWConnection(host: host, port: port, using: .udp)
        
        testConnection?.stateUpdateHandler = { [weak self] state in
            DispatchQueue.main.async {
                switch state {
                case .ready:
                    self?.hasLocalNetworkPermission = true
                    self?.isCheckingPermission = false
                    self?.hasCompletedInitialCheck = true
                    self?.testConnection?.cancel()
                case .failed(let error):
                    // 检查是否是权限被拒绝
                    switch error {
                    case .posix(let posixError) where posixError == .EPERM:
                        print("Local network permission denied: \(posixError)")
                        self?.hasLocalNetworkPermission = false
                    default:
                        // 其他错误可能意味着有权限但连接失败
                        self?.hasLocalNetworkPermission = true
                    }
                    self?.isCheckingPermission = false
                    self?.hasCompletedInitialCheck = true
                    self?.testConnection?.cancel()
                case .cancelled:
                    self?.isCheckingPermission = false
                    self?.hasCompletedInitialCheck = true
                default:
                    break
                }
            }
        }
        
        testConnection?.start(queue: .global(qos: .utility))
        
        // 设置超时
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            if self.isCheckingPermission {
                self.hasLocalNetworkPermission = true // 假设有权限
                self.isCheckingPermission = false
                self.hasCompletedInitialCheck = true
                self.testConnection?.cancel()
            }
        }
    }
    
    func requestPermissionIfNeeded() {
        if !hasLocalNetworkPermission {
            checkLocalNetworkPermission()
        }
    }

    /// 延迟执行初始权限检查，用于应用启动后的权限检查
    func performDelayedInitialCheck() {
        // 延迟2秒执行权限检查，确保应用完全启动
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.checkLocalNetworkPermission()
        }
    }
}
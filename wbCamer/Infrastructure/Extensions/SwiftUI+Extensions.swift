//
//  SwiftUI+Extensions.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import SwiftUI

// MARK: - View Extensions

extension View {
    /// 条件修饰符
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
    
    /// 条件修饰符（带else分支）
    @ViewBuilder
    func `if`<TrueContent: View, FalseContent: View>(
        _ condition: Bool,
        if trueTransform: (Self) -> TrueContent,
        else falseTransform: (Self) -> FalseContent
    ) -> some View {
        if condition {
            trueTransform(self)
        } else {
            falseTransform(self)
        }
    }
    
    /// 隐藏视图
    @ViewBuilder
    func hidden(_ hidden: Bool) -> some View {
        if hidden {
            self.hidden()
        } else {
            self
        }
    }
    
    /// 圆角边框
    func roundedBorder(
        radius: CGFloat = 8,
        color: Color = .gray,
        lineWidth: CGFloat = 1
    ) -> some View {
        self
            .overlay(
                RoundedRectangle(cornerRadius: radius)
                    .stroke(color, lineWidth: lineWidth)
            )
    }
    
    /// 卡片样式
    func cardStyle(
        backgroundColor: Color = Color(.systemBackground),
        cornerRadius: CGFloat = 12,
        shadowRadius: CGFloat = 4
    ) -> some View {
        self
            .background(backgroundColor)
            .cornerRadius(cornerRadius)
            .shadow(color: .black.opacity(0.1), radius: shadowRadius, x: 0, y: 2)
    }
    
    /// 震动反馈
    func hapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) -> some View {
        self.onTapGesture {
            let impactFeedback = UIImpactFeedbackGenerator(style: style)
            impactFeedback.impactOccurred()
        }
    }
    
    /// 键盘工具栏
    func keyboardToolbar<Content: View>(@ViewBuilder content: () -> Content) -> some View {
        self.toolbar {
            ToolbarItemGroup(placement: .keyboard) {
                content()
            }
        }
    }
    
    /// 导航栏隐藏
    func hideNavigationBar() -> some View {
        self
            .navigationBarHidden(true)
            .navigationBarTitleDisplayMode(.inline)
    }
    
    /// 安全区域填充
    func fillSafeArea(_ edges: Edge.Set = .all) -> some View {
        self
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .ignoresSafeArea(.all, edges: edges)
    }
}

// MARK: - Color Extensions

extension Color {
    /// 十六进制颜色初始化
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// 应用主题色
    static let appPrimary = Color(hex: "007AFF")
    static let appSecondary = Color(hex: "5856D6")
    static let appSuccess = Color(hex: "34C759")
    static let appWarning = Color(hex: "FF9500")
    static let appError = Color(hex: "FF3B30")
    
    /// 连接状态颜色
    static func connectionColor(for state: ConnectionState) -> Color {
        switch state {
        case .connected:
            return .appSuccess
        case .connecting, .reconnecting:
            return .appWarning
        case .disconnected:
            return .gray
        case .failed:
            return .appError
        }
    }
}

// MARK: - Font Extensions

extension Font {
    /// 应用字体
    static let appTitle = Font.system(size: 24, weight: .bold, design: .default)
    static let appHeadline = Font.system(size: 18, weight: .semibold, design: .default)
    static let appBody = Font.system(size: 16, weight: .regular, design: .default)
    static let appCaption = Font.system(size: 12, weight: .regular, design: .default)
    static let appMono = Font.system(size: 14, weight: .regular, design: .monospaced)
}

// MARK: - Image Extensions

extension Image {
    /// 系统图标
    static let camera = Image(systemName: "camera.fill")
    static let video = Image(systemName: "video.fill")
    static let microphone = Image(systemName: "mic.fill")
    static let settings = Image(systemName: "gear")
    static let network = Image(systemName: "network")
    static let wifi = Image(systemName: "wifi")
    static let record = Image(systemName: "record.circle")
    static let play = Image(systemName: "play.fill")
    static let pause = Image(systemName: "pause.fill")
    static let stop = Image(systemName: "stop.fill")
    static let refresh = Image(systemName: "arrow.clockwise")
    static let trash = Image(systemName: "trash")
    static let copy = Image(systemName: "doc.on.doc")
    static let checkmark = Image(systemName: "checkmark")
    static let xmark = Image(systemName: "xmark")
    static let warning = Image(systemName: "exclamationmark.triangle")
    static let info = Image(systemName: "info.circle")
    
    /// PTZ控制图标
    static let ptzUp = Image(systemName: "chevron.up")
    static let ptzDown = Image(systemName: "chevron.down")
    static let ptzLeft = Image(systemName: "chevron.left")
    static let ptzRight = Image(systemName: "chevron.right")
    static let zoomIn = Image(systemName: "plus.magnifyingglass")
    static let zoomOut = Image(systemName: "minus.magnifyingglass")
    static let home = Image(systemName: "house")
}

// MARK: - Animation Extensions

extension Animation {
    /// 应用动画
    static let appDefault = Animation.easeInOut(duration: 0.3)
    static let appSpring = Animation.spring(response: 0.5, dampingFraction: 0.8)
    static let appBounce = Animation.interpolatingSpring(stiffness: 300, damping: 15)
}

// MARK: - Custom View Modifiers

/// 加载状态修饰符
struct LoadingModifier: ViewModifier {
    let isLoading: Bool
    let loadingText: String
    
    func body(content: Content) -> some View {
        ZStack {
            content
                .disabled(isLoading)
                .blur(radius: isLoading ? 2 : 0)
            
            if isLoading {
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.2)
                    Text(loadingText)
                        .font(.appCaption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(radius: 8)
            }
        }
        .animation(.appDefault, value: isLoading)
    }
}

extension View {
    func loading(_ isLoading: Bool, text: String = "加载中...") -> some View {
        modifier(LoadingModifier(isLoading: isLoading, loadingText: text))
    }
}

/// 错误提示修饰符
struct ErrorAlertModifier: ViewModifier {
    @Binding var error: Error?
    
    func body(content: Content) -> some View {
        content
            .alert("错误", isPresented: .constant(error != nil)) {
                Button("确定") {
                    error = nil
                }
            } message: {
                Text(error?.localizedDescription ?? "未知错误")
            }
    }
}

extension View {
    func errorAlert(_ error: Binding<Error?>) -> some View {
        modifier(ErrorAlertModifier(error: error))
    }
}


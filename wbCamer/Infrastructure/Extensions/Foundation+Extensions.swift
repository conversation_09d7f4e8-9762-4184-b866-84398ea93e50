//
//  Foundation+Extensions.swift
//  wbCamer
//
//  Created by REKROW on 2025/6/25.
//

import Foundation
import Network

// MARK: - String Extensions

extension String {
    /// 验证IP地址格式
    var isValidIPAddress: Bool {
        return IPv4Address(self) != nil || IPv6Address(self) != nil
    }
    
    /// 验证端口号
    var isValidPort: Bool {
        guard let port = Int(self) else { return false }
        return port >= 1 && port <= 65535
    }
    
    /// 本地化字符串
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /// 移除空白字符
    var trimmed: String {
        return trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// 安全的子字符串
    func safeSubstring(from index: Int, length: Int) -> String {
        let startIndex = self.index(self.startIndex, offsetBy: max(0, index))
        let endIndex = self.index(startIndex, offsetBy: min(length, self.count - index))
        return String(self[startIndex..<endIndex])
    }
}

// MARK: - Date Extensions

extension Date {
    /// 格式化为相对时间字符串
    var relativeTimeString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: self, relativeTo: Date())
    }
    
    /// 格式化为短时间字符串
    var shortTimeString: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: self)
    }
    
    /// 格式化为完整日期时间字符串
    var fullDateTimeString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .medium
        return formatter.string(from: self)
    }
    
    /// 检查是否在指定时间间隔内
    func isWithin(_ interval: TimeInterval, of date: Date = Date()) -> Bool {
        return abs(timeIntervalSince(date)) <= interval
    }
}

// MARK: - URL Extensions

extension URL {
    /// 创建WebSocket URL
    static func webSocket(host: String, port: UInt16, path: String = "/", secure: Bool = false) -> URL? {
        let scheme = secure ? "wss" : "ws"
        return URL(string: "\(scheme)://\(host):\(port)\(path)")
    }
    
    /// 创建HTTP URL
    static func http(host: String, port: UInt16, path: String = "/", secure: Bool = false) -> URL? {
        let scheme = secure ? "https" : "http"
        return URL(string: "\(scheme)://\(host):\(port)\(path)")
    }
    
    /// 添加查询参数
    func appendingQueryParameters(_ parameters: [String: String]) -> URL {
        guard var components = URLComponents(url: self, resolvingAgainstBaseURL: false) else {
            return self
        }
        
        var queryItems = components.queryItems ?? []
        for (key, value) in parameters {
            queryItems.append(URLQueryItem(name: key, value: value))
        }
        components.queryItems = queryItems
        
        return components.url ?? self
    }
}

// MARK: - Data Extensions

extension Data {
    /// 转换为十六进制字符串
    var hexString: String {
        return map { String(format: "%02hhx", $0) }.joined()
    }
    
    /// 从十六进制字符串创建Data
    init?(hexString: String) {
        let length = hexString.count / 2
        var data = Data(capacity: length)
        
        for i in 0..<length {
            let start = hexString.index(hexString.startIndex, offsetBy: i * 2)
            let end = hexString.index(start, offsetBy: 2)
            let byteString = String(hexString[start..<end])
            
            guard let byte = UInt8(byteString, radix: 16) else {
                return nil
            }
            data.append(byte)
        }
        
        self = data
    }
    
    /// 安全的JSON解码
    func jsonObject() -> Any? {
        return try? JSONSerialization.jsonObject(with: self, options: [])
    }
}

// MARK: - Dictionary Extensions

extension Dictionary where Key == String, Value == Any {
    /// 安全获取字符串值
    func stringValue(for key: String) -> String? {
        return self[key] as? String
    }
    
    /// 安全获取整数值
    func intValue(for key: String) -> Int? {
        if let value = self[key] as? Int {
            return value
        } else if let stringValue = self[key] as? String {
            return Int(stringValue)
        }
        return nil
    }
    
    /// 安全获取浮点数值
    func doubleValue(for key: String) -> Double? {
        if let value = self[key] as? Double {
            return value
        } else if let stringValue = self[key] as? String {
            return Double(stringValue)
        }
        return nil
    }
    
    /// 安全获取布尔值
    func boolValue(for key: String) -> Bool? {
        if let value = self[key] as? Bool {
            return value
        } else if let stringValue = self[key] as? String {
            return stringValue.lowercased() == "true" || stringValue == "1"
        }
        return nil
    }
}

// MARK: - Array Extensions

extension Array {
    /// 安全获取元素
    subscript(safe index: Int) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
    
    /// 移除满足条件的第一个元素
    @discardableResult
    mutating func removeFirst(where predicate: (Element) throws -> Bool) rethrows -> Element? {
        guard let index = try firstIndex(where: predicate) else { return nil }
        return remove(at: index)
    }
}

// MARK: - UserDefaults Extensions

extension UserDefaults {
    /// 安全设置Codable对象
    func setCodable<T: Codable>(_ object: T, forKey key: String) {
        if let data = try? JSONEncoder().encode(object) {
            set(data, forKey: key)
        }
    }
    
    /// 安全获取Codable对象
    func getCodable<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        guard let data = data(forKey: key) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }
}

// MARK: - DispatchQueue Extensions

extension DispatchQueue {
    /// 安全的主线程执行
    static func safeMain(_ block: @escaping () -> Void) {
        if Thread.isMainThread {
            block()
        } else {
            DispatchQueue.main.async(execute: block)
        }
    }
    
    /// 延迟执行
    static func delay(_ delay: TimeInterval, execute: @escaping () -> Void) {
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: execute)
    }
}
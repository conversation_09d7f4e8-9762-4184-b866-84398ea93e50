wbCamer(4902,0x1f6dc31c0) malloc: xzm: failed to initialize deferred reclamation buffer (46)
Error creating the CFMessagePort needed to communicate with PPT.
🔧 EagleServiceDiscovery singleton initialized
Network monitoring started
🏗️ CameraManager initialized
Network monitoring started
Network became available - attempting to reconnect WebSocket
[2025-07-13 22:54:57.935] [CameraManager] WebSocket state changed: disconnected
[2025-07-13 22:54:57.935] ⚠️ WebSocket disconnected - will attempt reconnection in background
✅ CameraManager setup complete
Network status updated: WiFi, Quality: good
Starting initial device discovery scan...
🔍 EagleServiceDiscovery.startManualScan() called
Browser ready
🔍 Service discovered: ZCAM_P2-R1_010374._eagle._tcp.local.
📡 Found Eagle service: ZCAM_P2-R1_010374 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM_P2-R1_010374 of type _eagle._tcp in domain local.
Successfully resolved endpoint for ZCAM_P2-R1_010374
Extracting IP from endpoint for ZCAM_P2-R1_010374: *************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM_P2-R1_010374: *************:80
Cannot connect: baseURL is empty
nw_resolver_start_query_timer_block_invoke [C1.1.1] Query fired: did not receive all answers in time for ZCAM-P2-R1-010374.local.:80@en0
Successfully resolved Eagle device: ZCAM_P2-R1_010374 -> *************
[C1 ZCAM_P2-R1_010374._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
Browser cancelled
Failed to send CA Event for app launch measurements for ca_event_type: 1 event_name: com.apple.app_launch_measurement.ExtendedLaunchMetrics
📞 CameraManager.connect() called for *************:80
🔗 Attempting to connect to camera: Eagle Camera at *************:80
APIClient base URL updated to: http://*************
✅ PTZManager configured with APIClient for *************
✅ Camera connectivity test passed (latency: 0.0075359344482421875)
🔄 Establishing camera connection to *************:80
📡 Getting camera info...
🚀 Request: GET http://*************/info
📋 Headers: ["Accept": "application/json", "Content-Type": "application/json"]
✅ Response: 200 from http://*************/info
📥 Response Data: 1249 bytes
📄 Response Body: {"cameraName":"P2-R1_010374","model":"e2ptz","number":"1","sw":"0.9.15","hw":"3.0","mac":"0e:7b:b6:84:2c:2c","sn":"921M0010374","nickName":"P2-R1","eth_ip":"*************","ip":"*************","pixelLinkMode":"Single","feature":{"platform":"hisi","product_catalog":"","rebootAfterClearSettings":"0","rebootAfterVideoSystem":"0","upgradeWOCard":"1","fwName":"*.zip","fwCheck":"1","md5Check":"1","setCfgToAll":"0","syncOnlyMaster":"1","stopPreviewInSnap":"0","stopPreviewInTimelapse":"0","photoSupport":"1","autoFraming":true,"ezFraming":true,"facedb":true,"wsSupport":"1","release":true,"snapSupportExposureMode":"1","blComp":true,"preRoll":true,"aLineIn":true,"ezLink":true,"viscaSupport":true,"irService":true,"freeD":true,"sdiSupport":true,"dzoomDigital":true,"lens_ctrl_ptz":true,"snmpSupport":true,"advanceColor":true,"eisSupport":true,"genlockSupport":true,"wifiSupport":false,"waitStableSupport":true,"snapSupportFmt":{"isAllFmt":"1","fmt":["4KP23.98","4KP29.97","4KP59.94","1080P23.98","1080P29.97","1080P59.94","4KP25","4KP50","1080P25","1080P50","4KP24","1080P24"]},"timelapseSupportFmt":{"isAllFmt":"1","fmt":["4KP23.98","4KP29.97","4KP59.94","1080P23.98","1080P29.97","1080P59.94","4KP25","4KP50","1080P25","1080P50","4KP24","1080P24"]}}}
✅ Camera info request successful: CameraInfoResponse(model: Optional("e2ptz"), sw: Optional("0.9.15"), mac: Optional("0e:7b:b6:84:2c:2c"), eth_ip: Optional("*************"), sn: Optional("921M0010374"))
Camera Model: e2ptz
Software Version: 0.9.15
MAC Address: 0e:7b:b6:84:2c:2c
IP Address: *************
Serial Number: 921M0010374
🔌 Connecting WebSocket to camera...
🔌 直接连接到WebSocket地址: ws://*************:81/
Connecting to WebSocket: ws://*************:81/
🔌 WebSocket state changed: disconnected
[CameraManager] Updating isConnected to true on main thread (from updateCameraInfo)
[CameraManager] isConnected updated: true
[2025-07-13 22:55:17.490] [CameraManager] WebSocket state changed: connecting
🔌 WebSocket state changed: connecting
[2025-07-13 22:55:17.486] 🔌 WebSocket状态: 正在连接...
WebSocket connection opened with protocol: none
[2025-07-13 22:55:17.508] [CameraManager] WebSocket state changed: connected
[2025-07-13 22:55:17.508] ✅ WebSocket reconnected successfully
🔌 WebSocket state changed: connected
✅ WebSocket connected successfully to: ws://*************:81/
🎉 Camera connection established successfully!
✅ WebSocket connection established, relying on real-time status updates
🎥 Initializing WebRTC for video streaming...
[2025-07-13 22:55:17.508] 🔌 WebSocket状态: 已连接
Stopped heartbeat timeout monitoring
Started P2-R1 heartbeat timeout monitoring
[CameraManager] Updating isConnected to true on main thread
[CameraManager] isConnected updated: true
[CameraManager] Updating shouldShowVideoInterface: false (isConnected: true, hasCamera: true, isWebRTCConnected: false)
[CameraManager] Initializing optimized WebRTC client...
[CameraManager] Optimized WebRTC client created: true
[CameraManager] WebRTC client after initialization: true
[CameraManager] WebRTC connection state changed: RTCPeerConnectionState(rawValue: 0)
[CameraManager] Updating shouldShowVideoInterface: false (isConnected: true, hasCamera: true, isWebRTCConnected: false)
[CameraManager] WebRTC ICE state changed: RTCIceConnectionState(rawValue: 0)
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArray0 0x1f89fbe10>(

)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 0
ℹ️ [Parse AI] 没有检测到目标，返回空数组
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArray0 0x1f89fbe10>(

)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 0
ℹ️ [Parse AI] 没有检测到目标，返回空数组
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArray0 0x1f89fbe10>(

)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 0
ℹ️ [Parse AI] 没有检测到目标，返回空数组
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x3038c0e60>(
2,
0,
0,
35,
0.18125,
0.14583,
0.275,
0.61666
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArray0 0x1f89fbe10>(

)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 0
ℹ️ [Parse AI] 没有检测到目标，返回空数组
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x303895310>(
2,
0,
0,
37,
0.21875,
0.16388,
0.27343,
0.60416
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x3038d3480>(
2,
0,
0,
41,
0.2289,
0.16249,
0.27109,
0.60277
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArray0 0x1f89fbe10>(

)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 0
ℹ️ [Parse AI] 没有检测到目标，返回空数组
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x303895040>(
2,
0,
0,
35,
0.27812,
0.1875,
0.25624,
0.58472
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x3038d3610>(
2,
0,
0,
51,
0.29296,
0.18472,
0.25078,
0.58472
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x30388a580>(
2,
0,
0,
64,
0.31328,
0.19166,
0.24531,
0.57916
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x3038d3340>(
2,
0,
0,
57,
0.33203,
0.2,
0.23984,
0.57499
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 75 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x303892170>(
2,
0,
0,
61,
0.34999,
0.21527,
0.22734,
0.56388
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🎥 Starting WebRTC connection...
🎥 Connecting to WebRTC Streamer at *************:8000
🔍 [AI Detection] 开始处理AI检测消息: ["debug": 0, "sel": 0, "pending": 0, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "objs": <__NSArrayI 0x303892170>(
2,
0,
0,
59,
0.35937,
0.22222,
0.22109,
0.55833
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
tcp_input [C8:2] flags=[R.] seq=0, ack=447615561, win=0 state=SYN_SENT rcv_nxt=0, snd_una=447615560
nw_endpoint_flow_failed_with_error [C8 *************:8443 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, dns, uses wifi)] already failing, returning
Connection 8: received failure notification
Connection 8: failed to connect 1:61, reason -1
Connection 8: encountered error(1:61)
nw_connection_copy_connected_local_endpoint_block_invoke [C8] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C8] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
Task <0888F20D-1BF9-4D96-8334-87446557A276>.<3> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
Task <0888F20D-1BF9-4D96-8334-87446557A276>.<3> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x301507ea0 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <0888F20D-1BF9-4D96-8334-87446557A276>.<3>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <0888F20D-1BF9-4D96-8334-87446557A276>.<3>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://*************:8443/api/getIceServers, NSErrorFailingURLKey=http://*************:8443/api/getIceServers, _kCFStreamErrorDomainKey=1}
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x30388f2a0>(
2,
0,
0,
52,
0.35781,
0.22916,
0.21406,
0.54305
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x3038d3340>(
2,
0,
0,
59,
0.34843,
0.22638,
0.21015,
0.53194
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x30388bb10>(
2,
0,
0,
64,
0.34375,
0.22499,
0.20781,
0.52638
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x3038919f0>(
2,
0,
0,
66,
0.3414,
0.22361,
0.20703,
0.52361
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x3038919f0>(
2,
0,
0,
64,
0.33984,
0.22361,
0.20624,
0.52222
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x303895450>(
2,
0,
0,
60,
0.3375,
0.24027,
0.20546,
0.51805
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x3038919f0>(
2,
0,
0,
57,
0.34687,
0.25277,
0.2039,
0.51666
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x3038919f0>(
2,
0,
0,
56,
0.35156,
0.25833,
0.20312,
0.51527
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x30388bb10>(
2,
0,
0,
60,
0.36484,
0.25972,
0.20234,
0.51388
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x303895130>(
2,
0,
0,
57,
0.37109,
0.26111,
0.20156,
0.51249
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x303890910>(
2,
0,
0,
43,
0.38593,
0.26388,
0.20546,
0.51388
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
ℹ️ HTTP status polling disabled - using WebSocket for real-time updates
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x303890910>(
2,
0,
0,
55,
0.4039,
0.27083,
0.20624,
0.51527
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["num": 1, "debug": 0, "sel": 0, "pending": 0, "what": AI, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "objs": <__NSArrayI 0x303891c70>(
2,
0,
0,
61,
0.42343,
0.27916,
0.20781,
0.51805
)
, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
Stream settings changed
✅ WebRTC Streamer connected successfully to Eagle Camera
[CameraManager] Updating shouldShowVideoInterface: true (isConnected: true, hasCamera: true, isWebRTCConnected: true)
[CameraViews] Body refresh - isConnected: true, currentCamera: Optional("Eagle Camera"), webRTCClient: true
[CameraViews] WebRTC remoteVideoTrack: true
[CameraViews] ✅ Condition met: camera=Eagle Camera, webRTC available, hasVideoTrack=true
[CameraViews] ✅ WebRTC client available, creating ProductionVideoView
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x30388eda0>(
2,
0,
0,
58,
0.44062,
0.28472,
0.21093,
0.52361
)
, "cols": 8, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "what": AI, "num": 1, "debug": 0, "pending": 0, "sel": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
[WebRTCVideoView] ✅ Video track available, showing optimized video
🎨 [AIDetectionOverlayView] 渲染UI，当前目标数量: 0
[CameraViews] WebRTCVideoView appeared
👀 [AIDetectionOverlayView] 视图出现，开始设置订阅
🔗 [AIDetectionOverlayView] 设置WebSocket订阅
✅ [AIDetectionOverlayView] WebSocket订阅设置完成
[CameraManager] WebRTC ICE state changed: RTCIceConnectionState(rawValue: 1)
[CameraManager] Updating shouldShowVideoInterface: false (isConnected: true, hasCamera: true, isWebRTCConnected: false)
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
[CameraViews] Body refresh - isConnected: true, currentCamera: Optional("Eagle Camera"), webRTCClient: true
[CameraViews] WebRTC remoteVideoTrack: true
[CameraViews] ✅ Condition met: camera=Eagle Camera, webRTC available, hasVideoTrack=true
[CameraViews] ✅ WebRTC client available, creating ProductionVideoView
[WebRTCVideoView] ✅ Video track available, showing optimized video
[CameraManager] WebRTC connection state changed: RTCPeerConnectionState(rawValue: 1)
[CameraManager] Updating shouldShowVideoInterface: false (isConnected: true, hasCamera: true, isWebRTCConnected: false)
🔍 [AI Detection] 开始处理AI检测消息: ["what": AI, "sel": 0, "pending": 0, "cols": 8, "objs": <__NSArrayI 0x30387f7a0>(
2,
0,
0,
49,
0.45781,
0.29027,
0.2125,
0.53055
)
, "num": 1, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
[CameraManager] WebRTC connection state changed: RTCPeerConnectionState(rawValue: 2)
[CameraManager] Updating shouldShowVideoInterface: true (isConnected: true, hasCamera: true, isWebRTCConnected: true)
[CameraViews] Body refresh - isConnected: true, currentCamera: Optional("Eagle Camera"), webRTCClient: true
[CameraViews] WebRTC remoteVideoTrack: true
[CameraViews] ✅ Condition met: camera=Eagle Camera, webRTC available, hasVideoTrack=true
[CameraViews] ✅ WebRTC client available, creating ProductionVideoView
[WebRTCVideoView] ✅ Video track available, showing optimized video
🎨 [AIDetectionOverlayView] 渲染UI，当前目标数量: 0
[CameraViews] WebRTCVideoView appeared
👀 [AIDetectionOverlayView] 视图出现，开始设置订阅
🔗 [AIDetectionOverlayView] 设置WebSocket订阅
✅ [AIDetectionOverlayView] WebSocket订阅设置完成
[CameraManager] WebRTC ICE state changed: RTCIceConnectionState(rawValue: 2)
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038751d0>(
2,
0,
0,
73,
0.47734,
0.30138,
0.21484,
0.53472
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x30387f4d0>(
2,
0,
0,
60,
0.49609,
0.31388,
0.21562,
0.53611
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x30387f4d0>(
2,
0,
0,
46,
0.51484,
0.31805,
0.21328,
0.53333
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038748c0>(
2,
0,
0,
48,
0.52421,
0.31944,
0.21171,
0.53194
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303875c70>(
2,
0,
0,
55,
0.5375,
0.33333,
0.20859,
0.52777
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303875c70>(
2,
0,
0,
55,
0.54453,
0.34027,
0.20703,
0.52638
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303874370>(
2,
0,
0,
61,
0.54765,
0.34444,
0.20624,
0.52499
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
📊 [Parse AI] 检测到目标数量: 1
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x30389e8f0>(
2,
0,
0,
55,
0.54921,
0.34583,
0.20624,
0.52499
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038b8a50>(
2,
0,
0,
51,
0.55,
0.34722,
0.20624,
0.52499
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x30387f660>(
2,
0,
0,
46,
0.53984,
0.33472,
0.20859,
0.52638
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038d1f90>(
2,
0,
0,
61,
0.52343,
0.32777,
0.21171,
0.52361
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038962b0>(
2,
0,
0,
72,
0.50156,
0.32499,
0.21875,
0.52499
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038b9360>(
2,
0,
0,
50,
0.47812,
0.31527,
0.2289,
0.52638
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303894b90>(
2,
0,
0,
64,
0.45234,
0.29722,
0.23828,
0.52916
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303893b60>(
2,
0,
0,
60,
0.42968,
0.28611,
0.2414,
0.53055
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303890cd0>(
2,
0,
0,
59,
0.41874,
0.28055,
0.24296,
0.53194
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038b8730>(
2,
0,
0,
48,
0.41015,
0.27916,
0.23749,
0.52916
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038ba2b0>(
2,
0,
0,
51,
0.40546,
0.27777,
0.23515,
0.52777
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303890cd0>(
2,
0,
0,
43,
0.39453,
0.27361,
0.23203,
0.52499
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038b8a50>(
2,
0,
0,
60,
0.38203,
0.26388,
0.2289,
0.52638
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038b80f0>(
2,
0,
0,
60,
0.37578,
0.25833,
0.22734,
0.52638
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303895130>(
2,
0,
0,
57,
0.37421,
0.25694,
0.21562,
0.53055
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303895e00>(
2,
0,
0,
57,
0.37343,
0.25694,
0.21015,
0.53333
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303894b90>(
2,
0,
0,
60,
0.37343,
0.25694,
0.20703,
0.53472
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303894c80>(
2,
0,
0,
52,
0.37343,
0.25694,
0.20546,
0.53472
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303895680>(
2,
0,
0,
51,
0.38281,
0.25833,
0.21015,
0.53472
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038b8000>(
2,
0,
0,
58,
0.39921,
0.26111,
0.21093,
0.5375
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303895e00>(
2,
0,
0,
60,
0.40703,
0.26249,
0.21171,
0.53888
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
[HardwareDecoder] Using hardware H264 decoder
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection

🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303895cc0>(
2,
0,
0,
61,
0.41953,
0.26111,
0.21171,
0.54027
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038963f0>(
2,
0,
0,
52,
0.43515,
0.26666,
0.21484,
0.54166
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔍 [Parse AI] 解析目标 0
🔄 [AIDetectionOverlayView] 已更新检测目标列表
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038ba3a0>(
2,
0,
0,
46,
0.45312,
0.275,
0.21328,
0.54583
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038964e0>(
2,
0,
0,
46,
0.4625,
0.27916,
0.2125,
0.54861
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038d6670>(
2,
0,
0,
48,
0.46718,
0.28055,
0.2125,
0.55
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038740a0>(
2,
0,
0,
51,
0.46953,
0.28194,
0.2125,
0.55
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 77 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 77 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🎨 [AIDetectionOverlayView] 渲染UI，当前目标数量: 0
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x30387cf50>(
2,
0,
0,
57,
0.47031,
0.28194,
0.2125,
0.55
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x30389d040>(
2,
0,
0,
49,
0.47109,
0.28194,
0.2125,
0.55
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x30389e8f0>(
2,
0,
0,
60,
0.47109,
0.28194,
0.2125,
0.55
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303890af0>(
2,
0,
0,
60,
0.46562,
0.27638,
0.20859,
0.55833
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038ba3a0>(
2,
0,
0,
60,
0.45234,
0.27083,
0.20937,
0.5625
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c5d60>(
2,
0,
0,
56,
0.43437,
0.26249,
0.21484,
0.55972
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038b9360>(
2,
0,
0,
56,
0.41718,
0.26388,
0.22265,
0.55
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303896a80>(
2,
0,
0,
55,
0.40859,
0.26388,
0.22656,
0.54583
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
56,
0.4039,
0.26388,
0.2289,
0.54305
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c5d60>(
2,
0,
0,
48,
0.3914,
0.25972,
0.2414,
0.53611
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c68a0>(
2,
0,
0,
54,
0.38515,
0.25833,
0.24765,
0.53333
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303896940>(
2,
0,
0,
51,
0.38203,
0.25694,
0.25078,
0.53194
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303896940>(
2,
0,
0,
55,
0.38046,
0.25694,
0.25234,
0.53055
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038969e0>(
2,
0,
0,
60,
0.37968,
0.25694,
0.25312,
0.53055
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x30387ceb0>(
2,
0,
0,
66,
0.37968,
0.25694,
0.2539,
0.53055
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
63,
0.37968,
0.25694,
0.2539,
0.53055
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["what": AI, "sel": 0, "objs": <__NSArrayI 0x3038b9360>(
2,
0,
0,
60,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "pending": 0, "cols": 8, "num": 1]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038968a0>(
2,
0,
0,
55,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303892d50>(
2,
0,
0,
51,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896120>(
2,
0,
0,
50,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c5360>(
2,
0,
0,
49,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038968a0>(
2,
0,
0,
40,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038b9360>(
2,
0,
0,
43,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c5cc0>(
2,
0,
0,
52,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303892df0>(
2,
0,
0,
42,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896990>(
2,
0,
0,
46,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038968a0>(
2,
0,
0,
48,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c5cc0>(
2,
0,
0,
50,
0.37968,
0.25694,
0.2539,
0.53055
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892d50>(
2,
0,
0,
50,
0.38203,
0.27083,
0.25312,
0.51805
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038ba2b0>(
2,
0,
0,
49,
0.38281,
0.27777,
0.25312,
0.51249
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303892c60>(
2,
0,
0,
49,
0.38359,
0.28194,
0.25312,
0.50972
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c68a0>(
2,
0,
0,
51,
0.38359,
0.28333,
0.25312,
0.5083299999999999
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303891b80>(
2,
0,
0,
54,
0.38359,
0.28472,
0.25312,
0.5069399999999999
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c77a0>(
2,
0,
0,
49,
0.39531,
0.28333,
0.24296,
0.51666
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303890af0>(
2,
0,
0,
52,
0.40156,
0.28333,
0.23749,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896800>(
2,
0,
0,
52,
0.41484,
0.28749,
0.22734,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303892e90>(
2,
0,
0,
51,
0.42109,
0.29027,
0.22187,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c68a0>(
2,
0,
0,
52,
0.42421,
0.29166,
0.21953,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c5360>(
2,
0,
0,
52,
0.42578,
0.29166,
0.21796,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038b8050>(
2,
0,
0,
57,
0.42656,
0.29166,
0.21718,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x30389e940>(
2,
0,
0,
52,
0.42734,
0.29166,
0.21718,
0.52222
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303890af0>(
2,
0,
0,
55,
0.42734,
0.29166,
0.21718,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038b8be0>(
2,
0,
0,
52,
0.42734,
0.29166,
0.21718,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038b8ff0>(
2,
0,
0,
55,
0.42734,
0.29166,
0.21718,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
56,
0.42734,
0.29166,
0.21718,
0.52222
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892e40>(
2,
0,
0,
61,
0.42734,
0.29166,
0.21718,
0.52222
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038b8be0>(
2,
0,
0,
58,
0.42734,
0.29166,
0.21718,
0.52222
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303896120>(
2,
0,
0,
66,
0.42734,
0.29166,
0.21718,
0.52222
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c5360>(
2,
0,
0,
67,
0.4375,
0.29027,
0.21171,
0.53333
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303896990>(
2,
0,
0,
68,
0.44296,
0.28888,
0.20859,
0.53888
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303896120>(
2,
0,
0,
67,
0.4539,
0.29444,
0.20468,
0.5375
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
70,
0.45937,
0.29722,
0.20312,
0.53611
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303891b80>(
2,
0,
0,
67,
0.4625,
0.29861,
0.20234,
0.53611
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x30387d810>(
2,
0,
0,
70,
0.46406,
0.3,
0.20156,
0.53611
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
66,
0.46484,
0.3,
0.20156,
0.53611
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038968a0>(
2,
0,
0,
66,
0.46484,
0.3,
0.20156,
0.53611
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038b8050>(
2,
0,
0,
64,
0.46484,
0.3,
0.20156,
0.53611
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
64,
0.46484,
0.3,
0.20156,
0.53611
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c6580>(
2,
0,
0,
64,
0.46484,
0.3,
0.20156,
0.53611
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303896120>(
2,
0,
0,
66,
0.4539,
0.29027,
0.20546,
0.54027
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038b8ff0>(
2,
0,
0,
60,
0.44843,
0.28472,
0.20703,
0.54305
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
64,
0.44609,
0.28194,
0.20781,
0.54444
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892e90>(
2,
0,
0,
64,
0.44453,
0.28055,
0.20859,
0.54444
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038969e0>(
2,
0,
0,
60,
0.44374,
0.28055,
0.20859,
0.54444
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c5360>(
2,
0,
0,
61,
0.44374,
0.28055,
0.20859,
0.54444
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038ba2b0>(
2,
0,
0,
60,
0.44374,
0.28055,
0.20859,
0.54444
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896760>(
2,
0,
0,
64,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c6670>(
2,
0,
0,
67,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038b8ff0>(
2,
0,
0,
66,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038ba9e0>(
2,
0,
0,
67,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c5360>(
2,
0,
0,
60,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c6580>(
2,
0,
0,
58,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038917c0>(
2,
0,
0,
52,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896760>(
2,
0,
0,
48,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038ba3a0>(
2,
0,
0,
49,
0.44374,
0.28055,
0.20859,
0.54444
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038967b0>(
2,
0,
0,
52,
0.4375,
0.28194,
0.20624,
0.54027
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896120>(
2,
0,
0,
64,
0.43437,
0.28194,
0.20546,
0.5375
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303891b80>(
2,
0,
0,
49,
0.43203,
0.27638,
0.19765,
0.52916
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038ba3a0>(
2,
0,
0,
49,
0.42421,
0.27222,
0.19531,
0.52222
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896760>(
2,
0,
0,
48,
0.42031,
0.26944,
0.19453,
0.51805
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c56d0>(
2,
0,
0,
51,
0.40859,
0.25972,
0.19531,
0.51944
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038c57c0>(
2,
0,
0,
51,
0.39531,
0.24861,
0.19453,
0.51805
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896850>(
2,
0,
0,
48,
0.38906,
0.24305,
0.19374,
0.51805
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038967b0>(
2,
0,
0,
43,
0.37968,
0.23333,
0.19296,
0.51666
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038ba300>(
2,
0,
0,
46,
0.37265,
0.23611,
0.19296,
0.51388
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038b8ff0>(
2,
0,
0,
42,
0.36875,
0.23749,
0.19296,
0.51249
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892e90>(
2,
0,
0,
49,
0.35937,
0.23611,
0.19453,
0.5027700000000001
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892c60>(
2,
0,
0,
49,
0.34843,
0.23055,
0.19609,
0.50138
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303896800>(
2,
0,
0,
42,
0.3375,
0.23611,
0.19609,
0.5027700000000001
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038ba2b0>(
2,
0,
0,
46,
0.33203,
0.23888,
0.19609,
0.5027700000000001
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c6670>(
2,
0,
0,
40,
0.32578,
0.24444,
0.19453,
0.50138
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303895270>(
2,
0,
0,
48,
0.31093,
0.25277,
0.19453,
0.49722
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038967b0>(
2,
0,
0,
42,
0.29687,
0.26249,
0.19531,
0.49305
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038ba9e0>(
2,
0,
0,
37,
0.28437,
0.26944,
0.19453,
0.48888
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArray0 0x1f89fbe10>(

)
, "num": 0, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 0
ℹ️ [Parse AI] 没有检测到目标，返回空数组
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038b8050>(
2,
0,
0,
46,
0.25,
0.29861,
0.19374,
0.47361
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892ee0>(
2,
0,
0,
56,
0.24531,
0.30555,
0.19296,
0.47361
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x3038ba3a0>(
2,
0,
0,
49,
0.24296,
0.30833,
0.19218,
0.47361
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038c6440>(
2,
0,
0,
43,
0.23906,
0.31388,
0.18828,
0.47361
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892f30>(
2,
0,
0,
51,
0.23906,
0.32083,
0.17968,
0.47361
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038c6440>(
2,
0,
0,
42,
0.23906,
0.32361,
0.17578,
0.47361
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c5540>(
2,
0,
0,
37,
0.23906,
0.32499,
0.17343,
0.47361
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303896a80>(
2,
0,
0,
37,
0.23906,
0.32638,
0.17265,
0.47361
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892c60>(
2,
0,
0,
42,
0.23906,
0.32638,
0.17187,
0.47361
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c68a0>(
2,
0,
0,
52,
0.23203,
0.32638,
0.17656,
0.46666
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892c60>(
2,
0,
0,
66,
0.22343,
0.32777,
0.18281,
0.45694
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892e90>(
2,
0,
0,
66,
0.2125,
0.33055,
0.19062,
0.44583
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892e90>(
2,
0,
0,
64,
0.19687,
0.32777,
0.19843,
0.44166
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038968a0>(
2,
0,
0,
58,
0.17812,
0.31805,
0.20546,
0.44583
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x303892f30>(
2,
0,
0,
48,
0.16328,
0.30833,
0.21093,
0.44999
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038b8be0>(
2,
0,
0,
49,
0.14531,
0.29444,
0.21562,
0.45555
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c68a0>(
2,
0,
0,
49,
0.125,
0.27916,
0.22031,
0.46388
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038c68a0>(
2,
0,
0,
41,
0.11484,
0.27222,
0.22265,
0.46805
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["objs": <__NSArrayI 0x303896850>(
2,
0,
0,
38,
0.10937,
0.26805,
0.22343,
0.47083
)
, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "sel": 0, "num": 1, "what": AI, "cols": 8, "pending": 0, "debug": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArray0 0x1f89fbe10>(

)
, "num": 0, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 0
ℹ️ [Parse AI] 没有检测到目标，返回空数组
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038b8ff0>(
2,
0,
0,
38,
0.12968,
0.26249,
0.24218,
0.49444
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
37,
0.14531,
0.26388,
0.23828,
0.49583
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x303892f30>(
2,
0,
0,
38,
0.17343,
0.26944,
0.23046,
0.49722
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["target_ids": <__NSArray0 0x1f89fbe10>(

)
, "num": 1, "cols": 8, "objs": <__NSArrayI 0x3038b8ff0>(
2,
0,
0,
34,
0.19687,
0.275,
0.22421,
0.49861
)
, "sel": 0, "what": AI, "debug": 0, "pending": 0]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038c7750>(
2,
0,
0,
61,
0.23671,
0.28333,
0.21484,
0.49444
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
37,
0.275,
0.28888,
0.20546,
0.49166
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x303892d00>(
2,
0,
0,
49,
0.30937,
0.29444,
0.19843,
0.49305
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038c7750>(
2,
0,
0,
43,
0.3414,
0.29722,
0.19374,
0.49305
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038969e0>(
2,
0,
0,
40,
0.36796,
0.3,
0.19062,
0.48888
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "pending": 0, "objs": <__NSArrayI 0x3038c5720>(
2,
0,
0,
60,
0.38984,
0.30138,
0.18906,
0.48472
)
, "num": 1, "what": AI, "debug": 0, "cols": 8]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038969e0>(
2,
0,
0,
48,
0.40546,
0.30138,
0.18828,
0.48333
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: statusUpdate
ℹ️ [AIDetectionOverlayView] 忽略非AI检测消息: statusUpdate
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038c7750>(
2,
0,
0,
46,
0.42109,
0.30138,
0.18593,
0.48611
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 78 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 78 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038b90e0>(
2,
0,
0,
49,
0.43359,
0.30277,
0.18515,
0.48611
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038c6580>(
2,
0,
0,
55,
0.43984,
0.30277,
0.18437,
0.48611
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x303896120>(
2,
0,
0,
49,
0.44531,
0.30138,
0.18515,
0.49444
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038c6580>(
2,
0,
0,
56,
0.44843,
0.30138,
0.18515,
0.49861
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x303891b80>(
2,
0,
0,
57,
0.45468,
0.30555,
0.18359,
0.5041600000000001
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038c57c0>(
2,
0,
0,
57,
0.46328,
0.30555,
0.18203,
0.5055500000000001
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x303892e40>(
2,
0,
0,
59,
0.46718,
0.30555,
0.18125,
0.5055500000000001
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x303895270>(
2,
0,
0,
61,
0.46953,
0.30555,
0.18125,
0.5055500000000001
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038b8780>(
2,
0,
0,
59,
0.47031,
0.30555,
0.18125,
0.5055500000000001
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
🔍 [AI Detection] 开始处理AI检测消息: ["sel": 0, "objs": <__NSArrayI 0x3038c6440>(
2,
0,
0,
67,
0.47109,
0.30555,
0.18125,
0.5055500000000001
)
, "num": 1, "pending": 0, "target_ids": <__NSArray0 0x1f89fbe10>(

)
, "debug": 0, "cols": 8, "what": AI]
🔍 [Parse AI] 开始解析AI检测数据
📊 [Parse AI] 检测到目标数量: 1
🔍 [Parse AI] 解析目标 0
❌ [Parse AI] 目标 0 数据不完整，跳过
🎯 [Parse AI] 解析完成，共创建 0 个有效目标
✅ [AI Detection] 成功解析到 0 个目标
📦 [AI Detection] 创建AIDetectionMessage，包含 0 个目标
✅ [AI Detection] 成功编码AI检测消息，数据大小: 79 字节
📨 [AI Detection] 创建WebSocket消息，类型: aiDetection
🚀 [AI Detection] 发送消息到messageSubject
🤖 AI detection message handled by WebSocketManager
✅ [AI Detection] 消息已发送到messageSubject
📥 [AIDetectionOverlayView] 收到WebSocket消息，类型: aiDetection
🎯 [AIDetectionOverlayView] 收到AI检测消息
📦 [AIDetectionOverlayView] 消息包含数据，大小: 79 字节
✅ [AIDetectionOverlayView] 成功解码AI检测消息，包含 0 个目标
🔄 [AIDetectionOverlayView] 已更新检测目标列表
Message from debugger: killed

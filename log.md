wbCamer - 4932
SwiftUI
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/AIDetectionOverlayView.swift:116 Invalid frame dimension (negative or non-finite).

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/AIDetectionOverlayView.swift:116 Invalid frame dimension (negative or non-finite).

wbCamer
Command SwiftCompile failed with a nonzero exit code

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/CameraManager.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/CameraManager.swift:488:50 Cannot infer type of closure parameter 'completion' without a type annotation

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/CameraManager.swift:536:60 Ambiguous use of 'networkError'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/CameraManager.swift:589:86 'APIError' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/CameraManager.swift:961:50 Cannot infer type of closure parameter 'completion' without a type annotation

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:68:9 'async' call in a function that does not support concurrency

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:95:31 Referencing operator function '!=' on 'BinaryInteger' requires that 'CardWebRTCState' conform to 'BinaryInteger'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:95:64 Referencing operator function '!=' on 'BinaryInteger' requires that 'CardWebRTCState' conform to 'BinaryInteger'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:122:59 Type 'WebRTCError' has no member 'connectionFailed'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:163:31 Type 'WebRTCError' has no member 'factoryNotInitialized'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:179:31 Type 'WebRTCError' has no member 'peerConnectionCreationFailed'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:185:31 Type 'WebRTCError' has no member 'peerConnectionNotFound'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:203:63 Type 'WebRTCError' has no member 'offerCreationFailed'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:211:31 Type 'WebRTCError' has no member 'peerConnectionNotFound'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:270:31 Type 'WebRTCError' has no member 'peerConnectionNotFound'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardWebRTCClient.swift:414:6 Invalid redeclaration of 'WebRTCError'

wbCamer - 4932
SwiftUI
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/AIDetectionOverlayView.swift:116 Invalid frame dimension (negative or non-finite).

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/AIDetectionOverlayView.swift:116 Invalid frame dimension (negative or non-finite).

wbCamer
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Business/SharedServices/NetworkBandwidthManager.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Business/SharedServices/NetworkBandwidthManager.swift:23:36 'NetworkQuality' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/PTZManager.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/PTZManager.swift:25:8 Invalid redeclaration of 'ZoomRequest'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/PTZManager.swift:49:34 'ZoomRequest' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/PTZManager.swift:222:31 'nil' requires a contextual type

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/PTZManager.swift:227:30 No exact matches in call to initializer 

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/PTZManager.swift:323:51 'ZoomRequest' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Core/Camera/PTZManager.swift:369:27 'nil' requires a contextual type

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardPTZManager.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardPTZManager.swift:22:37 'PTZMoveRequest' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardPTZManager.swift:23:42 'PTZDirectionRequest' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardPTZManager.swift:185:54 'PTZMoveRequest' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardPTZManager.swift:202:59 'PTZDirectionRequest' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardPTZManager.swift:230:16 Invalid redeclaration of 'PTZMoveRequest'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/CardSpecific/CardPTZManager.swift:237:16 Invalid redeclaration of 'PTZDirectionRequest'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift:28:6 Invalid redeclaration of 'NetworkQuality'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift:40:18 'NetworkQuality' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift:98:22 Cannot infer contextual base in reference to member 'unknown'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift:99:23 Cannot infer contextual base in reference to member 'unknown'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift:104:28 'nil' requires a contextual type

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift:183:61 'NetworkQuality' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift:378:66 'NetworkQuality' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/NetworkMonitor.swift:408:91 'NetworkQuality' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/OptimizedWebRTCClient.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/OptimizedWebRTCClient.swift:260:50 Cannot find type 'SignalingMessage' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/OptimizedWebRTCClient.swift:276:52 Cannot find type 'SignalingMessage' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/OptimizedWebRTCClient.swift:287:41 Cannot find type 'SignalingMessage' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/OptimizedWebRTCClient.swift:315:42 Cannot find type 'SignalingMessage' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/OptimizedWebRTCClient.swift:328:48 Cannot find type 'SignalingMessage' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/CardComponents/SharedVirtualJoysticks.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/CardComponents/SharedVirtualJoysticks.swift:289:16 'JoystickStyle' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/CardComponents/SharedVirtualJoysticks.swift:299:32 'JoystickStyle' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/CommonComponents.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/CommonComponents.swift:124:8 Invalid redeclaration of 'EmptyStateView'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/videoplayerview.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/videoplayerview.swift:205:47 Cannot find type 'WebRTCStreamerClient' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/VirtualJoystick.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/VirtualJoystick.swift:12:6 Invalid redeclaration of 'JoystickStyle'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/VirtualJoystick.swift:84:16 'JoystickStyle' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/VirtualJoystick.swift:117:8 Invalid redeclaration of 'VirtualJoystick'

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/VirtualJoystick.swift:121:16 'JoystickStyle' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/VirtualJoystick.swift:131:17 'JoystickStyle' is ambiguous for type lookup in this context

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/VirtualJoystick.swift:270:44 Cannot infer contextual base in reference to member 'ptz'

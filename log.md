wbCamer - 4932
SwiftUI
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/AIDetectionOverlayView.swift:116 Invalid frame dimension (negative or non-finite).

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/AIDetectionOverlayView.swift:116 Invalid frame dimension (negative or non-finite).

wbCamer
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/WebRTC/OptimizedWebRTCStreamerClient.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/WebRTC/OptimizedWebRTCStreamerClient.swift:172:19 Cannot find 'WebRTCStreamerError' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/WebRTC/OptimizedWebRTCStreamerClient.swift:178:19 Cannot find 'WebRTCStreamerError' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Data/Network/WebRTC/OptimizedWebRTCStreamerClient.swift:189:19 Cannot find 'WebRTCStreamerError' in scope

/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/videoplayerview.swift
/Users/<USER>/Desktop/APP/wbCamer/wbCamer/Presentation/Components/videoplayerview.swift:205:47 Cannot find type 'WebRTCStreamerClient' in scope

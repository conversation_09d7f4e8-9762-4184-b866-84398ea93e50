wbCamer(5885,0x1f6dc31c0) malloc: xzm: failed to initialize deferred reclamation buffer (46)
Error creating the CFMessagePort needed to communicate with PPT.
🔧 EagleServiceDiscovery singleton initialized
[LandscapeMainView] 🚀 Multi-camera card architecture initialized
🔍 EagleServiceDiscovery.startManualScan() called
[CameraCardManager] 🔍 Device discovery started
🔍 Service discovered: ZCAM_E2_0028_99._eagle._tcp.local.
📡 Found Eagle service: ZCAM_E2_0028_99 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM_E2_0028_99 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM_E2_2380_99._eagle._tcp.local.
📡 Found Eagle service: ZCAM_E2_2380_99 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM_E2_2380_99 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM-P2-R1-010433._eagle._tcp.local.
📡 Found Eagle service: ZCAM-P2-R1-010433 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM-P2-R1-010433 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM-P2-R1-010377._eagle._tcp.local.
📡 Found Eagle service: ZCAM-P2-R1-010377 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM-P2-R1-010377 of type _eagle._tcp in domain local.
🔍 EagleServiceDiscovery.startManualScan() called
🔍 Service discovered: ZCAM-P2-R1-010433._eagle._tcp.local.
📡 Found Eagle service: ZCAM-P2-R1-010433 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM-P2-R1-010433 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM_E2_0028_99._eagle._tcp.local.
📡 Found Eagle service: ZCAM_E2_0028_99 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM_E2_0028_99 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM_E2_2380_99._eagle._tcp.local.
📡 Found Eagle service: ZCAM_E2_2380_99 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM_E2_2380_99 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM-P2-R1-010377._eagle._tcp.local.
📡 Found Eagle service: ZCAM-P2-R1-010377 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM-P2-R1-010377 of type _eagle._tcp in domain local.
tcp_input [C2.1.1.1:1] flags=[R.] seq=0, ack=2576506035, win=0 state=SYN_SENT rcv_nxt=0, snd_una=2576506034
tcp_input [C7.1.1.1:1] flags=[R.] seq=0, ack=850310324, win=0 state=SYN_SENT rcv_nxt=0, snd_una=850310323
nw_endpoint_flow_failed_with_error [C2.1.1.1 fe80::1486:43ff:fe0e:2380%en0.80@en0 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], scoped, ipv4, ipv6, dns, uses wifi)] already failing, returning
nw_endpoint_flow_failed_with_error [C7.1.1.1 fe80::1486:43ff:fe0e:2380%en0.80@en0 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], scoped, ipv4, ipv6, dns, uses wifi)] already failing, returning
tcp_input [C1.1.1.1:1] flags=[R.] seq=0, ack=1262444591, win=0 state=SYN_SENT rcv_nxt=0, snd_una=1262444590
tcp_input [C6.1.1.1:1] flags=[R.] seq=0, ack=2742778613, win=0 state=SYN_SENT rcv_nxt=0, snd_una=2742778612
tcp_input [C8.1.1.1:1] flags=[R.] seq=0, ack=1358848595, win=0 state=SYN_SENT rcv_nxt=0, snd_una=1358848594
tcp_input [C4.1.1.1:1] flags=[R.] seq=0, ack=757066743, win=0 state=SYN_SENT rcv_nxt=0, snd_una=757066742
nw_endpoint_flow_failed_with_error [C1.1.1.1 fe80::58e7:73ff:fe25:402d%en0.80@en0 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], scoped, ipv4, ipv6, dns, uses wifi)] already failing, returning
nw_endpoint_flow_failed_with_error [C6.1.1.1 fe80::58e7:73ff:fe25:402d%en0.80@en0 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], scoped, ipv4, ipv6, dns, uses wifi)] already failing, returning
nw_endpoint_flow_failed_with_error [C8.1.1.1 2409:8900:24f0:878d:a841:1aff:fec5:67c9.80@en0 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], scoped, ipv4, ipv6, dns, uses wifi)] already failing, returning
nw_endpoint_flow_failed_with_error [C4.1.1.1 2409:8900:24f0:878d:a841:1aff:fec5:67c9.80@en0 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], scoped, ipv4, ipv6, dns, uses wifi)] already failing, returning
Successfully resolved endpoint for ZCAM_E2_2380_99
nw_resolver_start_query_timer_block_invoke [C4.1.1] Query fired: did not receive all answers in time for ZCAM-P2-R1-010377.local.:80@en0
nw_resolver_start_query_timer_block_invoke [C8.1.1] Query fired: did not receive all answers in time for ZCAM-P2-R1-010377.local.:80@en0
Successfully resolved endpoint for ZCAM_E2_2380_99
Extracting IP from endpoint for ZCAM_E2_2380_99: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM_E2_2380_99: **************:80
Unable to open mach-O at path: /Library/Caches/com.apple.xbs/Binaries/RenderBox/install/Root/System/Library/PrivateFrameworks/RenderBox.framework/default.metallib  Error:2
Extracting IP from endpoint for ZCAM_E2_2380_99: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM_E2_2380_99: **************:80
Starting initial device discovery scan...
🔍 EagleServiceDiscovery.startManualScan() called
Browser ready
Browser cancelled
Browser ready
🔍 Service discovered: ZCAM-P2-R1-010433._eagle._tcp.local.
📡 Found Eagle service: ZCAM-P2-R1-010433 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM-P2-R1-010433 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM_E2_0028_99._eagle._tcp.local.
📡 Found Eagle service: ZCAM_E2_0028_99 of type _eagle._tcp in domain local.
Successfully resolved Eagle device: ZCAM_E2_2380_99 -> **************
Attempting to resolve service: ZCAM_E2_0028_99 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM-P2-R1-010377._eagle._tcp.local.
📡 Found Eagle service: ZCAM-P2-R1-010377 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM-P2-R1-010377 of type _eagle._tcp in domain local.
🔍 Service discovered: ZCAM_E2_2380_99._eagle._tcp.local.
📡 Found Eagle service: ZCAM_E2_2380_99 of type _eagle._tcp in domain local.
Attempting to resolve service: ZCAM_E2_2380_99 of type _eagle._tcp in domain local.
Browser cancelled
Browser ready
tcp_input [C3.1.1.1:1] flags=[R.] seq=0, ack=3106403247, win=0 state=SYN_SENT rcv_nxt=0, snd_una=3106403246
tcp_input [C5.1.1.1:1] flags=[R.] seq=0, ack=2525587501, win=0 state=SYN_SENT rcv_nxt=0, snd_una=2525587500
Successfully resolved endpoint for ZCAM_E2_0028_99
Extracting IP from endpoint for ZCAM_E2_0028_99: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM_E2_0028_99: **************:80
Successfully resolved endpoint for ZCAM_E2_0028_99
Extracting IP from endpoint for ZCAM_E2_0028_99: **************%en0:80
Found hostPort endpoint with port 80
nw_resolver_start_query_timer_block_invoke [C3.1.1] Query fired: did not receive all answers in time for ZCAM-P2-R1-010433.local.:80@en0
✅ Successfully resolved IPv4 for ZCAM_E2_0028_99: **************:80
nw_resolver_start_query_timer_block_invoke [C5.1.1] Query fired: did not receive all answers in time for ZCAM-P2-R1-010433.local.:80@en0
Successfully resolved endpoint for ZCAM-P2-R1-010377
Extracting IP from endpoint for ZCAM-P2-R1-010377: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM-P2-R1-010377: **************:80
Successfully resolved endpoint for ZCAM-P2-R1-010377
tcp_input [C13.1.1.1:1] flags=[R.] seq=0, ack=918358633, win=0 state=SYN_SENT rcv_nxt=0, snd_una=918358632
Extracting IP from endpoint for ZCAM-P2-R1-010377: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM-P2-R1-010377: **************:80
nw_resolver_start_query_timer_block_invoke [C10.1.1] Query fired: did not receive all answers in time for ZCAM-P2-R1-010433.local.:80@en0
nw_resolver_start_query_timer_block_invoke [C12.1.1] Query fired: did not receive all answers in time for ZCAM-P2-R1-010377.local.:80@en0
[C2 ZCAM_E2_2380_99._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
[C4 ZCAM-P2-R1-010377._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
[C6 ZCAM_E2_0028_99._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
[C7 ZCAM_E2_2380_99._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
Successfully resolved endpoint for ZCAM-P2-R1-010433
Extracting IP from endpoint for ZCAM-P2-R1-010433: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM-P2-R1-010433: **************:80
Successfully resolved endpoint for ZCAM-P2-R1-010433
Extracting IP from endpoint for ZCAM-P2-R1-010433: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM-P2-R1-010433: **************:80
[C1 ZCAM_E2_0028_99._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
Successfully resolved endpoint for ZCAM_E2_0028_99
Extracting IP from endpoint for ZCAM_E2_0028_99: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM_E2_0028_99: **************:80
[C8 ZCAM-P2-R1-010377._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
Successfully resolved endpoint for ZCAM-P2-R1-010433
Extracting IP from endpoint for ZCAM-P2-R1-010433: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM-P2-R1-010433: **************:80
[C3 ZCAM-P2-R1-010433._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
Successfully resolved endpoint for ZCAM_E2_2380_99
Extracting IP from endpoint for ZCAM_E2_2380_99: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM_E2_2380_99: **************:80
Successfully resolved endpoint for ZCAM-P2-R1-010377
Extracting IP from endpoint for ZCAM-P2-R1-010377: **************%en0:80
Found hostPort endpoint with port 80
✅ Successfully resolved IPv4 for ZCAM-P2-R1-010377: **************:80
[C5 ZCAM-P2-R1-010433._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
[C11 ZCAM_E2_0028_99._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
Successfully resolved Eagle device: ZCAM_E2_0028_99 -> **************
[C10 ZCAM-P2-R1-010433._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
[C12 ZCAM-P2-R1-010377._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
[C13 ZCAM_E2_2380_99._eagle._tcp.local. tcp, attribution: developer] is already cancelled, ignoring cancel
Successfully resolved Eagle device: ZCAM-P2-R1-010377 -> **************
Successfully resolved Eagle device: ZCAM-P2-R1-010433 -> **************
Successfully resolved Eagle device: ZCAM_E2_2380_99 -> **************
Browser cancelled
[CameraCardManager] 🆕 Creating card for P2-R1 Camera at **************
[CardAPIClient] 888C5D69 initialized for http://**************
[CardWebSocketManager] 888C5D69 initialized for **************:81
[CardWebRTCClient] 888C5D69 initialized for **************:8000
[CardPTZManager] 888C5D69 initialized
[CameraCard] 🔗 Connecting to P2-R1 Camera at **************
<0x140191f40> Gesture: System gesture gate timed out.
[CardAPIClient] 888C5D69 connecting to http://**************
[CardWebSocketManager] 888C5D69 connecting to ws://**************:81
[CardWebRTCClient] 888C5D69 connecting to **************:8000
[CardWebRTCClient] 888C5D69 signaling state changed: RTCSignalingState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE gathering state: RTCIceGatheringState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
tcp_input [C16:2] flags=[R.] seq=0, ack=3584910580, win=0 state=SYN_SENT rcv_nxt=0, snd_una=3584910579
nw_endpoint_flow_failed_with_error [C16 **************:8000 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] already failing, returning
Connection 16: received failure notification
Connection 16: failed to connect 1:61, reason -1
Connection 16: encountered error(1:61)
nw_connection_copy_connected_local_endpoint_block_invoke [C16] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C16] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
Task <BF05AAE5-ED76-42CE-B632-4F7EADA95922>.<1> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
Task <BF05AAE5-ED76-42CE-B632-4F7EADA95922>.<1> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x30173f960 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <BF05AAE5-ED76-42CE-B632-4F7EADA95922>.<1>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <BF05AAE5-ED76-42CE-B632-4F7EADA95922>.<1>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardWebRTCClient] 888C5D69 failed to send offer: Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x30173f960 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <BF05AAE5-ED76-42CE-B632-4F7EADA95922>.<1>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <BF05AAE5-ED76-42CE-B632-4F7EADA95922>.<1>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardAPIClient] 888C5D69 connection test failed: keyNotFound(CodingKeys(stringValue: "firmware", intValue: nil), Swift.DecodingError.Context(codingPath: [], debugDescription: "No value associated with key CodingKeys(stringValue: \"firmware\", intValue: nil) (\"firmware\").", underlyingError: nil))
[CardAPIClient] 888C5D69 connection failed
[CardWebSocketManager] 888C5D69 WebSocket connected
[CardWebSocketManager] 888C5D69 received: {"what":"basicInfo","nickname":"P2-R1","wrsPort":80,"recording":false,"preroll":false,"duration":0,"
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":0,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[]}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":0,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[]}
[CameraCardManager] ❌ Card connection error: P2-R1 Camera
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CameraCard] ❌ Failed to connect P2-R1 Camera: connectionTimeout
[CameraCardManager] ❌ Failed to connect card P2-R1 Camera: connectionTimeout
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":0,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[]}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,83,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,83,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,82,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,82,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9149,"fDist":8120}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,82,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,81,0.9
[CameraCard] 🔗 Connecting to P2-R1 Camera at **************
[CardAPIClient] 888C5D69 connecting to http://**************
[CardWebRTCClient] 888C5D69 connecting to **************:8000
[CardWebSocketManager] 888C5D69 already connected or connecting
[CardWebRTCClient] 888C5D69 signaling state changed: RTCSignalingState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE gathering state: RTCIceGatheringState(rawValue: 1)
[CardAPIClient] 888C5D69 connection test failed: keyNotFound(CodingKeys(stringValue: "firmware", intValue: nil), Swift.DecodingError.Context(codingPath: [], debugDescription: "No value associated with key CodingKeys(stringValue: \"firmware\", intValue: nil) (\"firmware\").", underlyingError: nil))
[CardAPIClient] 888C5D69 connection failed
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
tcp_input [C17:2] flags=[R.] seq=0, ack=2119439712, win=0 state=SYN_SENT rcv_nxt=0, snd_una=2119439711
nw_endpoint_flow_failed_with_error [C17 **************:8000 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] already failing, returning
Connection 17: received failure notification
Connection 17: failed to connect 1:61, reason -1
Connection 17: encountered error(1:61)
nw_connection_copy_connected_local_endpoint_block_invoke [C17] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C17] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
Task <E58C5A44-638F-45ED-8C2A-EA3C601579C2>.<2> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
Task <E58C5A44-638F-45ED-8C2A-EA3C601579C2>.<2> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x301786100 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <E58C5A44-638F-45ED-8C2A-EA3C601579C2>.<2>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <E58C5A44-638F-45ED-8C2A-EA3C601579C2>.<2>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardWebRTCClient] 888C5D69 failed to send offer: Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x301786100 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <E58C5A44-638F-45ED-8C2A-EA3C601579C2>.<2>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <E58C5A44-638F-45ED-8C2A-EA3C601579C2>.<2>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CameraCardManager] ❌ Card connection error: P2-R1 Camera
[CameraCard] ❌ Failed to connect P2-R1 Camera: connectionTimeout
[CameraCardManager] ❌ Failed to connect card P2-R1 Camera: connectionTimeout
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,82,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10121}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,83,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,84,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,66,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,67,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,67,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,66,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,73,0.0
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,67,0.0
[CameraCard] 🔗 Connecting to P2-R1 Camera at **************
[CardAPIClient] 888C5D69 connecting to http://**************
[CardWebRTCClient] 888C5D69 connecting to **************:8000
[CardWebSocketManager] 888C5D69 already connected or connecting
[CardWebRTCClient] 888C5D69 signaling state changed: RTCSignalingState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE gathering state: RTCIceGatheringState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardAPIClient] 888C5D69 connection test failed: keyNotFound(CodingKeys(stringValue: "firmware", intValue: nil), Swift.DecodingError.Context(codingPath: [], debugDescription: "No value associated with key CodingKeys(stringValue: \"firmware\", intValue: nil) (\"firmware\").", underlyingError: nil))
[CardAPIClient] 888C5D69 connection failed
tcp_input [C18:2] flags=[R.] seq=0, ack=2414814300, win=0 state=SYN_SENT rcv_nxt=0, snd_una=2414814299
nw_endpoint_flow_failed_with_error [C18 **************:8000 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] already failing, returning
Connection 18: received failure notification
Connection 18: failed to connect 1:61, reason -1
Connection 18: encountered error(1:61)
nw_connection_copy_connected_local_endpoint_block_invoke [C18] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C18] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
Task <E9683AA2-3A37-48E8-897A-1924DA1CF7F6>.<3> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
Task <E9683AA2-3A37-48E8-897A-1924DA1CF7F6>.<3> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x301796610 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <E9683AA2-3A37-48E8-897A-1924DA1CF7F6>.<3>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <E9683AA2-3A37-48E8-897A-1924DA1CF7F6>.<3>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardWebRTCClient] 888C5D69 failed to send offer: Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x301796610 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <E9683AA2-3A37-48E8-897A-1924DA1CF7F6>.<3>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <E9683AA2-3A37-48E8-897A-1924DA1CF7F6>.<3>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CameraCardManager] ❌ Card connection error: P2-R1 Camera
[CameraCard] ❌ Failed to connect P2-R1 Camera: connectionTimeout
[CameraCardManager] ❌ Failed to connect card P2-R1 Camera: connectionTimeout
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.0
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.0
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9150,"fDist":8120}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.0
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.0
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10122}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.1
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.1
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,65,0.1
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.1
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.1
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,67,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9151,"fDist":4500}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10123}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,70,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,77,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,68,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"hb"}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,69,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,66,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,76,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,76,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,77,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,72,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,69,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9152,"fDist":4500}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10124}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9153,"fDist":4500}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.6
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,80,0.6
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10125}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.6
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.6
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.6
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,88,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9154,"fDist":4500}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,69,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10126}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1642,0,78,0.9
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,65,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,70,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,71,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,68,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,73,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,74,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9155,"fDist":4500}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,74,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,73,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,71,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10127}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,67,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,66,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,65,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,67,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9156,"fDist":3500}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,75,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10128}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,74,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,79,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,79,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,77,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"hb"}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,76,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,73,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,70,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,70,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,70,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,71,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9157,"fDist":3500}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,71,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,73,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,71,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10129}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,75,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,76,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,78,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,78,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,67,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,75,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,69,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9158,"fDist":4750}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10130}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,79,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,75,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.6
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,80,0.6
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":0,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[]}
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9159,"fDist":8750}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":0,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[]}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10131}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,79,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,79,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,77,0.5
[CameraCard] 🔗 Connecting to P2-R1 Camera at **************
[CardAPIClient] 888C5D69 connecting to http://**************
[CardWebSocketManager] 888C5D69 already connected or connecting
[CardWebRTCClient] 888C5D69 connecting to **************:8000
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":0,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[]}
[CardWebRTCClient] 888C5D69 signaling state changed: RTCSignalingState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE gathering state: RTCIceGatheringState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":0,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[]}
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
tcp_input [C19:2] flags=[R.] seq=0, ack=2083779798, win=0 state=SYN_SENT rcv_nxt=0, snd_una=2083779797
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
nw_endpoint_flow_failed_with_error [C19 **************:8000 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] already failing, returning
Connection 19: received failure notification
Connection 19: failed to connect 1:61, reason -1
Connection 19: encountered error(1:61)
nw_connection_copy_connected_local_endpoint_block_invoke [C19] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C19] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
Task <6A509F31-F954-4B73-B168-5AE06C4D1EFF>.<4> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
Task <6A509F31-F954-4B73-B168-5AE06C4D1EFF>.<4> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x3017cf420 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <6A509F31-F954-4B73-B168-5AE06C4D1EFF>.<4>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <6A509F31-F954-4B73-B168-5AE06C4D1EFF>.<4>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardWebRTCClient] 888C5D69 failed to send offer: Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x3017cf420 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <6A509F31-F954-4B73-B168-5AE06C4D1EFF>.<4>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <6A509F31-F954-4B73-B168-5AE06C4D1EFF>.<4>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardAPIClient] 888C5D69 connection test failed: keyNotFound(CodingKeys(stringValue: "firmware", intValue: nil), Swift.DecodingError.Context(codingPath: [], debugDescription: "No value associated with key CodingKeys(stringValue: \"firmware\", intValue: nil) (\"firmware\").", underlyingError: nil))
[CardAPIClient] 888C5D69 connection failed
[CameraCardManager] ❌ Card connection error: P2-R1 Camera
[CameraCard] ❌ Failed to connect P2-R1 Camera: connectionTimeout
[CameraCardManager] ❌ Failed to connect card P2-R1 Camera: connectionTimeout
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9160,"fDist":7500}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10132}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,80,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9161,"fDist":6870}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10133}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"hb"}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":4,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9162,"fDist":6870}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10134}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,80,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9163,"fDist":6870}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.2
[CameraCard] 🔗 Connecting to P2-R1 Camera at **************
[CardAPIClient] 888C5D69 connecting to http://**************
[CardWebSocketManager] 888C5D69 already connected or connecting
[CardWebRTCClient] 888C5D69 connecting to **************:8000
[CardWebRTCClient] 888C5D69 signaling state changed: RTCSignalingState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE gathering state: RTCIceGatheringState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardAPIClient] 888C5D69 connection test failed: keyNotFound(CodingKeys(stringValue: "firmware", intValue: nil), Swift.DecodingError.Context(codingPath: [], debugDescription: "No value associated with key CodingKeys(stringValue: \"firmware\", intValue: nil) (\"firmware\").", underlyingError: nil))
[CardAPIClient] 888C5D69 connection failed
tcp_input [C20:2] flags=[R.] seq=0, ack=1383150917, win=0 state=SYN_SENT rcv_nxt=0, snd_una=1383150916
nw_endpoint_flow_failed_with_error [C20 **************:8000 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] already failing, returning
Connection 20: received failure notification
Connection 20: failed to connect 1:61, reason -1
Connection 20: encountered error(1:61)
nw_connection_copy_connected_local_endpoint_block_invoke [C20] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C20] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
Task <ECB1DD8D-78A8-4F5A-8353-4A6FB67D27D4>.<5> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
Task <ECB1DD8D-78A8-4F5A-8353-4A6FB67D27D4>.<5> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x30179c360 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <ECB1DD8D-78A8-4F5A-8353-4A6FB67D27D4>.<5>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <ECB1DD8D-78A8-4F5A-8353-4A6FB67D27D4>.<5>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardWebRTCClient] 888C5D69 failed to send offer: Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x30179c360 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <ECB1DD8D-78A8-4F5A-8353-4A6FB67D27D4>.<5>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <ECB1DD8D-78A8-4F5A-8353-4A6FB67D27D4>.<5>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CameraCardManager] ❌ Card connection error: P2-R1 Camera
[CameraCard] ❌ Failed to connect P2-R1 Camera: connectionTimeout
[CameraCardManager] ❌ Failed to connect card P2-R1 Camera: connectionTimeout
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,65,0.2
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10135}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,65,0.2
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.2
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,88,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,71,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9164,"fDist":6870}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,65,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10136}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.3
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,87,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,80,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9165,"fDist":6870}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,86,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10137}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9166,"fDist":4750}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10138}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.5
[CameraCard] 🔗 Connecting to P2-R1 Camera at **************
[CardWebSocketManager] 888C5D69 already connected or connecting
[CardWebRTCClient] 888C5D69 connecting to **************:8000
[CardAPIClient] 888C5D69 connecting to http://**************
[CardWebRTCClient] 888C5D69 signaling state changed: RTCSignalingState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE gathering state: RTCIceGatheringState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.5
[CardAPIClient] 888C5D69 connection test failed: keyNotFound(CodingKeys(stringValue: "firmware", intValue: nil), Swift.DecodingError.Context(codingPath: [], debugDescription: "No value associated with key CodingKeys(stringValue: \"firmware\", intValue: nil) (\"firmware\").", underlyingError: nil))
[CardAPIClient] 888C5D69 connection failed
tcp_input [C21:2] flags=[R.] seq=0, ack=1391060060, win=0 state=SYN_SENT rcv_nxt=0, snd_una=1391060059
nw_endpoint_flow_failed_with_error [C21 **************:8000 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] already failing, returning
Connection 21: received failure notification
Connection 21: failed to connect 1:61, reason -1
Connection 21: encountered error(1:61)
nw_connection_copy_connected_local_endpoint_block_invoke [C21] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C21] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
Task <167468F3-EE44-4C8B-A4D4-415604C81AEE>.<6> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
Task <167468F3-EE44-4C8B-A4D4-415604C81AEE>.<6> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x3017dcea0 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <167468F3-EE44-4C8B-A4D4-415604C81AEE>.<6>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <167468F3-EE44-4C8B-A4D4-415604C81AEE>.<6>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardWebRTCClient] 888C5D69 failed to send offer: Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x3017dcea0 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <167468F3-EE44-4C8B-A4D4-415604C81AEE>.<6>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <167468F3-EE44-4C8B-A4D4-415604C81AEE>.<6>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CameraCardManager] ❌ Card connection error: P2-R1 Camera
[CameraCard] ❌ Failed to connect P2-R1 Camera: connectionTimeout
[CameraCardManager] ❌ Failed to connect card P2-R1 Camera: connectionTimeout
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"hb"}
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9167,"fDist":4250}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10139}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,85,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9168,"fDist":4250}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,80,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10140}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,79,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,84,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,65,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,65,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,66,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,66,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,66,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9169,"fDist":4250}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,66,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,66,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":1,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,67,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10141}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1647,0,66,0.8
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,73,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CameraCard] 🔗 Connecting to P2-R1 Camera at **************
[CardAPIClient] 888C5D69 connecting to http://**************
[CardWebSocketManager] 888C5D69 already connected or connecting
[CardWebRTCClient] 888C5D69 connecting to **************:8000
[CardWebRTCClient] 888C5D69 signaling state changed: RTCSignalingState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE gathering state: RTCIceGatheringState(rawValue: 1)
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,79,0.5
tcp_input [C22:2] flags=[R.] seq=0, ack=54926984, win=0 state=SYN_SENT rcv_nxt=0, snd_una=54926983
nw_endpoint_flow_failed_with_error [C22 **************:8000 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] already failing, returning
Connection 22: received failure notification
Connection 22: failed to connect 1:61, reason -1
Connection 22: encountered error(1:61)
nw_connection_copy_connected_local_endpoint_block_invoke [C22] Client called nw_connection_copy_connected_local_endpoint on unconnected nw_connection
nw_connection_copy_connected_remote_endpoint_block_invoke [C22] Client called nw_connection_copy_connected_remote_endpoint on unconnected nw_connection
Task <FB4E641E-191F-4681-9885-0D0194A45252>.<7> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
Task <FB4E641E-191F-4681-9885-0D0194A45252>.<7> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x301787c30 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <FB4E641E-191F-4681-9885-0D0194A45252>.<7>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <FB4E641E-191F-4681-9885-0D0194A45252>.<7>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardWebRTCClient] 888C5D69 failed to send offer: Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x301787c30 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <FB4E641E-191F-4681-9885-0D0194A45252>.<7>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <FB4E641E-191F-4681-9885-0D0194A45252>.<7>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://**************:8000/offer, NSErrorFailingURLKey=http://**************:8000/offer, _kCFStreamErrorDomainKey=1}
[CardAPIClient] 888C5D69 connection test failed: keyNotFound(CodingKeys(stringValue: "firmware", intValue: nil), Swift.DecodingError.Context(codingPath: [], debugDescription: "No value associated with key CodingKeys(stringValue: \"firmware\", intValue: nil) (\"firmware\").", underlyingError: nil))
[CardAPIClient] 888C5D69 connection failed
[CameraCardManager] ❌ Card connection error: P2-R1 Camera
[CameraCard] ❌ Failed to connect P2-R1 Camera: connectionTimeout
[CameraCardManager] ❌ Failed to connect card P2-R1 Camera: connectionTimeout
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,81,0.5
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebRTCClient] 888C5D69 ICE candidate generated
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":2,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.5
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,82,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,80,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":4,"value":9170,"fDist":8120}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,83,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,80,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"RecUpdateDur","index":2,"value":10142}
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,65,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,67,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,87,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,84,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,69,0.4
[CardWebSocketManager] 888C5D69 received: {"what":"AI","num":3,"pending":0,"cols":8,"sel":-1,"debug":0,"target_ids":[],"objs":[2,1635,0,71,0.4
Message from debugger: killed
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI检测消息测试脚本 - objs数组格式
用于测试修复后的P2-R1 AI检测消息解析功能

根据P2-R1 Web服务源码分析，AI检测消息格式为：
{
  "what": "AI",
  "pending": 0,
  "num": 目标数量,
  "cols": 8,
  "objs": [app, id, type, confidence, x, y, width, height, ...]
}

每个目标占用8个连续的数组元素，顺序为：
- app: 应用ID
- id: 目标ID
- type: 目标类型 (1=人脸, 2=人, 3=头部, 4=移动目标)
- confidence: 置信度 (0.0-1.0)
- x: 归一化X坐标 (0.0-1.0)
- y: 归一化Y坐标 (0.0-1.0)
- width: 归一化宽度 (0.0-1.0)
- height: 归一化高度 (0.0-1.0)
"""

import asyncio
import websockets
import json
import time

# WebSocket服务器地址
WS_URL = "ws://localhost:8080"

# 测试用的AI检测消息
test_messages = [
    {
        "description": "单个人脸检测",
        "message": {
            "what": "AI",
            "pending": 0,
            "num": 1,
            "cols": 8,
            "objs": [
                1,      # app
                101,    # id
                1,      # type (人脸)
                0.95,   # confidence
                0.3,    # x (归一化)
                0.2,    # y (归一化)
                0.15,   # width (归一化)
                0.25    # height (归一化)
            ]
        }
    },
    {
        "description": "两个目标检测 - 人脸和人",
        "message": {
            "what": "AI",
            "pending": 0,
            "num": 2,
            "cols": 8,
            "objs": [
                # 第一个目标：人脸
                1,      # app
                201,    # id
                1,      # type (人脸)
                0.92,   # confidence
                0.25,   # x
                0.15,   # y
                0.12,   # width
                0.20,   # height
                # 第二个目标：人
                1,      # app
                202,    # id
                2,      # type (人)
                0.88,   # confidence
                0.6,    # x
                0.3,    # y
                0.18,   # width
                0.35    # height
            ]
        }
    },
    {
        "description": "三个目标检测 - 人脸、人、头部",
        "message": {
            "what": "AI",
            "pending": 0,
            "num": 3,
            "cols": 8,
            "objs": [
                # 第一个目标：人脸
                1, 301, 1, 0.96, 0.2, 0.1, 0.1, 0.18,
                # 第二个目标：人
                1, 302, 2, 0.85, 0.5, 0.25, 0.2, 0.4,
                # 第三个目标：头部
                1, 303, 3, 0.78, 0.75, 0.15, 0.08, 0.12
            ]
        }
    },
    {
        "description": "无目标检测",
        "message": {
            "what": "AI",
            "pending": 0,
            "num": 0,
            "cols": 8,
            "objs": []
        }
    },
    {
        "description": "移动目标检测",
        "message": {
            "what": "AI",
            "pending": 0,
            "num": 1,
            "cols": 8,
            "objs": [
                1,      # app
                401,    # id
                4,      # type (移动目标)
                0.82,   # confidence
                0.45,   # x
                0.35,   # y
                0.25,   # width
                0.3     # height
            ]
        }
    }
]

async def send_test_message(websocket, test_case):
    """发送测试消息"""
    print(f"\n📤 发送测试: {test_case['description']}")
    message_json = json.dumps(test_case['message'])
    print(f"📋 消息内容: {message_json}")
    
    await websocket.send(message_json)
    print("✅ 消息已发送")

async def test_ai_detection():
    """测试AI检测消息"""
    try:
        print(f"🔗 连接到WebSocket服务器: {WS_URL}")
        async with websockets.connect(WS_URL) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送所有测试消息
            for i, test_case in enumerate(test_messages, 1):
                print(f"\n{'='*60}")
                print(f"🧪 测试用例 {i}/{len(test_messages)}")
                
                await send_test_message(websocket, test_case)
                
                # 等待一段时间让应用处理消息
                await asyncio.sleep(2)
            
            print(f"\n{'='*60}")
            print("🎉 所有测试消息发送完成")
            print("请检查应用日志以验证AI检测消息解析是否正常工作")
            
            # 保持连接一段时间
            await asyncio.sleep(5)
            
    except ConnectionRefusedError:
        print(f"❌ 无法连接到WebSocket服务器 {WS_URL}")
        print("请确保应用正在运行并监听WebSocket连接")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    print("🚀 启动AI检测消息测试 - objs数组格式")
    print("📝 测试P2-R1设备的AI检测消息解析功能")
    print(f"🎯 目标服务器: {WS_URL}")
    
    asyncio.run(test_ai_detection())
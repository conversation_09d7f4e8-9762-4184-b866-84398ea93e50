#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟P2-R1摄像机WebSocket服务器
用于测试AI检测消息解析功能

这个服务器模拟P2-R1摄像机的WebSocket行为：
1. 监听端口8080
2. 接受WebSocket连接
3. 发送心跳消息
4. 发送AI检测消息
"""

import asyncio
import websockets
import json
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 服务器配置
HOST = "localhost"
PORT = 8080
HEARTBEAT_INTERVAL = 11  # P2-R1心跳间隔11秒

# 测试用的AI检测消息
ai_detection_messages = [
    {
        "what": "AI",
        "pending": 0,
        "num": 1,
        "cols": 8,
        "objs": [
            1,      # app
            101,    # id
            1,      # type (人脸)
            0.95,   # confidence
            0.3,    # x (归一化)
            0.2,    # y (归一化)
            0.15,   # width (归一化)
            0.25    # height (归一化)
        ]
    },
    {
        "what": "AI",
        "pending": 0,
        "num": 2,
        "cols": 8,
        "objs": [
            # 第一个目标：人脸
            1, 201, 1, 0.92, 0.25, 0.15, 0.12, 0.20,
            # 第二个目标：人
            1, 202, 2, 0.88, 0.6, 0.3, 0.18, 0.35
        ]
    },
    {
        "what": "AI",
        "pending": 0,
        "num": 0,
        "cols": 8,
        "objs": []
    },
    {
        "what": "AI",
        "pending": 0,
        "num": 1,
        "cols": 8,
        "objs": [
            1, 301, 4, 0.82, 0.45, 0.35, 0.25, 0.3  # 移动目标
        ]
    }
]

# 心跳消息
heartbeat_message = {
    "what": "heartbeat",
    "timestamp": int(time.time())
}

class MockCameraServer:
    def __init__(self):
        self.connected_clients = set()
        self.ai_message_index = 0
        
    async def register_client(self, websocket):
        """注册新客户端"""
        self.connected_clients.add(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"📱 客户端已连接: {client_info} (总连接数: {len(self.connected_clients)})")
        
    async def unregister_client(self, websocket):
        """注销客户端"""
        self.connected_clients.discard(websocket)
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        logger.info(f"📱 客户端已断开: {client_info} (总连接数: {len(self.connected_clients)})")
        
    async def send_to_all_clients(self, message):
        """向所有客户端发送消息"""
        if not self.connected_clients:
            return
            
        message_json = json.dumps(message)
        disconnected_clients = set()
        
        for websocket in self.connected_clients:
            try:
                await websocket.send(message_json)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(websocket)
            except Exception as e:
                logger.error(f"发送消息失败: {e}")
                disconnected_clients.add(websocket)
                
        # 清理断开的连接
        for websocket in disconnected_clients:
            await self.unregister_client(websocket)
            
    async def heartbeat_task(self):
        """心跳任务"""
        while True:
            try:
                await asyncio.sleep(HEARTBEAT_INTERVAL)
                
                # 更新心跳时间戳
                heartbeat_message["timestamp"] = int(time.time())
                
                if self.connected_clients:
                    logger.info(f"💓 发送心跳消息到 {len(self.connected_clients)} 个客户端")
                    await self.send_to_all_clients(heartbeat_message)
                    
            except asyncio.CancelledError:
                logger.info("心跳任务已取消")
                break
            except Exception as e:
                logger.error(f"心跳任务错误: {e}")
                
    async def ai_detection_task(self):
        """AI检测消息任务"""
        await asyncio.sleep(5)  # 等待5秒后开始发送AI消息
        
        while True:
            try:
                if self.connected_clients:
                    # 循环发送不同的AI检测消息
                    message = ai_detection_messages[self.ai_message_index]
                    self.ai_message_index = (self.ai_message_index + 1) % len(ai_detection_messages)
                    
                    logger.info(f"🤖 发送AI检测消息: {message['num']} 个目标")
                    await self.send_to_all_clients(message)
                    
                await asyncio.sleep(8)  # 每8秒发送一次AI检测消息
                
            except asyncio.CancelledError:
                logger.info("AI检测任务已取消")
                break
            except Exception as e:
                logger.error(f"AI检测任务错误: {e}")
                
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        await self.register_client(websocket)
        
        try:
            # 发送欢迎消息
            welcome_message = {
                "what": "welcome",
                "message": "Connected to Mock P2-R1 Camera",
                "timestamp": int(time.time())
            }
            await websocket.send(json.dumps(welcome_message))
            
            # 监听客户端消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    logger.info(f"📨 收到客户端消息: {data}")
                    
                    # 这里可以根据需要处理客户端消息
                    # 例如响应特定的控制命令
                    
                except json.JSONDecodeError:
                    logger.warning(f"收到无效JSON消息: {message}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("客户端连接已关闭")
        except Exception as e:
            logger.error(f"处理客户端时发生错误: {e}")
        finally:
            await self.unregister_client(websocket)
            
    async def start_server(self):
        """启动服务器"""
        logger.info(f"🚀 启动模拟P2-R1摄像机服务器")
        logger.info(f"📡 监听地址: {HOST}:{PORT}")
        logger.info(f"💓 心跳间隔: {HEARTBEAT_INTERVAL}秒")
        
        # 启动心跳任务
        heartbeat_task = asyncio.create_task(self.heartbeat_task())
        
        # 启动AI检测任务
        ai_task = asyncio.create_task(self.ai_detection_task())
        
        try:
            # 启动WebSocket服务器
            async with websockets.serve(self.handle_client, HOST, PORT):
                logger.info("✅ 服务器启动成功，等待客户端连接...")
                logger.info("按 Ctrl+C 停止服务器")
                
                # 保持服务器运行
                await asyncio.Future()  # 永远等待
                
        except KeyboardInterrupt:
            logger.info("\n🛑 收到停止信号，正在关闭服务器...")
        except Exception as e:
            logger.error(f"服务器错误: {e}")
        finally:
            # 取消任务
            heartbeat_task.cancel()
            ai_task.cancel()
            
            try:
                await heartbeat_task
                await ai_task
            except asyncio.CancelledError:
                pass
                
            logger.info("🔚 服务器已关闭")

async def main():
    server = MockCameraServer()
    await server.start_server()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
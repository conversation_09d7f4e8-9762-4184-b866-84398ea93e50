# ZCam HTTP API 参考文档

本文档整理了ZCam摄像机支持的所有HTTP API，按功能分类，提供可直接在浏览器地址栏中使用的URL格式。

**更新说明：** 本文档基于官方GitHub仓库 `imaginevision/Z-Camera-Doc` 和P2-R1实际源码分析进行更新，主要包括：
- 网络连接说明（E2系列多种连接模式及IP地址）
- E2系列流媒体控制（详细流设置API、RTSP流访问）
- E2系列专用功能（ProRes录制支持、像素同步录制）
- P2-R1智能功能（自动构图、人脸识别、轨迹控制）
- P2-R1流媒体推送（RTMP、SRT、NDI协议支持）
- P2-R1高级图像处理（矩阵控制、色彩校正、伽马设置）
- P2-R1系统管理（SNMP、SNTP、FreeD协议、温度监控）

## 网络连接说明

- **WiFi模式：** 摄像机作为接入点(AP)，默认IP: `**********`
- **USB RNDIS模式：** 通过USB连接，默认IP: `***********`
- **以太网模式：** 可配置为DHCP或静态IP
- **路由器模式：** 摄像机连接到路由器，从路由器获取IP

## 基础信息和状态

### 摄像机信息
```
GET: http://***********/info
GET: http://***********/ctrl/mode
GET: http://***********/camera_status
GET: http://***********/commit_info
GET: http://***********/ctrl/mode?action=to_rec
GET: http://***********/ctrl/mode?action=to_pb
GET: http://***********/ctrl/mode?action=to_cap
```

### 摄像机发现和连接
```
# 摄像机作为接入点时的固定IP
# WiFi AP模式: **********
# USB RNDIS模式: ***********
# 注意：E2系列摄像机在WiFi模式下作为接入点，客户端需要直接连接
```

### 摄像机昵称
```
GET: http://***********/ctrl/nick_name
SET: http://***********/ctrl/nick_name?action=set&name=MyCam
```

### 会话管理
```
GET: http://***********/ctrl/session
GET: http://***********/ctrl/session?action=quit
```

## 通用控制接口

### 参数获取和设置
```
GET: http://***********/ctrl/get?k=iris
SET: http://***********/ctrl/set?iris=4
GET: http://***********/ctrl/getbatch?catalog=exposure
```

## 镜头控制

### 焦距
```
GET: http://***********/ctrl/get?k=lens_focal_length
```

### 变焦控制
```
GET: http://***********/ctrl/lens?action=zoomin&fspeed=5
GET: http://***********/ctrl/lens?action=zoomout&fspeed=5
GET: http://***********/ctrl/lens?action=zoomstop
GET: http://***********/ctrl/lens?action=z_status
GET: http://***********/ctrl/get?k=zoom_mode
SET: http://***********/ctrl/set?zoom_mode=optical
```

### 对焦控制
```
GET: http://***********/ctrl/lens?action=focusnear&fspeed=5
GET: http://***********/ctrl/lens?action=focusfar&fspeed=5
GET: http://***********/ctrl/lens?action=focusstop
GET: http://***********/ctrl/lens?action=f_status
```

### 自动对焦
```
GET: http://***********/ctrl/af
GET: http://***********/ctrl/get?k=af_speed
SET: http://***********/ctrl/set?af_speed=5
GET: http://***********/ctrl/get?k=focus
SET: http://***********/ctrl/set?focus=AF
GET: http://***********/ctrl/get?k=af_mode
SET: http://***********/ctrl/set?af_mode=center
GET: http://***********/ctrl/af?action=update_roi_center&x=100&y=100
GET: http://***********/ctrl/af?action=query
GET: http://***********/ctrl/get?k=caf
SET: http://***********/ctrl/set?caf=on
GET: http://***********/ctrl/get?k=af_area
SET: http://***********/ctrl/set?af_area=large
GET: http://***********/ctrl/get?k=caf_sens
SET: http://***********/ctrl/set?caf_sens=normal
GET: http://***********/ctrl/get?k=af_adjust_with_ptz
SET: http://***********/ctrl/set?af_adjust_with_ptz=on
GET: http://***********/ctrl/af_face/trace_target?id=1
GET: http://***********/ctrl/get?k=live_caf
SET: http://***********/ctrl/set?live_caf=on
GET: http://***********/ctrl/get?k=mf_mag
SET: http://***********/ctrl/set?mf_mag=on
GET: http://***********/ctrl/get?k=mf_recording
SET: http://***********/ctrl/set?mf_recording=on
```

## 云台控制

### 云台查询和移动
```
GET: http://***********/ctrl/pt?action=query
GET: http://***********/ctrl/pt?action=query&detail=y
GET: http://***********/ctrl/pt?action=stop
GET: http://***********/ctrl/pt?action=stop_all
GET: http://***********/ctrl/pt?action=up&fspeed=5
GET: http://***********/ctrl/pt?action=down&fspeed=5
GET: http://***********/ctrl/pt?action=left&fspeed=5
GET: http://***********/ctrl/pt?action=right&fspeed=5
GET: http://***********/ctrl/pt?action=pt&pan_speed=5&tilt_speed=3
GET: http://***********/ctrl/pt?action=home
GET: http://***********/ctrl/pt?action=reset
GET: http://***********/ctrl/pt?action=limit&direct=up&pan_pos=180&tilt_pos=90
```

### 云台设置
```
GET: http://***********/ctrl/get?k=ptz_limit
SET: http://***********/ctrl/set?ptz_limit=on
GET: http://***********/ctrl/get?k=pt_speedmode
SET: http://***********/ctrl/set?pt_speedmode=normal
GET: http://***********/ctrl/get?k=pt_speed_with_zoom_pos
SET: http://***********/ctrl/set?pt_speed_with_zoom_pos=on
GET: http://***********/ctrl/get?k=ptz_flip
SET: http://***********/ctrl/set?ptz_flip=on
GET: http://***********/ctrl/get?k=pt_priv_mode
SET: http://***********/ctrl/set?pt_priv_mode=on
GET: http://***********/ctrl/get?k=pt_pwr_pos
SET: http://***********/ctrl/set?pt_pwr_pos=home
```

## 预置位控制

### 预置位操作
```
GET: http://***********/ctrl/preset?action=recall&index=1
GET: http://***********/ctrl/preset?action=set&index=1
GET: http://***********/ctrl/preset?action=del&index=1
GET: http://***********/ctrl/preset?action=get_info&index=1
GET: http://***********/ctrl/preset?action=set_name&index=1&new_name=Position1
GET: http://***********/ctrl/preset?action=preset_speed&index=1&preset_speed_unit=time
GET: http://***********/ctrl/preset?action=preset_speed&index=1&preset_time=5
GET: http://***********/ctrl/preset?action=preset_speed&index=1&preset_speed=10
```

### 预置位设置
```
GET: http://***********/ctrl/get?k=ptz_common_speed
SET: http://***********/ctrl/set?ptz_common_speed=10
GET: http://***********/ctrl/get?k=ptz_common_time
SET: http://***********/ctrl/set?ptz_common_time=5
GET: http://***********/ctrl/get?k=ptz_preset_mode
SET: http://***********/ctrl/set?ptz_preset_mode=speed
GET: http://***********/ctrl/get?k=ptz_speed_mode
SET: http://***********/ctrl/set?ptz_speed_mode=normal
GET: http://***********/ctrl/get?k=freeze_during_preset
SET: http://***********/ctrl/set?freeze_during_preset=on
GET: http://***********/ctrl/get?k=ptz_common_speed_unit
SET: http://***********/ctrl/set?ptz_common_speed_unit=time
```

## 轨迹控制

### 轨迹操作
```
GET: http://***********/ctrl/ptrace?action=del&index=1
GET: http://***********/ctrl/ptrace?action=set_name&index=1&new_name=Track1
GET: http://***********/ctrl/ptrace?action=rec_start&index=1
GET: http://***********/ctrl/ptrace?action=rec_stop
GET: http://***********/ctrl/ptrace?action=play_prepare&index=1
GET: http://***********/ctrl/ptrace?action=play_start
GET: http://***********/ctrl/ptrace?action=play_stop
GET: http://***********/ctrl/ptrace?action=query
GET: http://***********/ctrl/ptrace?action=get_info&index=1
```

## 用户管理

### 用户操作
```
GET: http://***********/login/user
GET: http://***********/login/adduser?user=admin&pswd=123456&grp=admin
GET: http://***********/login/deluser?user=testuser
GET: http://***********/login/pswd?user=admin&old=123456&new=newpass
GET: http://***********/login/quit
```

## 录制控制

### 录制操作
```
GET: http://***********/ctrl/rec?action=start
GET: http://***********/ctrl/rec?action=stop
GET: http://***********/ctrl/rec?action=query
GET: http://***********/ctrl/rec?action=remain
GET: http://***********/ctrl/rec?action=query_repairing
```

### 时间码控制
```
GET: http://***********/ctrl/tc?action=query
GET: http://***********/ctrl/tc?action=reset
GET: http://***********/ctrl/tc?action=current_time
GET: http://***********/ctrl/tc?action=set&tc=01:00:00:00
```

### 录制元数据
```
GET: http://***********/ctrl/rec?action=meta&key=title&value=MyVideo
GET: http://***********/ctrl/rec?action=meta&key=description&value=Test Recording
```

### 录制格式和分辨率
```
GET: http://***********/ctrl/get?k=resolution
SET: http://***********/ctrl/set?resolution=4096x2160
GET: http://***********/ctrl/get?k=project_fps
SET: http://***********/ctrl/set?project_fps=24
GET: http://***********/ctrl/get?k=vfr_ctrl
SET: http://***********/ctrl/set?vfr_ctrl=on
GET: http://***********/ctrl/get?k=mov_vfr
SET: http://***********/ctrl/set?mov_vfr=120
GET: http://***********/ctrl/get?k=file_format
SET: http://***********/ctrl/set?file_format=mov
GET: http://***********/ctrl/get?k=file_rotation
SET: http://***********/ctrl/set?file_rotation=0
GET: http://***********/ctrl/get?k=split_duration
SET: http://***********/ctrl/set?split_duration=0
GET: http://***********/ctrl/get?k=rec_fps
SET: http://***********/ctrl/set?rec_fps=24
GET: http://***********/ctrl/get?k=raw_over_hdmi
SET: http://***********/ctrl/set?raw_over_hdmi=off
GET: http://***********/ctrl/get?k=rec_frame_indicator
SET: http://***********/ctrl/set?rec_frame_indicator=on
GET: http://***********/ctrl/get?k=preroll
SET: http://***********/ctrl/set?preroll=off
```

### 其他录制控制
```
GET: http://***********/ctrl/get?k=record_meta
SET: http://***********/ctrl/set?record_meta=on
GET: http://***********/ctrl/get?k=record_mode
SET: http://***********/ctrl/set?record_mode=normal
```

### E2系列专用录制功能
```
# ProRes录制支持 (E2系列)
GET: http://***********/ctrl/get?k=codec
SET: http://***********/ctrl/set?codec=prores_422
SET: http://***********/ctrl/set?codec=prores_422_hq
SET: http://***********/ctrl/set?codec=prores_422_lt
SET: http://***********/ctrl/set?codec=prores_422_proxy

# 像素同步录制
GET: http://***********/ctrl/get?k=pixel_sync
SET: http://***********/ctrl/set?pixel_sync=on
GET: http://***********/ctrl/get?k=camera_id
SET: http://***********/ctrl/set?camera_id=CAM01
GET: http://***********/ctrl/get?k=reelname
SET: http://***********/ctrl/set?reelname=REEL001
```

### 录制参数
```
GET: http://***********/ctrl/get?k=resolution
SET: http://***********/ctrl/set?resolution=4K_UHD
GET: http://***********/ctrl/get?k=project_fps
SET: http://***********/ctrl/set?project_fps=25
GET: http://***********/ctrl/get?k=vfr_ctrl
SET: http://***********/ctrl/set?vfr_ctrl=on
GET: http://***********/ctrl/get?k=movvfr
SET: http://***********/ctrl/set?movvfr=on
GET: http://***********/ctrl/get?k=record_file_format
SET: http://***********/ctrl/set?record_file_format=MOV
GET: http://***********/ctrl/get?k=rotation
SET: http://***********/ctrl/set?rotation=0
GET: http://***********/ctrl/get?k=split_duration
SET: http://***********/ctrl/set?split_duration=30
GET: http://***********/ctrl/get?k=rec_fps
SET: http://***********/ctrl/set?rec_fps=25
GET: http://***********/ctrl/get?k=raw_over_hdmi
SET: http://***********/ctrl/set?raw_over_hdmi=on
GET: http://***********/ctrl/get?k=rec_frame_indicator
SET: http://***********/ctrl/set?rec_frame_indicator=on
```

### 预录制
```
GET: http://***********/ctrl/get?k=preroll
SET: http://***********/ctrl/set?preroll=on
GET: http://***********/ctrl/get?k=preroll_duration
SET: http://***********/ctrl/set?preroll_duration=5
```

## 拍照控制

```
GET: http://***********/ctrl/still?action=cap
```

## 回放控制

```
GET: http://***********/ctrl/pb?action=query
```

## 同步控制

### EZLink
```
GET: http://***********/ctrl/get?k=ezlink_mode
SET: http://***********/ctrl/set?ezlink_mode=master
GET: http://***********/ctrl/get?k=ezlink_trigger
SET: http://***********/ctrl/set?ezlink_trigger=rec
```

### Genlock
```
GET: http://***********/ctrl/get?k=genlock
SET: http://***********/ctrl/set?genlock=on
GET: http://***********/ctrl/get?k=genlock_coarse
SET: http://***********/ctrl/set?genlock_coarse=0
GET: http://***********/ctrl/get?k=genlock_fine
SET: http://***********/ctrl/set?genlock_fine=0
```

## 视频设置

### 视频编码
```
GET: http://***********/ctrl/get?k=video_encoder
SET: http://***********/ctrl/set?video_encoder=H264
GET: http://***********/ctrl/get?k=bitrate_level
SET: http://***********/ctrl/set?bitrate_level=high
GET: http://***********/ctrl/get?k=compose_mode
SET: http://***********/ctrl/set?compose_mode=normal
GET: http://***********/ctrl/get?k=photo_q
SET: http://***********/ctrl/set?photo_q=fine
```

### 视频处理
```
GET: http://***********/ctrl/get?k=eis_on_off
SET: http://***********/ctrl/set?eis_on_off=on
GET: http://***********/ctrl/get?k=vid_rot
SET: http://***********/ctrl/set?vid_rot=0
GET: http://***********/ctrl/get?k=record_mode
SET: http://***********/ctrl/set?record_mode=normal
GET: http://***********/ctrl/get?k=video_tl_interval
SET: http://***********/ctrl/set?video_tl_interval=1
GET: http://***********/ctrl/get?k=low_jello
SET: http://***********/ctrl/set?low_jello=on
```

## 音频设置

### 音频编码和输入
```
GET: http://***********/ctrl/get?k=primary_audio
SET: http://***********/ctrl/set?primary_audio=PCM
GET: http://***********/ctrl/get?k=audio_channel
SET: http://***********/ctrl/set?audio_channel=stereo
GET: http://***********/ctrl/get?k=audio_phantom_power
SET: http://***********/ctrl/set?audio_phantom_power=on
GET: http://***********/ctrl/get?k=audio_level_display
SET: http://***********/ctrl/set?audio_level_display=on
```

### 音频增益
```
GET: http://***********/ctrl/get?k=ain_gain_type
SET: http://***********/ctrl/set?ain_gain_type=manual
GET: http://***********/ctrl/get?k=audio_input_level
SET: http://***********/ctrl/set?audio_input_level=0
GET: http://***********/ctrl/get?k=audio_input_gain
SET: http://***********/ctrl/set?audio_input_gain=0
GET: http://***********/ctrl/get?k=audio_in_l_gain
SET: http://***********/ctrl/set?audio_in_l_gain=0
GET: http://***********/ctrl/get?k=audio_in_r_gain
SET: http://***********/ctrl/set?audio_in_r_gain=0
GET: http://***********/ctrl/get?k=audio_output_gain
SET: http://***********/ctrl/set?audio_output_gain=0
GET: http://***********/ctrl/get?k=audio_noise_reduction
SET: http://***********/ctrl/set?audio_noise_reduction=on
```

## 配置文件

```
GET: http://***********/prf/export
```

## 安全设置

### HTTPS设置
```
GET: http://***********/ctrl/get?k=https_on
SET: http://***********/ctrl/set?https_on=on
GET: http://***********/ctrl/get?k=https_auth
SET: http://***********/ctrl/set?https_auth=on
```

### 证书管理
```
GET: http://***********/ctrl/get?k=https_cert_source
SET: http://***********/ctrl/set?https_cert_source=internal
GET: http://***********/cert/gen
GET: http://***********/cert/info
GET: http://***********/cert/delete
```

## 流媒体控制

### RTMP流
```
GET: http://***********/ctrl/rtmp?action=query&index=0
GET: http://***********/ctrl/rtmp?action=start&url=rtmp://server/live&key=streamkey
GET: http://***********/ctrl/rtmp?action=stop&index=0
```

### SRT流
```
GET: http://***********/ctrl/srt?action=query
GET: http://***********/ctrl/srt?action=start&url=srt://*************:9999
GET: http://***********/ctrl/srt?action=stop
GET: http://***********/ctrl/srt?action=set&mode=caller&passphrase=password&pbkeyLen=16&latency=120&ttl=64
```

### NDI流
```
GET: http://***********/ctrl/ndi?action=query
GET: http://***********/ctrl/ndi?action=set&machine=ZCam&stream=Stream1&group=public&ttl=1&multicastEnable=false&discoveryServer1=&discoveryServer2=&bridgeName=&bridgeEncryptionKey=&bridgeServer=&bridgePort=5990
```

### RTSP流
```
GET: http://***********/ctrl/rtsp?action=query
GET: http://***********/ctrl/rtsp?action=set&authentication=none
```

### 流设置
```
GET: http://***********/ctrl/stream_setting?action=query&index=stream0
GET: http://***********/ctrl/stream_setting?index=stream0&bitrate=8000
GET: http://***********/ctrl/stream_setting?action=stream_performance&index=stream0
GET: http://***********/ctrl/get?k=stream_resolution
SET: http://***********/ctrl/set?stream_resolution=1080p
GET: http://***********/ctrl/get?k=stream_fps
SET: http://***********/ctrl/set?stream_fps=30
GET: http://***********/ctrl/get?k=stream_video_encoder
SET: http://***********/ctrl/set?stream_video_encoder=H264
GET: http://***********/ctrl/get?k=stream_param_save
SET: http://***********/ctrl/set?stream_param_save=on
```

### E2系列流媒体控制 (基于官方文档更新)
```
# 流设置 - stream0为录制流，stream1为网络流
GET: http://***********/ctrl/stream_setting?index=stream1&bitwidth=8&enc=h265&width=1920&height=1080&fps=25&bitrate=30000000
GET: http://***********/ctrl/stream_setting?index=stream0&bitwidth=10&enc=h264&width=3840&height=2160&fps=30&bitrate=50000000

# RTSP流访问
RTSP: rtsp://***********/live_stream

# 流参数设置（需分别设置）
GET: http://***********/ctrl/stream_setting?index=stream1&bitwidth=8
GET: http://***********/ctrl/stream_setting?index=stream1&enc=h265
GET: http://***********/ctrl/stream_setting?index=stream1&width=1920
GET: http://***********/ctrl/stream_setting?index=stream1&height=1080
GET: http://***********/ctrl/stream_setting?index=stream1&fps=25
GET: http://***********/ctrl/stream_setting?index=stream1&bitrate=30000000
```

## 辅助工具

### 显示辅助
```
GET: http://***********/ctrl/get?k=assitool_display
SET: http://***********/ctrl/set?assitool_display=on
GET: http://***********/ctrl/get?k=assitool_scope
SET: http://***********/ctrl/set?assitool_scope=waveform
GET: http://***********/ctrl/get?k=assitool_peak_onoff
SET: http://***********/ctrl/set?assitool_peak_onoff=on
GET: http://***********/ctrl/get?k=assitool_peak_color
SET: http://***********/ctrl/set?assitool_peak_color=red
```

### 框线和标记
```
GET: http://***********/ctrl/get?k=assitool_frame_line
SET: http://***********/ctrl/set?assitool_frame_line=on
GET: http://***********/ctrl/get?k=assitool_frame_line_color
SET: http://***********/ctrl/set?assitool_frame_line_color=white
GET: http://***********/ctrl/get?k=assitool_frame_line_opacity
SET: http://***********/ctrl/set?assitool_frame_line_opacity=50
GET: http://***********/ctrl/get?k=assitool_center_mark
SET: http://***********/ctrl/set?assitool_center_mark=on
GET: http://***********/ctrl/get?k=assitool_center_mark_color
SET: http://***********/ctrl/set?assitool_center_mark_color=white
```

### 曝光辅助
```
GET: http://***********/ctrl/get?k=assitool_exposure
SET: http://***********/ctrl/set?assitool_exposure=zebra
GET: http://***********/ctrl/get?k=assitool_zera_th1
SET: http://***********/ctrl/set?assitool_zera_th1=70
GET: http://***********/ctrl/get?k=assitool_zera_th2
SET: http://***********/ctrl/set?assitool_zera_th2=100
```

### 网格和安全区
```
GET: http://***********/ctrl/get?k=assitool_grid_line
SET: http://***********/ctrl/set?assitool_grid_line=on
GET: http://***********/ctrl/get?k=assitool_safe_area
SET: http://***********/ctrl/set?assitool_safe_area=on
GET: http://***********/ctrl/get?k=hdmi_focus_area
SET: http://***********/ctrl/set?hdmi_focus_area=on
```

## 时间码设置

```
GET: http://***********/ctrl/get?k=tc_source
SET: http://***********/ctrl/set?tc_source=internal
GET: http://***********/ctrl/get?k=tc_count_up
SET: http://***********/ctrl/set?tc_count_up=rec_run
GET: http://***********/ctrl/get?k=tc_hdmi_dispaly
SET: http://***********/ctrl/set?tc_hdmi_dispaly=on
GET: http://***********/ctrl/get?k=tc_drop_frame
SET: http://***********/ctrl/set?tc_drop_frame=off
```

## 多机位控制

```
GET: http://***********/ctrl/get?k=union_ae
SET: http://***********/ctrl/set?union_ae=on
GET: http://***********/ctrl/get?k=union_awb
SET: http://***********/ctrl/set?union_awb=on
GET: http://***********/ctrl/get?k=ezlink_mode
SET: http://***********/ctrl/set?ezlink_mode=master
GET: http://***********/ctrl/get?k=ezlink_trigger
SET: http://***********/ctrl/set?ezlink_trigger=rec
```

## 曝光控制

### 基础曝光参数
```
GET: http://***********/ctrl/get?k=ev
SET: http://***********/ctrl/set?ev=0
GET: http://***********/ctrl/get?k=flicker
SET: http://***********/ctrl/set?flicker=50hz
GET: http://***********/ctrl/get?k=meter_mode
SET: http://***********/ctrl/set?meter_mode=center
GET: http://***********/ctrl/get?k=iris
SET: http://***********/ctrl/set?iris=4
```

### ISO设置
```
GET: http://***********/ctrl/get?k=iso
SET: http://***********/ctrl/set?iso=800
GET: http://***********/ctrl/get?k=min_iso
SET: http://***********/ctrl/set?min_iso=100
GET: http://***********/ctrl/get?k=max_iso
SET: http://***********/ctrl/set?max_iso=6400
GET: http://***********/ctrl/get?k=iso_ctrl
SET: http://***********/ctrl/set?iso_ctrl=manual
```

### 快门控制
```
GET: http://***********/ctrl/get?k=shutter_angle_ctrl
SET: http://***********/ctrl/set?shutter_angle_ctrl=on
GET: http://***********/ctrl/get?k=sht_operation
SET: http://***********/ctrl/set?sht_operation=angle
GET: http://***********/ctrl/get?k=eND
SET: http://***********/ctrl/set?eND=on
```

### 自动曝光
```
GET: http://***********/ctrl/get?k=ae_speed
SET: http://***********/ctrl/set?ae_speed=normal
GET: http://***********/ctrl/get?k=lock_ae_in_rec
SET: http://***********/ctrl/set?lock_ae_in_rec=on
GET: http://***********/ctrl/get?k=bl_comp
SET: http://***********/ctrl/set?bl_comp=on
```

## 白平衡控制

### 白平衡模式
```
GET: http://***********/ctrl/get?k=wb
SET: http://***********/ctrl/set?wb=auto
GET: http://***********/ctrl/get?k=mwb
SET: http://***********/ctrl/set?mwb=5600
GET: http://***********/ctrl/get?k=tint
SET: http://***********/ctrl/set?tint=0
```

### 手动白平衡
```
GET: http://***********/ctrl/get?k=mwb_r
SET: http://***********/ctrl/set?mwb_r=1.0
GET: http://***********/ctrl/get?k=mwb_g
SET: http://***********/ctrl/set?mwb_g=1.0
GET: http://***********/ctrl/get?k=mwb_b
SET: http://***********/ctrl/set?mwb_b=1.0
```

### 自动白平衡
```
GET: http://***********/ctrl/get?k=wb_priority
SET: http://***********/ctrl/set?wb_priority=normal
GET: http://***********/ctrl/get?k=lock_awb_in_rec
SET: http://***********/ctrl/set?lock_awb_in_rec=on
GET: http://***********/ctrl/wb?action=one_push
```

## 图像处理

### 基础图像参数
```
GET: http://***********/ctrl/get?k=lut
SET: http://***********/ctrl/set?lut=rec709
GET: http://***********/ctrl/get?k=noise_reduction
SET: http://***********/ctrl/set?noise_reduction=on
GET: http://***********/ctrl/get?k=luma_level
SET: http://***********/ctrl/set?luma_level=0-255
GET: http://***********/ctrl/get?k=brightness
SET: http://***********/ctrl/set?brightness=0
GET: http://***********/ctrl/get?k=contrast
SET: http://***********/ctrl/set?contrast=0
GET: http://***********/ctrl/get?k=sharpness
SET: http://***********/ctrl/set?sharpness=0
GET: http://***********/ctrl/get?k=saturation
SET: http://***********/ctrl/set?saturation=0
GET: http://***********/ctrl/get?k=hue
SET: http://***********/ctrl/set?hue=0
```

### 矩阵控制
```
GET: http://***********/ctrl/matrix
GET: http://***********/ctrl/matrix?enable=on
GET: http://***********/ctrl/matrix?index=0&value=1.0
```

### 色彩校正
```
GET: http://***********/ctrl/cc
GET: http://***********/ctrl/cc?enable=on
GET: http://***********/ctrl/cc?action=set_hue&index=0&value=0
GET: http://***********/ctrl/cc?action=set_sat&index=0&value=0
```

### 伽马控制
```
GET: http://***********/ctrl/gamma?action=get&option=gamma
GET: http://***********/ctrl/gamma?action=set&option=gamma&base=0.45&power=1.0
GET: http://***********/ctrl/gamma?action=get&option=black_level
GET: http://***********/ctrl/gamma?action=set&option=black_level&enable=on&level=0
GET: http://***********/ctrl/gamma?action=get&option=black_gamma
GET: http://***********/ctrl/gamma?action=set&option=black_gamma&enable=on&range=0&level=0
GET: http://***********/ctrl/gamma?action=get&option=knee
GET: http://***********/ctrl/gamma?action=set&option=knee&enable=on&point=90&slope=0
```

## P2-R1智能构图功能

### 自动构图检测
```
# 启动自动构图检测
GET: http://***********/ctrl/framing/detect?enable=1

# 停止自动构图检测
GET: http://***********/ctrl/framing/detect?enable=0

# 查询检测状态
GET: http://***********/ctrl/framing/detect?action=query
```

### 构图跟踪控制
```
# 启动跟踪
GET: http://***********/ctrl/framing/trace?enable=1

# 停止跟踪
GET: http://***********/ctrl/framing/trace?enable=0

# 设置跟踪目标
GET: http://***********/ctrl/framing/trace_target?id=1

# 设置起始位置
GET: http://***********/ctrl/framing/start_position?index=1
```

### 视野设置
```
# 设置构图类型
GET: http://***********/ctrl/framing/fov?type=body
GET: http://***********/ctrl/framing/fov?type=face

# 设置身体尺寸
GET: http://***********/ctrl/framing/fov?body_size=small
GET: http://***********/ctrl/framing/fov?body_size=medium
GET: http://***********/ctrl/framing/fov?body_size=large

# 设置人脸高度
GET: http://***********/ctrl/framing/fov?face_height=20

# 设置位置
GET: http://***********/ctrl/framing/fov?placement=center
GET: http://***********/ctrl/framing/fov?placement=left
GET: http://***********/ctrl/framing/fov?placement=right

# 调试视野
GET: http://***********/ctrl/framing/debug_fov
```

### 自动控制
```
# 启动自动构图
GET: http://***********/ctrl/framing/auto_start?enable=on

# 重启构图
GET: http://***********/ctrl/framing/restart

# 自动目标选择
GET: http://***********/ctrl/framing/auto_target?enable=on
```

### 目标丢失处理
```
# 目标丢失时停止
GET: http://***********/ctrl/framing/target_lost?action=stop

# 设置超时时间
GET: http://***********/ctrl/framing/target_lost?timeout_sec=5

# 查询目标丢失状态
GET: http://***********/ctrl/framing/target_lost?action=query

# 检测重启时间
GET: http://***********/ctrl/framing/detect_restart?time=10
GET: http://***********/ctrl/framing/detect_restart?action=query

# 手动目标等待时间
GET: http://***********/ctrl/framing/manual_target?wait_time=3
GET: http://***********/ctrl/framing/manual_target?action=query
```

### 状态查询
```
# 查询构图状态
GET: http://***********/ctrl/framing/status

# 查询所有状态
GET: http://***********/ctrl/framing/all_status

# 查询构图信息
GET: http://***********/ctrl/framing/info

# 设置Tally灯
GET: http://***********/ctrl/framing/tally?enable=on
```

### 遮罩区域
```
# 启用遮罩区域
GET: http://***********/ctrl/framing/mask_area?enable=on

# 查询遮罩区域
GET: http://***********/ctrl/framing/mask_area

# 设置遮罩区域
GET: http://***********/ctrl/framing/mask_area?left=0&top=0&right=100&bottom=100
```

### 高级设置
```
# 跟踪活跃人脸
GET: http://***********/ctrl/framing/track_active_face?enable=on
GET: http://***********/ctrl/framing/track_active_face

# 设置目标数量
GET: http://***********/ctrl/framing/target_num?num=1
GET: http://***********/ctrl/framing/target_num

# 倾斜锁定
GET: http://***********/ctrl/framing/tilt_lock?enable=on
GET: http://***********/ctrl/framing/tilt_lock
```

## 28. P2-R1云台轨迹功能

### 轨迹录制
```
# 开始录制轨迹
GET http://***********00/ctrl/ptrace?action=record&id=TRACE_ID

# 停止录制轨迹
GET http://***********00/ctrl/ptrace?action=stop

# 暂停录制轨迹
GET http://***********00/ctrl/ptrace?action=pause

# 恢复录制轨迹
GET http://***********00/ctrl/ptrace?action=resume
```

### 轨迹播放
```
# 播放轨迹
GET http://***********00/ctrl/ptrace?action=play&id=TRACE_ID

# 停止播放轨迹
GET http://***********00/ctrl/ptrace?action=stop

# 暂停播放轨迹
GET http://***********00/ctrl/ptrace?action=pause

# 恢复播放轨迹
GET http://***********00/ctrl/ptrace?action=resume
```

### 轨迹管理
```
# 查询轨迹列表
GET http://***********00/ctrl/ptrace?action=list

# 删除轨迹
GET http://***********00/ctrl/ptrace?action=delete&id=TRACE_ID

# 清空所有轨迹
GET http://***********00/ctrl/ptrace?action=clear

# 查询轨迹状态
GET http://***********00/ctrl/ptrace?action=query
```

### 轨迹设置
```
# 设置轨迹播放速度
GET http://***********00/ctrl/ptrace?action=set&speed=SPEED_VALUE

# 设置轨迹循环播放
GET http://***********00/ctrl/ptrace?action=set&loop=on
GET http://***********00/ctrl/ptrace?action=set&loop=off

# 设置轨迹平滑度
GET http://***********00/ctrl/ptrace?action=set&smooth=SMOOTH_VALUE
```

## 29. P2-R1流媒体推送功能

### RTMP推流
```
# 启动RTMP推流
GET http://***********00/ctrl/rtmp?action=start&url=rtmp://server/live/stream

# 停止RTMP推流
GET http://***********00/ctrl/rtmp?action=stop

# 查询RTMP状态
GET http://***********00/ctrl/rtmp?action=query

# 设置RTMP参数
GET http://***********00/ctrl/rtmp?action=set&bitrate=BITRATE&resolution=RESOLUTION
```

### SRT推流
```
# 启动SRT推流
GET http://***********00/ctrl/srt?action=start&url=srt://server:port

# 停止SRT推流
GET http://***********00/ctrl/srt?action=stop

# 查询SRT状态
GET http://***********00/ctrl/srt?action=query

# 设置SRT参数
GET http://***********00/ctrl/srt?action=set&latency=LATENCY&mode=MODE
```

### NDI推流
```
# 启动NDI推流
GET http://***********00/ctrl/ndi?action=start&name=NDI_SOURCE_NAME

# 停止NDI推流
GET http://***********00/ctrl/ndi?action=stop

# 查询NDI状态
GET http://***********00/ctrl/ndi?action=query

# 设置NDI参数
GET http://***********00/ctrl/ndi?action=set&quality=QUALITY&audio=on
```

## 30. P2-R1高级图像处理

### 矩阵控制
```
# 获取矩阵设置
GET http://***********00/ctrl/get?k=matrix

# 设置矩阵模式
GET http://***********00/ctrl/set?k=matrix&v=MODE_VALUE

# 设置自定义矩阵
GET http://***********00/ctrl/matrix?action=set&r1=VALUE&r2=VALUE&r3=VALUE&g1=VALUE&g2=VALUE&g3=VALUE&b1=VALUE&b2=VALUE&b3=VALUE

# 重置矩阵
GET http://***********00/ctrl/matrix?action=reset
```

### 色彩校正
```
# 获取色彩校正设置
GET http://***********00/ctrl/get?k=cc

# 设置色彩校正模式
GET http://***********00/ctrl/set?k=cc&v=MODE_VALUE

# 设置色彩校正参数
GET http://***********00/ctrl/cc?action=set&lift_r=VALUE&lift_g=VALUE&lift_b=VALUE&gamma_r=VALUE&gamma_g=VALUE&gamma_b=VALUE&gain_r=VALUE&gain_g=VALUE&gain_b=VALUE

# 重置色彩校正
GET http://***********00/ctrl/cc?action=reset
```

### 伽马控制
```
# 获取伽马设置
GET http://***********00/ctrl/get?k=gamma

# 设置伽马曲线
GET http://***********00/ctrl/set?k=gamma&v=GAMMA_CURVE

# 设置自定义伽马
GET http://***********00/ctrl/gamma?action=set&curve=CUSTOM&points=POINT_VALUES

# 重置伽马
GET http://***********00/ctrl/gamma?action=reset
```

## 31. P2-R1系统管理功能

### SNMP协议支持
```
# 启用SNMP
GET http://***********00/ctrl/snmp?action=enable

# 禁用SNMP
GET http://***********00/ctrl/snmp?action=disable

# 查询SNMP状态
GET http://***********00/ctrl/snmp?action=query

# 设置SNMP社区字符串
GET http://***********00/ctrl/snmp?action=set&community=COMMUNITY_STRING
```

### SNTP时间同步
```
# 启用SNTP
GET http://***********00/ctrl/sntp?action=enable

# 禁用SNTP
GET http://***********00/ctrl/sntp?action=disable

# 设置SNTP服务器
GET http://***********00/ctrl/sntp?action=set&server=NTP_SERVER_ADDRESS

# 查询SNTP状态
GET http://***********00/ctrl/sntp?action=query

# 立即同步时间
GET http://***********00/ctrl/sntp?action=sync
```

### FreeD协议支持
```
# 启用FreeD
GET http://***********00/ctrl/freed?action=enable

# 禁用FreeD
GET http://***********00/ctrl/freed?action=disable

# 设置FreeD参数
GET http://***********00/ctrl/freed?action=set&ip=TARGET_IP&port=TARGET_PORT&camera_id=CAMERA_ID

# 查询FreeD状态
GET http://***********00/ctrl/freed?action=query
```

### 固件和许可证管理
```
# 查询固件版本
GET http://***********00/ctrl/get?k=fw_version

# 开始固件升级
GET http://***********00/ctrl/upgrade?action=start

# 查询升级状态
GET http://***********00/ctrl/upgrade?action=query

# 查询许可证状态
GET http://***********00/ctrl/license?action=query

# 激活许可证
GET http://***********00/ctrl/license?action=activate&key=LICENSE_KEY
```

## 人脸数据库

### 人脸管理
```
GET: http://***********/ctrl/facedb/query
GET: http://***********/ctrl/facedb/prepare?face_id=1
GET: http://***********/ctrl/facedb/add?which=1&name=Person1&priority=1&activate=on
GET: http://***********/ctrl/facedb/update?uid=1&name=Person1&priority=1&activate=on
GET: http://***********/ctrl/facedb/delete?uid=1
GET: http://***********/ctrl/facedb/set?enable=on
GET: http://***********/ctrl/facedb/status
```

## 固件升级

```
GET: http://***********/ctrl/upgrade?action=fw_check
GET: http://***********/ctrl/upgrade?action=run
GET: http://***********/ctrl/upgrade?action=ui_check
```

## 文件管理

```
GET: http://***********/DCIM/
GET: http://***********/DCIM/100ZCAM/
```

## 系统设置

### 系统控制
```
GET: http://***********/ctrl/set?action=clear
GET: http://***********/ctrl/temperature
GET: http://***********/ctrl/get?k=tally_on
SET: http://***********/ctrl/set?tally_on=50
GET: http://***********/ctrl/get?k=color_bar_enable
SET: http://***********/ctrl/set?color_bar_enable=on
GET: http://***********/ctrl/get?k=led
SET: http://***********/ctrl/set?led=on
GET: http://***********/ctrl/get?k=desqueeze
SET: http://***********/ctrl/set?desqueeze=on
```

### FreeD协议
```
GET: http://***********/ctrl/freed
GET: http://***********/ctrl/freed?camera_id=1
GET: http://***********/ctrl/freed?ip=*************&port=40000
GET: http://***********/ctrl/freed?enable=on
```

### VISCA协议
```
GET: http://***********/ctrl/get?k=visca_id
SET: http://***********/ctrl/set?visca_id=1
GET: http://***********/ctrl/get?k=visca_baudrate
SET: http://***********/ctrl/set?visca_baudrate=9600
GET: http://***********/ctrl/get?k=visca_enable
SET: http://***********/ctrl/set?visca_enable=on
```

### 时间设置
```
GET: http://***********/datetime/get
GET: http://***********/datetime?date=2024-01-01&time=12:00:00
GET: http://***********/datetime?timezone=UTC+8
```

### SNMP设置
```
GET: http://***********/ctrl/snmp?enable=on
GET: http://***********/ctrl/snmp?user=admin&auth=SHA&enc=AES&pass=password
GET: http://***********/ctrl/snmp?system=ZCam
GET: http://***********/ctrl/snmp?location=Studio
GET: http://***********/ctrl/snmp?contact=<EMAIL>
GET: http://***********/ctrl/snmp
```

### SNTP时间同步
```
GET: http://***********/ctrl/sntp?action=start&ip_addr=pool.ntp.org&port=123&interval=3600
GET: http://***********/ctrl/sntp?action=stop
GET: http://***********/ctrl/sntp?action=get
GET: http://***********/ctrl/sntp?action=set_interval&interval=3600
```

### 存储卡管理
```
GET: http://***********/ctrl/card?action=present
GET: http://***********/ctrl/card?action=query_format
GET: http://***********/ctrl/card?action=format
```

### 电源管理
```
GET: http://***********/ctrl/shutdown
GET: http://***********/ctrl/reboot
GET: http://***********/ctrl/mode?action=to_standby
GET: http://***********/ctrl/mode?action=exit_standby
GET: http://***********/ctrl/get?k=auto_off
SET: http://***********/ctrl/set?auto_off=30
GET: http://***********/ctrl/get?k=auto_standby
SET: http://***********/ctrl/set?auto_standby=10
```

## 连接设置

### USB设置
```
GET: http://***********/ctrl/get?k=usb_device_role
SET: http://***********/ctrl/set?usb_device_role=storage
GET: http://***********/ctrl/usb?action=query
```

### 红外遥控
```
GET: http://***********/ctrl/get?k=ir
SET: http://***********/ctrl/set?ir=on
GET: http://***********/ctrl/get?k=ir_id
SET: http://***********/ctrl/set?ir_id=1
```

### SDI输出
```
GET: http://***********/ctrl/get?k=sdi
SET: http://***********/ctrl/set?sdi=on
GET: http://***********/ctrl/get?k=3g_sdi_mode
SET: http://***********/ctrl/set?3g_sdi_mode=level_a
```

### HDMI输出
```
GET: http://***********/ctrl/get?k=hdmi_format
SET: http://***********/ctrl/set?hdmi_format=1080p60
GET: http://***********/ctrl/get?k=hdmi_osd
SET: http://***********/ctrl/set?hdmi_osd=on
GET: http://***********/ctrl/get?k=osd_layout
SET: http://***********/ctrl/set?osd_layout=standard
```

## 网络设置

### 以太网设置
```
GET: http://***********/ctrl/network?action=info
GET: http://***********/ctrl/get?k=eth_mode
GET: http://***********/ctrl/network?action=set&mode=static&ipaddr=*************&netmask=*************&gateway=***********&dns=*******
```

### WiFi设置
```
GET: http://***********/ctrl/wifi_ctrl?action=query
GET: http://***********/ctrl/get?k=wifi
SET: http://***********/ctrl/set?wifi=on
GET: http://***********/ctrl/get?k=wifi_channel
SET: http://***********/ctrl/set?wifi_channel=6
```

---

## 更新日志

### 基于官方GitHub仓库的更新 (2024)

根据 `https://github.com/imaginevision/Z-Camera-Doc` 仓库的最新信息，本文档已更新包含：

1. **网络连接说明** - 添加了E2系列的多种网络连接模式说明
2. **E2系列流媒体控制** - 基于官方文档添加了详细的流设置API
3. **RTSP流支持** - 添加了RTSP流访问地址格式
4. **流参数分离设置** - 说明了E2系列需要分别设置各个流参数的特性
5. **IP地址更新** - 更新了不同连接模式下的默认IP地址

### 基于P2-R1源码分析的新增功能 (2024)

基于P2-R1 Web服务源码分析，新增以下高级功能：

1. **P2-R1智能构图功能** - 自动构图检测、跟踪控制、视野设置
2. **P2-R1人脸识别功能** - 人脸数据库管理、人脸跟踪
3. **P2-R1云台轨迹功能** - 轨迹录制、播放、管理
4. **P2-R1流媒体推送** - RTMP、SRT、NDI协议支持
5. **P2-R1高级图像处理** - 矩阵控制、色彩校正、伽马设置
6. **P2-R1系统管理功能** - SNMP、SNTP、FreeD协议、固件管理

### 参考来源
- GitHub仓库：`imaginevision/Z-Camera-Doc`
- P2-R1 Web服务源码：`/Users/<USER>/Desktop/Sample Code/P2-R1/www`
- E2协议文档：`E2/protocol/http/`
- 社区讨论和实际使用反馈

---

## 说明

1. **IP地址设置**：
   - WiFi AP模式：`**********`
   - USB RNDIS模式：`***********`
   - 以太网/路由器模式：根据网络配置
   - 文档中的 `***********` 为示例，需替换为实际IP

2. **参数值**：示例值，实际使用时需要根据具体需求设置

3. **请求类型**：
   - GET请求用于获取参数值或执行操作
   - SET请求用于设置参数值

4. **权限要求**：某些操作可能需要特定的权限或登录状态

5. **使用建议**：建议在使用前先查询当前状态，确保参数设置的有效性

6. **E2系列特性**：
   - 支持RTSP直接流输出
   - 流参数需要分别设置，不能在一个URL中设置所有参数
   - stream0为录制流，stream1为网络流
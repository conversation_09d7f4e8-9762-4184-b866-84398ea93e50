# SwiftLint Configuration for wbCamer Project

# 包含的路径
included:
  - wbCamer

# 排除的路径
excluded:
  - Carthage
  - Pods
  - .build
  - DerivedData
  - wbCamer.xcodeproj
  - wbCamer/Preview Content

# 禁用的规则
disabled_rules:
  - trailing_whitespace
  - todo
  - line_length

# 可选规则（默认禁用，这里启用）
opt_in_rules:
  - empty_count
  - force_unwrapping
  - implicitly_unwrapped_optional
  - overridden_super_call
  - redundant_nil_coalescing
  - switch_case_on_newline
  - vertical_parameter_alignment_on_call
  - closure_spacing
  - operator_usage_whitespace
  - return_arrow_whitespace

# 规则配置
type_body_length:
  warning: 300
  error: 400

file_length:
  warning: 500
  error: 800

function_body_length:
  warning: 60
  error: 100

type_name:
  min_length: 3
  max_length:
    warning: 40
    error: 50

identifier_name:
  min_length: 2
  max_length:
    warning: 40
    error: 50
  excluded:
    - id
    - ip
    - x
    - y

# 自定义规则
custom_rules:
  # 强制使用 MARK 注释
  mark_comment:
    name: "MARK Comment"
    regex: '(class|struct|enum|extension|protocol)\s+\w+.*\{'
    message: "Use MARK: comments to organize your code"
    severity: warning

# 报告格式
reporter: "xcode"
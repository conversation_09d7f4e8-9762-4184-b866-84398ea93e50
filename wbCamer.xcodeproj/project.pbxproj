// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		FD625EB92E0E24E7003ECD8D /* WebRTC in Frameworks */ = {isa = PBXBuildFile; productRef = FD3825E42E0C239600813272 /* WebRTC */; };
		FD625EBA2E0E24E7003ECD8D /* Starscream in Frameworks */ = {isa = PBXBuildFile; productRef = FD3825E52E0C239600813272 /* Starscream */; };
		FD625EBB2E0E24E7003ECD8D /* Reachability in Frameworks */ = {isa = PBXBuildFile; productRef = FD3825E62E0C239600813272 /* Reachability */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		FD3825D82E0C239500813272 /* wbCamer.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = wbCamer.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		FD3825DC2E0C239500813272 /* Exceptions for "wbCamer" folder in "wbCamer" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = FD3825D72E0C239500813272 /* wbCamer */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		FD3825DA2E0C239500813272 /* wbCamer */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				FD3825DC2E0C239500813272 /* Exceptions for "wbCamer" folder in "wbCamer" target */,
			);
			path = wbCamer;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		FD3825D52E0C239500813272 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FD625EBB2E0E24E7003ECD8D /* Reachability in Frameworks */,
				FD625EBA2E0E24E7003ECD8D /* Starscream in Frameworks */,
				FD625EB92E0E24E7003ECD8D /* WebRTC in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		FD3825CF2E0C239500813272 = {
			isa = PBXGroup;
			children = (
				FD3825DA2E0C239500813272 /* wbCamer */,
				FD3825D92E0C239500813272 /* Products */,
			);
			sourceTree = "<group>";
		};
		FD3825D92E0C239500813272 /* Products */ = {
			isa = PBXGroup;
			children = (
				FD3825D82E0C239500813272 /* wbCamer.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		FD3825D72E0C239500813272 /* wbCamer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FD3825FC2E0C239800813272 /* Build configuration list for PBXNativeTarget "wbCamer" */;
			buildPhases = (
				FD3825D42E0C239500813272 /* Sources */,
				FD3825D52E0C239500813272 /* Frameworks */,
				FD3825D62E0C239500813272 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				FD3825DA2E0C239500813272 /* wbCamer */,
			);
			name = wbCamer;
			packageProductDependencies = (
				FD3825E42E0C239600813272 /* WebRTC */,
				FD3825E52E0C239600813272 /* Starscream */,
				FD3825E62E0C239600813272 /* Reachability */,
			);
			productName = wbCamer;
			productReference = FD3825D82E0C239500813272 /* wbCamer.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		FD3825D02E0C239500813272 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					FD3825D72E0C239500813272 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = FD3825D32E0C239500813272 /* Build configuration list for PBXProject "wbCamer" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = FD3825CF2E0C239500813272;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				FD3825E12E0C239600813272 /* XCRemoteSwiftPackageReference "WebRTC" */,
				FD3825E22E0C239600813272 /* XCRemoteSwiftPackageReference "Starscream" */,
				FD3825E32E0C239600813272 /* XCRemoteSwiftPackageReference "Reachability.swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = FD3825D92E0C239500813272 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				FD3825D72E0C239500813272 /* wbCamer */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		FD3825D62E0C239500813272 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		FD3825D42E0C239500813272 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		FD3825FA2E0C239800813272 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		FD3825FB2E0C239800813272 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FD3825FD2E0C239800813272 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"wbCamer/Preview Content\"";
				DEVELOPMENT_TEAM = SU9T5KDDC2;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = wbCamer/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = wbCamer;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.video";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wangbo.wbCamer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		FD3825FE2E0C239800813272 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"wbCamer/Preview Content\"";
				DEVELOPMENT_TEAM = SU9T5KDDC2;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = wbCamer/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = wbCamer;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.video";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wangbo.wbCamer;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		FD3825D32E0C239500813272 /* Build configuration list for PBXProject "wbCamer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD3825FA2E0C239800813272 /* Debug */,
				FD3825FB2E0C239800813272 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FD3825FC2E0C239800813272 /* Build configuration list for PBXNativeTarget "wbCamer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FD3825FD2E0C239800813272 /* Debug */,
				FD3825FE2E0C239800813272 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		FD3825E12E0C239600813272 /* XCRemoteSwiftPackageReference "WebRTC" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/stasel/WebRTC.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 118.0.0;
			};
		};
		FD3825E22E0C239600813272 /* XCRemoteSwiftPackageReference "Starscream" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/daltoniam/Starscream.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.0;
			};
		};
		FD3825E32E0C239600813272 /* XCRemoteSwiftPackageReference "Reachability.swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ashleymills/Reachability.swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		FD3825E42E0C239600813272 /* WebRTC */ = {
			isa = XCSwiftPackageProductDependency;
			package = FD3825E12E0C239600813272 /* XCRemoteSwiftPackageReference "WebRTC" */;
			productName = WebRTC;
		};
		FD3825E52E0C239600813272 /* Starscream */ = {
			isa = XCSwiftPackageProductDependency;
			package = FD3825E22E0C239600813272 /* XCRemoteSwiftPackageReference "Starscream" */;
			productName = Starscream;
		};
		FD3825E62E0C239600813272 /* Reachability */ = {
			isa = XCSwiftPackageProductDependency;
			package = FD3825E32E0C239600813272 /* XCRemoteSwiftPackageReference "Reachability.swift" */;
			productName = Reachability;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = FD3825D02E0C239500813272 /* Project object */;
}

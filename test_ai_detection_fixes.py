#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI检测修复验证脚本
用于测试AI检测框显示修复：
1. 水平位置偏左问题
2. 置信度超过100%问题  
3. 目标类型不显示问题
"""

import asyncio
import websockets
import json
import time
from typing import List, Dict, Any

def create_test_ai_detection_message(test_case: str) -> Dict[str, Any]:
    """创建不同测试用例的AI检测消息"""
    
    if test_case == "high_confidence":
        # 测试高置信度（百分比格式 0-100）
        return {
            "what": "AI",
            "pending": 0,
            "num": 1,
            "cols": 8,
            "objs": [
                0,      # app
                1,      # id
                3,      # type (face)
                95.5,   # confidence (百分比格式)
                0.3,    # x (归一化坐标)
                0.2,    # y
                0.15,   # width
                0.2     # height
            ]
        }
    
    elif test_case == "low_confidence":
        # 测试低置信度（小数格式 0-1）
        return {
            "what": "AI",
            "pending": 0,
            "num": 1,
            "cols": 8,
            "objs": [
                0,      # app
                2,      # id
                1,      # type (person)
                0.75,   # confidence (小数格式)
                0.6,    # x
                0.4,    # y
                0.2,    # width
                0.3     # height
            ]
        }
    
    elif test_case == "multiple_targets":
        # 测试多个目标，不同类型和置信度
        return {
            "what": "AI",
            "pending": 0,
            "num": 3,
            "cols": 8,
            "objs": [
                # 目标1: 人脸，高置信度（百分比）
                0, 1, 3, 98.2, 0.1, 0.1, 0.12, 0.15,
                # 目标2: 人，中等置信度（小数）
                0, 2, 1, 0.82, 0.5, 0.3, 0.18, 0.25,
                # 目标3: 头部，低置信度（百分比）
                0, 3, 2, 65.0, 0.8, 0.6, 0.1, 0.12
            ]
        }
    
    elif test_case == "edge_positions":
        # 测试边缘位置，验证坐标转换
        return {
            "what": "AI",
            "pending": 0,
            "num": 4,
            "cols": 8,
            "objs": [
                # 左上角
                0, 1, 3, 90.0, 0.05, 0.05, 0.1, 0.1,
                # 右上角
                0, 2, 3, 85.0, 0.85, 0.05, 0.1, 0.1,
                # 左下角
                0, 3, 3, 80.0, 0.05, 0.85, 0.1, 0.1,
                # 右下角
                0, 4, 3, 75.0, 0.85, 0.85, 0.1, 0.1
            ]
        }
    
    elif test_case == "center_target":
        # 测试中心位置目标
        return {
            "what": "AI",
            "pending": 0,
            "num": 1,
            "cols": 8,
            "objs": [
                0,      # app
                5,      # id
                3,      # type (face)
                92.8,   # confidence
                0.45,   # x (接近中心)
                0.4,    # y (接近中心)
                0.1,    # width
                0.15    # height
            ]
        }
    
    else:
        # 默认测试用例
        return {
            "what": "AI",
            "pending": 0,
            "num": 1,
            "cols": 8,
            "objs": [
                0, 1, 3, 88.5, 0.3, 0.25, 0.15, 0.2
            ]
        }

async def send_test_messages(websocket, path):
    """发送测试消息到连接的客户端"""
    print(f"📱 客户端已连接: {websocket.remote_address}")
    
    try:
        # 测试用例列表
        test_cases = [
            ("high_confidence", "高置信度测试（百分比格式）"),
            ("low_confidence", "低置信度测试（小数格式）"),
            ("center_target", "中心位置目标测试"),
            ("edge_positions", "边缘位置测试"),
            ("multiple_targets", "多目标测试")
        ]
        
        for test_case, description in test_cases:
            print(f"\n🧪 发送测试用例: {description}")
            
            # 创建测试消息
            message = create_test_ai_detection_message(test_case)
            message_json = json.dumps(message)
            
            print(f"📤 发送消息: {message_json}")
            
            # 发送消息
            await websocket.send(message_json)
            
            # 等待5秒让用户观察结果
            await asyncio.sleep(5)
        
        print("\n✅ 所有测试用例发送完成")
        print("💡 请在iOS应用中观察以下修复效果:")
        print("   1. 检测框位置是否正确（不再偏左）")
        print("   2. 置信度显示是否正常（不超过100%）")
        print("   3. 目标类型是否正确显示")
        
        # 保持连接，继续发送心跳
        while True:
            await asyncio.sleep(10)
            heartbeat = {"what": "heartbeat", "timestamp": int(time.time())}
            await websocket.send(json.dumps(heartbeat))
            print("💓 发送心跳")
            
    except websockets.exceptions.ConnectionClosed:
        print("📱 客户端断开连接")
    except Exception as e:
        print(f"❌ 发送消息时出错: {e}")

async def main():
    """启动WebSocket服务器"""
    print("🚀 启动AI检测修复验证服务器...")
    print("📍 监听地址: localhost:8081")
    print("🔧 测试内容: 检测框位置、置信度显示、目标类型显示")
    print("\n等待iOS应用连接...")
    print("💡 请在iOS应用中连接到: ws://localhost:8081")
    
    # 启动WebSocket服务器
    server = await websockets.serve(
        send_test_messages,
        "localhost",
        8081,
        ping_interval=30,
        ping_timeout=10
    )
    
    print("✅ 服务器启动成功，等待连接...")
    
    # 保持服务器运行
    await server.wait_closed()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
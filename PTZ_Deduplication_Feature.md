# PTZ 去重功能实现文档

## 概述

为了避免频繁的网络操作和减少不必要的HTTP API调用，我们为PTZ（Pan-Tilt-Zoom）功能添加了智能去重机制。该功能可以有效减少网络负载，提高系统性能，同时保持良好的用户体验。

## 功能特性

### 1. 去重策略

- **方向一致性检查**：如果PTZ操作的方向与上一次发送的操作方向一致
- **数值差异检查**：速度参数差异小于阈值（默认0.05）
- **时间间隔检查**：操作时间间隔短于阈值（默认100ms）
- **停止操作例外**：Stop操作始终允许重复发送，确保设备能够及时停止

### 2. 支持的PTZ操作类型

#### 单方向控制（Direction Move）
- 左移（Left）
- 右移（Right）
- 上移（Up）
- 下移（Down）
- 停止（Stop）- 允许重复

#### 双轴控制（PT Move）
- Pan速度控制
- Tilt速度控制
- 停止操作 - 允许重复

#### 变焦控制（Zoom）
- 放大（Zoom In）
- 缩小（Zoom Out）
- 停止变焦（Stop）- 允许重复
- 位置设置（Position）
- 状态查询（Status）

## 技术实现

### 数据结构

```swift
// PTZ方向请求记录
struct PTZDirectionRequest {
    let action: PTZControlRequest.PTZAction
    let fspeed: Float
    let timestamp: Date
}

// PTZ移动请求记录
struct PTZMoveRequest {
    let panSpeed: Float
    let tiltSpeed: Float
    let timestamp: Date
}

// 变焦请求记录
struct ZoomRequest {
    let action: ZoomControlRequest.ZoomAction
    let fspeed: Float
    let timestamp: Date
}
```

### 配置参数

```swift
// 时间阈值：100ms内的重复请求将被忽略
private let deduplicationTimeThreshold: TimeInterval = 0.1

// 速度差异阈值：速度差异小于0.05的请求将被忽略
private let speedDifferenceThreshold: Float = 0.05
```

### 去重逻辑

#### 1. PTZ方向控制去重
```swift
private func shouldSkipPTZDirectionRequest(_ request: PTZDirectionRequest) -> Bool {
    guard let lastRequest = lastPTZDirectionRequest else {
        return false
    }
    
    let timeDifference = request.timestamp.timeIntervalSince(lastRequest.timestamp)
    let speedDifference = abs(request.fspeed - lastRequest.fspeed)
    
    // 如果方向相同，时间间隔短，且速度差异小，则跳过
    return request.action == lastRequest.action &&
           timeDifference < deduplicationTimeThreshold &&
           speedDifference < speedDifferenceThreshold
}
```

#### 2. PTZ移动控制去重
```swift
private func shouldSkipPTZMoveRequest(_ request: PTZMoveRequest) -> Bool {
    guard let lastRequest = lastPTZMoveRequest else {
        return false
    }
    
    let timeDifference = request.timestamp.timeIntervalSince(lastRequest.timestamp)
    let panSpeedDifference = abs(request.panSpeed - lastRequest.panSpeed)
    let tiltSpeedDifference = abs(request.tiltSpeed - lastRequest.tiltSpeed)
    
    // 如果时间间隔短，且pan和tilt速度差异都很小，则跳过
    return timeDifference < deduplicationTimeThreshold &&
           panSpeedDifference < speedDifferenceThreshold &&
           tiltSpeedDifference < speedDifferenceThreshold
}
```

#### 3. 变焦控制去重
```swift
private func shouldSkipZoomRequest(_ request: ZoomRequest) -> Bool {
    guard let lastRequest = lastZoomRequest else {
        return false
    }
    
    let timeDifference = request.timestamp.timeIntervalSince(lastRequest.timestamp)
    let speedDifference = abs(request.fspeed - lastRequest.fspeed)
    
    // 如果动作相同，时间间隔短，且速度差异小，则跳过
    return isSameZoomAction(request.action, lastRequest.action) &&
           timeDifference < deduplicationTimeThreshold &&
           speedDifference < speedDifferenceThreshold
}
```

## 使用方法

### 1. 自动去重

去重功能已集成到PTZManager的所有公共方法中，无需额外配置：

```swift
// 单方向控制 - 自动去重
PTZManager.shared.executePTZAction(.left, fspeed: 0.5)

// 双轴控制 - 自动去重
PTZManager.shared.executePTZMove(panSpeed: 0.3, tiltSpeed: 0.2)

// 变焦控制 - 自动去重
PTZManager.shared.executeZoomAction(.zoomIn, fspeed: 0.5)

// 停止操作 - 始终执行，不会被去重
PTZManager.shared.stopMovement()
PTZManager.shared.stopZoom()
```

### 2. 便捷方法

```swift
// 摇杆控制
PTZManager.shared.moveWithJoystick(panSpeed: panSpeed, tiltSpeed: tiltSpeed)

// 方向控制
PTZManager.shared.moveToDirection(.up, fspeed: 0.5)

// 变焦控制
PTZManager.shared.zoomIn(fspeed: 0.5)
PTZManager.shared.zoomOut(fspeed: 0.5)
```

## 性能优化效果

### 1. 网络请求减少
- 在高频操作场景下（如摇杆控制），可减少60-80%的重复网络请求
- 降低网络带宽占用和服务器负载

### 2. 用户体验改善
- 减少网络延迟对操作响应的影响
- 避免因频繁请求导致的设备响应延迟
- 保持操作的流畅性

### 3. 系统稳定性
- 减少网络错误的可能性
- 降低设备端的处理负载
- 提高整体系统的稳定性

## 调试和监控

### 1. 日志输出

去重功能包含详细的日志输出，便于调试：

```swift
// 当请求被去重时的日志
print("[PTZManager] 🔄 PTZ Direction request skipped due to deduplication: \(action), fspeed: \(fspeed)")
```

### 2. 状态清理

在停止所有操作时，去重记录会被自动清理：

```swift
func stopAllActions() {
    // ... 停止操作 ...
    
    // 清除去重记录
    lastPTZDirectionRequest = nil
    lastPTZMoveRequest = nil
    lastZoomRequest = nil
}
```

## 注意事项

1. **停止操作优先级**：Stop操作始终会被执行，不受去重机制影响
2. **阈值调整**：可根据实际使用情况调整时间和速度差异阈值
3. **内存管理**：去重记录会在适当时机自动清理，避免内存泄漏
4. **线程安全**：所有操作都在主线程执行，确保线程安全

## 未来扩展

1. **可配置阈值**：支持运行时动态调整去重参数
2. **统计功能**：添加去重效果统计和性能监控
3. **自适应算法**：根据网络状况自动调整去重策略
4. **用户偏好**：允许用户自定义去重行为

---

*文档版本：1.0*  
*最后更新：2024年*  
*作者：AI Assistant*
# Swift错误处理最佳实践指南

## 本次修复的错误分析

### 错误描述
```
Initializer for conditional binding must have Optional type, not 'any Error'
```

### 错误原因
在 `UIWindowScene.requestGeometryUpdate` 的完成闭包中，错误地使用了可选绑定（`if let`）来处理非可选的 `Error` 参数。

```swift
// ❌ 错误的写法
windowScene.requestGeometryUpdate(geometryPreferences) { error in
    if let error = error {  // Error: error不是Optional类型
        print("Orientation update error: \(error.localizedDescription)")
    }
}

// ✅ 正确的写法
windowScene.requestGeometryUpdate(geometryPreferences) { error in
    // error参数是非可选的Error类型，直接使用即可
    print("Orientation update error: \(error.localizedDescription)")
}
```

## Swift错误处理最佳实践

### 1. 理解不同的错误处理模式

#### 可选错误参数（Optional Error）
```swift
// 常见于网络请求等可能成功或失败的操作
URLSession.shared.dataTask(with: url) { data, response, error in
    if let error = error {
        // 处理错误
        print("Network error: \(error.localizedDescription)")
        return
    }
    // 处理成功情况
}
```

#### 非可选错误参数（Non-Optional Error）
```swift
// 常见于总是会提供错误信息的回调
windowScene.requestGeometryUpdate(preferences) { error in
    // error总是存在，包含操作结果信息
    print("Update result: \(error.localizedDescription)")
}
```

#### Result类型
```swift
// 现代Swift推荐的错误处理方式
func performOperation(completion: @escaping (Result<Data, Error>) -> Void) {
    // 实现
}

performOperation { result in
    switch result {
    case .success(let data):
        // 处理成功
    case .failure(let error):
        // 处理错误
    }
}
```

### 2. 错误处理代码规范

#### 早期返回模式
```swift
// ✅ 推荐：早期返回，减少嵌套
func processData(_ data: Data?) {
    guard let data = data else {
        print("No data available")
        return
    }
    
    // 处理数据的主要逻辑
}

// ❌ 避免：深层嵌套
func processData(_ data: Data?) {
    if let data = data {
        // 深层嵌套的处理逻辑
    } else {
        print("No data available")
    }
}
```

#### 错误信息的本地化
```swift
// ✅ 提供用户友好的错误信息
enum AppError: LocalizedError {
    case networkUnavailable
    case invalidData
    
    var errorDescription: String? {
        switch self {
        case .networkUnavailable:
            return NSLocalizedString("网络连接不可用", comment: "Network error")
        case .invalidData:
            return NSLocalizedString("数据格式错误", comment: "Data error")
        }
    }
}
```

### 3. 异步错误处理

#### async/await模式
```swift
// ✅ 现代异步错误处理
func fetchData() async throws -> Data {
    let (data, _) = try await URLSession.shared.data(from: url)
    return data
}

// 使用
Task {
    do {
        let data = try await fetchData()
        // 处理数据
    } catch {
        // 处理错误
        print("Failed to fetch data: \(error.localizedDescription)")
    }
}
```

#### Combine错误处理
```swift
// ✅ Combine中的错误处理
publisher
    .tryMap { data in
        // 可能抛出错误的转换
        try JSONDecoder().decode(Model.self, from: data)
    }
    .catch { error in
        // 错误恢复
        Just(Model.default)
    }
    .sink { model in
        // 处理结果
    }
```

### 4. 调试和日志记录

#### 结构化错误日志
```swift
// ✅ 结构化的错误日志
struct ErrorLogger {
    static func log(_ error: Error, context: String, file: String = #file, line: Int = #line) {
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        print("[ERROR] \(context) - \(fileName):\(line) - \(error.localizedDescription)")
    }
}

// 使用
ErrorLogger.log(error, context: "方向更新失败")
```

#### 错误追踪
```swift
// ✅ 添加错误追踪信息
func updateOrientation() {
    guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
        ErrorLogger.log(AppError.windowSceneNotFound, context: "获取WindowScene失败")
        return
    }
    
    windowScene.requestGeometryUpdate(preferences) { error in
        // 记录操作结果，即使是"成功"的错误信息
        print("方向更新完成: \(error.localizedDescription)")
    }
}
```

## 避免常见错误的检查清单

### 编译时检查
- [ ] 确认错误参数的类型（Optional vs Non-Optional）
- [ ] 使用适当的错误处理语法（`if let` vs 直接访问）
- [ ] 检查异步操作的错误处理完整性

### 运行时检查
- [ ] 测试错误路径的执行
- [ ] 验证错误信息的可读性
- [ ] 确保错误不会导致应用崩溃

### 代码审查检查
- [ ] 错误处理逻辑的一致性
- [ ] 错误信息的本地化
- [ ] 日志记录的完整性

## 总结

通过理解Swift中不同的错误处理模式，我们可以：

1. **避免类型错误**：正确识别Optional和Non-Optional错误参数
2. **提高代码质量**：使用现代的错误处理模式（Result、async/await）
3. **改善用户体验**：提供清晰、本地化的错误信息
4. **简化调试**：实现结构化的错误日志和追踪

记住：好的错误处理不仅仅是让代码编译通过，更重要的是为用户和开发者提供有价值的信息。
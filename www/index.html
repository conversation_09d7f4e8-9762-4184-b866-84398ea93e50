<!doctype html>
<html lang="en" translate="no">
    <head>
        <meta charset="utf-8">
        <meta name="google" content="notranslate">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no">
        <link rel="icon" type="image/png" href="/www/assets/ico-white.png">
        <title>Z CAM Controller</title>
        <link rel="stylesheet" href="/www/css/styles.min.css">
        <link rel="stylesheet" href="/www/css/styles.min.css">
        <link rel="stylesheet" href="/www/css/styles.min.css">
        <script src="/www/js/jquery-3.7.1.min.js"></script>
        <script src="/www/js/jquery-ui.min.js"></script>
        <script src="/www/js/jquery.switchButton.js"></script>
        <script src="/www/js/controller.min.js"></script>
    </head>
    <body style="display:none" id="main_ui">
        <div id="main_tabs">
            <div>
                <ul id="top-tabs" class="unselectable top_layout">
                    <li>
                        <a href="#tabs-live" data-i18n="tab.live">Live</a>
                    </li>
                    <li>
                        <a href="#tabs-settings" data-i18n="tab.settings">Settings</a>
                    </li>
                    <li>
                        <a href="#tabs-gallery" data-i18n="tab.gallery">Gallery</a>
                    </li>
                </ul>
                <div id="left-top-ctrl" class="unselectable">
                    <div id="info_camera_name"></div>
                </div>
                <div id="right-top-ctrl" class="unselectable">
                    <div id="power_on_off">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 26 26" fill="currentColor">
                            <path id="power_svg" d="M327,1065a11.992,11.992,0,0,1-4-23.3v2.15a10,10,0,1,0,8-.01v-2.14A11.992,11.992,0,0,1,327,1065Zm0-12a1,1,0,0,1-1-1v-12a1,1,0,0,1,2,0v12A1,1,0,0,1,327,1053Z" transform="translate(-315 -1039)"/>
                        </svg>
                        <ul id="power_ctrl" style="display:none">
                            <li id="pow_to_standy" value="Standby" class="li_item" style="display:none" data-i18n="power.standby">Standby</li>
                            <li id="power_on" value="Power On" class="li_item" style="display:none" data-i18n="power.power_on">Power On</li>
                        </ul>
                    </div>
                    <div id="change_language">
                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 26 26" fill="currentColor">
                            <path class="cls-1" d="M221,1065a13,13,0,1,1,13-13A13,13,0,0,1,221,1065Zm2.986-12h-5.972a35.492,35.492,0,0,0,.316,4h5.34A35.492,35.492,0,0,0,223.986,1053Zm7.967,0h-5.977a31.081,31.081,0,0,1-.372,4h5.156A10.834,10.834,0,0,0,231.953,1053Zm-2.487,6H225.2a15.214,15.214,0,0,1-1.312,3.6A10.978,10.978,0,0,0,229.466,1059ZM221,1063c0.931,0,1.759-1.56,2.309-4h-4.618C219.241,1061.44,220.069,1063,221,1063Zm-2.886-.4a15.214,15.214,0,0,1-1.312-3.6h-4.268A10.978,10.978,0,0,0,218.114,1062.6Zm-6.874-5.6H216.4a31.081,31.081,0,0,1-.372-4h-5.977A10.834,10.834,0,0,0,211.24,1057Zm-0.029-10a10.936,10.936,0,0,0-1.15,4h5.979a31.363,31.363,0,0,1,.352-4h-5.181Zm1.322-2H216.8a15.34,15.34,0,0,1,1.317-3.6A10.984,10.984,0,0,0,212.533,1045Zm8.467-4c-0.932,0-1.764,1.56-2.314,4h4.628C222.764,1042.56,221.932,1041,221,1041Zm-2.67,6a36.167,36.167,0,0,0-.3,4h5.934a36.167,36.167,0,0,0-.3-4h-5.34Zm5.556-5.6a15.34,15.34,0,0,1,1.317,3.6h4.264A10.984,10.984,0,0,0,223.886,1041.4Zm6.9,5.6h-5.181a31.363,31.363,0,0,1,.352,4h5.979A10.936,10.936,0,0,0,230.789,1047Z" transform="translate(-208 -1039)"/>
                        </svg>
                        <ul id="language_list" style="display:none">
                            <li value="en" class="li_item">English</li>
                            <li value="zh" class="li_item">简体中文</li>
                        </ul>
                    </div>
                    <div id="lock_ctrl">
                        <svg id="lock_ctrl_ic" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="48px" height="48px" fill="currentColor" class="ic-lock-fill">
                            <path d="M 32 9 C 24.832 9 19 14.832 19 22 L 19 27.347656 C 16.670659 28.171862 15 30.388126 15 33 L 15 49 C 15 52.314 17.686 55 21 55 L 43 55 C 46.314 55 49 52.314 49 49 L 49 33 C 49 30.388126 47.329341 28.171862 45 27.347656 L 45 22 C 45 14.832 39.168 9 32 9 z M 32 13 C 36.963 13 41 17.038 41 22 L 41 27 L 23 27 L 23 22 C 23 17.038 27.037 13 32 13 z"/>
                        </svg>
                        <input id="lock_cb" type="checkbox" class="multi-switch" value="0">
                    </div>
                </div>
            </div>
            <div id="tabs-live" class="unselectable">
                <div id="preset_trace_settings" style="display:none" class="ui-corner-all ui-widget ui-widget-content">
                    <div id="preset_trace_tab">
                        <div id="ul_tabs_preset">
                            <ul>
                                <li>
                                    <a href="#tabs-preset" class="ui-tabs-anchor-custom">Preset</a>
                                </li>
                            </ul>
                        </div>
                        <div id="tabs-preset">
                            <div class="preset-box">
                                <div id="preset-index" class="preset-index">
                                    <div id="pt-ctrl-preset" class="pt-ctrl-preset"></div>
                                </div>
                                <div class="preset-crl-bottom">
                                    <div id="preset-page-index" class="preset-page-button"></div>
                                    <div id="preset-ctrl" style="display:none">
                                        <div id="pt-ctrl-store" class="pt-ctrl-store">
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <text x="50%" y="85%" text-anchor="middle" font-size="24">+</text>
                                            </svg>
                                        </div>
                                        <div id="pt-ctrl-recall" class="pt-ctrl-recall">
                                            <svg width="12" height="12" xmlns="http://www.w3.org/2000/svg">
                                                <polygon points="0,0 12,6 0,12" fill="currentColor"/>
                                            </svg>
                                        </div>
                                        <div id="pt-ctrl-stop" class="pt-ctrl-store">
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <rect x="50%" y="50%" width="12" height="12" transform="translate(-6, -6)" rx="2" ry="2"></rect>
                                            </svg>
                                        </div>
                                        <div id="pt-ctrl-record" class="pt-ctrl-record">
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <text id="trace_record" x="50%" y="85%" text-anchor="middle" font-size="24">+</text>
                                                <rect id="trace_stop" x="50%" y="50%" width="12" height="12" transform="translate(-6, -6)" rx="2" ry="2" style="display:none"></rect>
                                            </svg>
                                        </div>
                                        <button type="button" id="pt-ctrl-prepare" class="preset-ctrl-nbtn_trace">Prepare</button>
                                        <div id="pt-ctrl-delete" class="pt-ctrl-delete">
                                            <svg viewBox="0 0 1024 1024" fill="currentcolor" stroke="currentcolor" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z"/>
                                            </svg>
                                        </div>
                                        <div id="pt-ctrl-trace-delete" class="pt-ctrl-delete">
                                            <svg viewBox="0 0 1024 1024" fill="currentcolor" stroke="currentcolor" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z"/>
                                            </svg>
                                        </div>
                                    </div>
                                    <div id="pt_preset_tips" class="pt_preset_trace_tips" style="display:none"></div>
                                    <div id="pt_trace_tips" class="pt_preset_trace_tips" style="display:none"></div>
                                    <div class="preset_trace">
                                        <select name="preset_trace" id="preset_trace" class="segment_menu"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="preview">
                    <video id="stream" muted></video>
                    <p id="info_video_stream_status"></p>
                    <canvas id="canvas" width="1280" height="720">Your browser doesn't support canvas</canvas>
                </div>
                <div id="info_bar">
                    <p class="info" id="info_iris">F-</p>
                    <p class="info" id="info_sht">-</p>
                    <p class="info" id="info_iso">ISO -</p>
                    <p class="info" id="info_ev_bar">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 24 24" fill="currentcolor" xml:space="preserve">
                            <image id="image0" width="18" height="18" x="0" y="2" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAQAAAC1QeVaAAAAIGNIUk0AAHomAACAhAAA+gAAAIDo
                                        AAB1MAAA6mAAADqYAAAXcJy6UTwAAAACYktHRAD/h4/MvwAAAAd0SU1FB+gFFwoaCICMJCYAAAI8
                                        elRYdFJhdyBwcm9maWxlIHR5cGUgeG1wAAA4jZVVS5bcIAzc6xQ5AtYXjuNuzC7vzXKOnxKe/rs7
                                        iXlj0xKoSiXB0PfvL/qVT1QlOcuIGsUXFz+5hXJxdvPw5pt05m2cTqfBDHtzTYuFmHYp2qOoYG31
                                        RlpjDWw0iVU3U8cXAUWwiVmGbFzkHFXWqI6N3hPMFy7528++haSPEgFs1EfykHV3XJdPJrcwsJ1y
                                        h153cLGq3QpxkhsxTWK84a+DTxEWoEpIg20RwwgM5jOnr8CmPPKL9yJC3PFpsopirHAUfh78kx6D
                                        hctqrKr+lBrTdGZ6NRSjyIp0RsyHt8Aq3ibjmMgtx2TCeDPefQcAIzBGfVKRqEgLCOl/ZAEKKBUK
                                        wd6mUg0KYcXF7wtBsBEQNlntwt7XIgV+5TvBtluJMBfCtCOVCj4lyUPacqnVc0jt6J43Yekx7nFY
                                        BOwHwSXUsnWnxvR30sfBs7kDemHNyHAUMjVibLA9fkq9Y2d4TYENQTRrZwkw/PzSlAWMsCAXyZyV
                                        bFCVu6YrXG2eu9yGnhBuN6Cp2qrwUmaKVDIF06azevsipNXmMV0RcEHYJqzggRkexVFMu5pU2Iww
                                        MUXXoUma5tFAJ+TBSPcLcqYiYXkSs/PugWki6yHycoT8Dph25Mtl8YBus48d6PiBeVav3BV9TeUu
                                        0tC/agN8+8SQ3mpzj/xBm4s0dIDs/6PND/CgXR0en8/586r9TrpacYPS4028uw7+HVg25EyH95uc
                                        /gArd22wXiSafAAAAGNJREFUGNONkMENwCAMA88VA1RM0P2naydIHxACBaTmQ5RTYhuZsa0DfkB9
                                        ifabAkixJaAaOMuTqCM5eByB3G2DIW5NsyANvkZDeQydWle0gqqH1xzXz2buRdjqVjPC/Ozy+19B
                                        NxNnRUWilQAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyNC0wNS0yM1QxMDoyNjowNyswMDowMJ6ZSgMA
                                        AAAldEVYdGRhdGU6bW9kaWZ5ADIwMjQtMDUtMjNUMTA6MjY6MDcrMDA6MDDvxPK/AAAAKHRFWHRk
                                        YXRlOnRpbWVzdGFtcAAyMDI0LTA1LTIzVDEwOjI2OjA4KzAwOjAwTpmjiQAAABl0RVh0U29mdHdh
                                        cmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAAASUVORK5CYII="/>
                        </svg>
                        <span id="info_ev"></span>
                    </p>
                    <p class="info" id="info_wb">-K</p>
                    <p class="info" id="info_temp"></p>
                    <p class="info" id="info_bat"></p>
                </div>
                <div id="rec_tabs">
                    <div id="info_bar_bottom">
                        <p class="info" id="info_res"></p>
                        <p class="info" id="info_fps"></p>
                        <p class="info" id="info_vfr" style="display:none"></p>
                        <p class="info" id="info_vencoder"></p>
                        <p class="info" id="info_img_profile"></p>
                    </div>
                </div>
                <div id="shortcut_tools_bar">
                    <div id="preset_trace_btn" class="colored-svg-default" style="display:none">
                        <svg id="preset_trace_open" xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 24 26" style="display:none">
                            <path class="cls-1" d="M267,1065h-9v-2h8a3,3,0,0,0,3-3v-16a3,3,0,0,0-3-3h-8v-2h9a4,4,0,0,1,4,4v18A4,4,0,0,1,267,1065Zm-6-19,6,6-6,6v-5h-4v-2h4v-5Zm-8,19h-4a2,2,0,0,1-2-2v-22a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v22A2,2,0,0,1,253,1065Zm0-24h-4v22h4v-22Z" transform="translate(-247 -1039)"/>
                        </svg>
                        <svg id="preset_trace_close" xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 24 26" style="display:none">
                            <path class="cls-1" d="M306,1065h-9v-2h8a3,3,0,0,0,3-3v-16a3,3,0,0,0-3-3h-8v-2h9a4,4,0,0,1,4,4v18A4,4,0,0,1,306,1065Zm-4-19-6,6,6,6v-5h4v-2h-4v-5Zm-10,19h-4a2,2,0,0,1-2-2v-22a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v22A2,2,0,0,1,292,1065Zm0-24h-4v22h4v-22Z" transform="translate(-286 -1039)"/>
                        </svg>
                    </div>
                    <div id="auto_framing_status">
                        <svg id="center_framing" xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 26 26" class="colored-svg-default" style="display:none">
                            <path class="cls-1" d="M597,1065h-3v-2h2a3,3,0,0,0,3-3v-2h2v3A4,4,0,0,1,597,1065Zm2-21a3,3,0,0,0-3-3h-2v-2h3a4,4,0,0,1,4,4v3h-2v-2Zm-2,9h-4.1a5.007,5.007,0,0,1-3.9,3.9v4.1h-2v-4.1a5.007,5.007,0,0,1-3.9-3.9H579v-2h4.1a5.007,5.007,0,0,1,3.9-3.9V1043h2v4.1a5.007,5.007,0,0,1,3.9,3.9H597v2Zm-9-4a3,3,0,1,0,3,3A3,3,0,0,0,588,1049Zm-1,2h2v2h-2v-2Zm-10-7v2h-2v-3a4,4,0,0,1,4-4h3v2h-2A3,3,0,0,0,577,1044Zm0,16a3,3,0,0,0,3,3h2v2h-3a4,4,0,0,1-4-4v-3h2v2Z" transform="translate(-575 -1039)"/>
                        </svg>
                        <svg id="roi_framing" xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 26 26" class="colored-svg-default" style="display:none">
                            <path class="cls-1" d="M546,1065h-3v-2h2a3,3,0,0,0,3-3v-2h2v3A4,4,0,0,1,546,1065Zm2-21a3,3,0,0,0-3-3h-2v-2h3a4,4,0,0,1,4,4v3h-2v-2Zm-2,5v2h-2v-1a2,2,0,0,0-2-2h-1v-2h2A3,3,0,0,1,546,1049Zm-11,7h4v2h-4v-2Zm0-10h4v2h-4v-2Zm-4,12a3,3,0,0,1-3-3v-2h2v1a2,2,0,0,0,2,2h1v2h-2Zm-1-8v1h-2v-2a3,3,0,0,1,3-3h2v2h-1A2,2,0,0,0,530,1050Zm-4-6v2h-2v-3a4,4,0,0,1,4-4h3v2h-2A3,3,0,0,0,526,1044Zm0,16a3,3,0,0,0,3,3h2v2h-3a4,4,0,0,1-4-4v-3h2v2Zm18-6v-1h2v2a3,3,0,0,1-3,3h-2v-2h1A2,2,0,0,0,544,1054Z" transform="translate(-524 -1039)"/>
                        </svg>
                        <svg id="auto_framing" xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 26 26" class="colored-svg-default" style="display:none">
                            <path class="cls-1" d="M495,1065h-3v-2h2a3,3,0,0,0,3-3v-2h2v3A4,4,0,0,1,495,1065Zm2-21a3,3,0,0,0-3-3h-2v-2h3a4,4,0,0,1,4,4v3h-2v-2Zm-2.987,15.97h-2a6.01,6.01,0,0,0-12.019.01h0.121c-1.286,0-2.124,0-2.124-.01a7.99,7.99,0,0,1,5.616-7.61,5,5,0,1,1,4.794,0A7.99,7.99,0,0,1,494.013,1059.97ZM489,1048a3,3,0,1,0-3,3A3,3,0,0,0,489,1048Zm-14-4v2h-2v-3a4,4,0,0,1,4-4h3v2h-2A3,3,0,0,0,475,1044Zm0,16a3,3,0,0,0,3,3h2v2h-3a4,4,0,0,1-4-4v-3h2v2Z" transform="translate(-473 -1039)"/>
                        </svg>
                    </div>
                    <div id="info_assitool">
                        <div id="assitool_grid_btn" class="colored-svg-default">
                            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 32 25" class="svg_style">
                                <path id="assitool_grid_svg" data-name="assitool_grid_svg" class="cls-1" d="M1622,976h-24a4,4,0,0,1-4-4V955a4,4,0,0,1,4-4h24a4,4,0,0,1,4,4v17A4,4,0,0,1,1622,976Zm-16-2h8v-6h-8v6Zm0-8h8v-5h-8v5Zm-2-13h-6a2,2,0,0,0-2,2v4h8v-6Zm0,8h-8v5h8v-5Zm0,7h-8v4a2,2,0,0,0,2,2h6v-6Zm2-9h8v-6h-8v6Zm18-4a2,2,0,0,0-2-2h-6v6h8v-4Zm0,6h-8v5h8v-5Zm0,7h-8v6h6a2,2,0,0,0,2-2v-4Z" transform="translate(-1594 -951)"/>
                            </svg>
                        </div>
                        <div id="assitool_safe_center_btn" class="colored-svg-default">
                            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="18" viewBox="0 0 32 25" class="svg_style">
                                <path id="assitool_safe_center_svg" data-name="assitool_safe_center_svg" class="cls-1" d="M1702,975h-9v-2h9a2,2,0,0,0,2-2v-5h2v5A4,4,0,0,1,1702,975Zm2-21a2,2,0,0,0-2-2h-9v-2h9a4,4,0,0,1,4,4v5h-2v-5Zm-21.36,3a1.5,1.5,0,0,0-1.64,1.294V959h-2v-2a2,2,0,0,1,2-2h6v2h-4.36Zm-6.64-3v5h-2v-5a4,4,0,0,1,4-4h9v2h-9A2,2,0,0,0,1676,954Zm0,17a2,2,0,0,0,2,2h9v2h-9a4,4,0,0,1-4-4v-5h2v5Zm25-3a2,2,0,0,1-2,2h-6v-2h4.36a1.5,1.5,0,0,0,1.64-1.294V966h2v2Zm0-11v2h-2v-0.706a1.5,1.5,0,0,0-1.64-1.294H1693v-2h6A2,2,0,0,1,1701,957Zm-22,11v-2h2v0.706a1.5,1.5,0,0,0,1.64,1.294H1687v2h-6A2,2,0,0,1,1679,968Z" transform="translate(-1674 -950)"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div id="control">
                    <div id="control_tabs">
                        <ul id="control_tabs_ul">
                            <li>
                                <a href="#tabs-ctrl-project" class="ui-tabs-anchor-custom" data-i18n="tab.prj">Project</a>
                            </li>
                            <li>
                                <a href="#tabs-ctrl-exp" class="ui-tabs-anchor-custom" data-i18n="tab.exp">Exposure</a>
                            </li>
                            <li>
                                <a href="#tabs-ctrl-wb" class="ui-tabs-anchor-custom" data-i18n="tab.wb">White Balance</a>
                            </li>
                            <li>
                                <a href="#tabs-ctrl-af" class="ui-tabs-anchor-custom" data-i18n="tab.focus">Focus</a>
                            </li>
                            <li>
                                <a href="#tabs-ctrl-img" class="ui-tabs-anchor-custom" data-i18n="tab.img">Image</a>
                            </li>
                        </ul>
                        <div id="tabs-ctrl-project">
                            <label data-i18n="cfg.res">Resolution</label>
                            <select name="resolution" id="resolution" class="select_menu"></select>
                            <label data-i18n="cfg.prjfps">Project FPS</label>
                            <select name="project_fps" id="project_fps" class="select_menu"></select>
                            <label data-i18n="cfg.vfr">Variable Framerate</label>
                            <select name="vfr" id="vfr" class="select_menu"></select>
                        </div>
                        <div id="tabs-ctrl-exp">
                            <label data-i18n="cfg.iris">Aperture (F Number)</label>
                            <input id="exp_iris" name="exp_iris" class="option_menu">
                            <label data-i18n="cfg.sht">Shutter</label>
                            <div id="shutter-time">
                                <input id="exp_sht_time" name="exp_sht_time" class="option_menu">
                                <button id="shutter_time_auto" type="button" data-i18n="cfg.auto">Auto</button>
                            </div>
                            <div id="shutter-angle-coarse">
                                <input id="exp_sht_angle" name="exp_sht_angle" class="option_menu">
                                <button id="shutter_angle_coarse_auto" type="button" data-i18n="cfg.auto">Auto</button>
                            </div>
                            <div id="shutter-angle-fine">
                                <div>
                                    <input id="exp_m_shutter_angle" name="exp_m_shutter_angle" class="ui-spinner-input">
                                </div>
                                <button id="shutter_angle_fine_auto" type="button" data-i18n="cfg.auto">Auto</button>
                            </div>
                            <label>ISO</label>
                            <div id="exp_iso_input">
                                <input id="exp_iso" name="exp_iso" class="option_menu">
                                <button id="exp_iso_auto" type="button" data-i18n="cfg.auto">Auto</button>
                            </div>
                            <label data-i18n="cfg.ev">EV</label>
                            <input id="exp_ev" name="exp_ev" class="option_menu">
                            <div id="exp-exp_e_nd">
                                <label>eND</label>
                                <input name="exp_e_nd" id="exp_e_nd" class="option_menu">
                            </div>
                            <label data-i18n="cfg.lock_in_rec">Lock In Record</label>
                            <select name="lock_ae_in_rec" id="lock_ae_in_rec" class="segment_menu"></select>
                        </div>
                        <div id="tabs-ctrl-wb">
                            <label data-i18n="cfg.mode">Mode</label>
                            <div class="flex-center">
                                <select name="wb_mode" id="wb_mode" class="select_menu"></select>
                                <button type="button" id="one_push_wb_button" style="display:none" data-i18n="cfg.onepushwb">One Push WB</button>
                            </div>
                            <label data-i18n="cfg.priority">Priority</label>
                            <select name="awb_priority" id="awb_priority" class="segment_menu"></select>
                            <div id="wb_cct_info">
                                <label data-i18n="cfg.kelvin">Kelvin</label>
                                <div id="wb_kelvin" class="sliderSpinner"></div>
                                <label data-i18n="cfg.tint">Tint</label>
                                <div id="wb_tint" class="sliderSpinner"></div>
                            </div>
                            <div id="wb_exp_info">
                                <label id="r_gain_label" data-i18n="cfg.rgain">R Gain</label>
                                <div id="r_gain" name="r_gain" class="sliderSpinner"></div>
                                <label id="g_gain_label" data-i18n="cfg.ggain">G Gain</label>
                                <div id="g_gain" name="g_gain" class="sliderSpinner"></div>
                                <label id="b_gain_label" data-i18n="cfg.bgain">B Gain</label>
                                <div id="b_gain" name="b_gain" class="sliderSpinner"></div>
                            </div>
                            <label data-i18n="cfg.lock_in_rec">Lock In Record</label>
                            <select name="lock_awb_in_rec" id="lock_awb_in_rec" class="segment_menu"></select>
                        </div>
                        <div id="tabs-ctrl-af">
                            <label data-i18n="cfg.mode">Mode</label>
                            <div class="flex-center">
                                <select name="focus_mode" id="focus_mode" class="segment_menu"></select>
                                <button type="button" id="one_push_af_button" style="display:none" data-i18n="cfg.onepushaf">One Push AF</button>
                            </div>
                            <label data-i18n="cfg.roi">Area</label>
                            <select name="focus_area" id="focus_area" class="select_menu"></select>
                            <div id="focus_area_size_ctrl" style="display:none">
                                <label data-i18n="cfg.roi_size">Area Size</label>
                                <select name="focus_area_size" id="focus_area_size" class="segment_menu"></select>
                            </div>
                            <div id="focus_caf_enable_ctrl" style="display:none">
                                <label data-i18n="cfg.caf">Continuous AF</label>
                                <select name="focus_caf_enable" id="focus_caf_enable" class="segment_menu"></select>
                            </div>
                            <label data-i18n="cfg.sens">Sensitivity</label>
                            <select name="focus_caf_sens" id="focus_caf_sens" class="segment_menu"></select>
                        </div>
                        <div id="tabs-ctrl-img">
                            <div id="image_settings" style="display:none">
                                <div id="image_profile">
                                    <label data-i18n="cfg.iprof">Image Profile</label>
                                    <select name="img_lut" id="img_lut" class="select_menu"></select>
                                </div>
                                <div id="master_gamma" style="display:none">
                                    <label data-i18n="cfg.gamma">Gamma</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.type">Type</label>
                                    <select name="gamma_type" id="gamma_type" class="segment_menu"></select>
                                    <label data-i18n="cfg.gamma_pwr">Power</label>
                                    <div id="gamma_power" name="gamma_power" class="sliderSpinner"></div>
                                    <div>
                                        <br>
                                        <label data-i18n="cfg.black_lvl">Black Level</label>
                                        <hr class="camui-border-red">
                                        <label data-i18n="cfg.status">Status</label>
                                        <select name="black_level_status" id="black_level_status" class="segment_menu"></select>
                                        <label data-i18n="cfg.gamma_lvl">Level</label>
                                        <div id="black_level" name="black_level" class="sliderSpinner"></div>
                                    </div>
                                    <div>
                                        <br>
                                        <label data-i18n="cfg.black_gamma">Black Gamma</label>
                                        <hr class="camui-border-red">
                                        <label data-i18n="cfg.status">Status</label>
                                        <select name="black_gamma_status" id="black_gamma_status" class="segment_menu"></select>
                                        <label data-i18n="cfg.gamma_rng">Range</label>
                                        <select name="black_gamma_range" id="black_gamma_range" class="segment_menu"></select>
                                        <label data-i18n="cfg.gamma_lvl">Level</label>
                                        <div id="black_gamma_level" name="black_gamma_level" class="sliderSpinner"></div>
                                    </div>
                                    <div>
                                        <label data-i18n="cfg.knee">Knee</label>
                                        <hr class="camui-border-red">
                                        <label data-i18n="cfg.status">Status</label>
                                        <select name="knee_status" id="knee_status" class="segment_menu"></select>
                                        <label data-i18n="cfg.point">Point</label>
                                        <div id="knee_point" name="knee_point" class="sliderSpinner"></div>
                                        <label data-i18n="cfg.slope">Slope</label>
                                        <div id="knee_slope" name="knee_slope" class="sliderSpinner"></div>
                                        <hr id="gamma_red" class="camui-border-red">
                                    </div>
                                </div>
                                <div id="image_config">
                                    <label data-i18n="cfg.sharpness">Sharpness</label>
                                    <select name="img_shapness" id="img_shapness" class="segment_menu"></select>
                                    <label data-i18n="cfg.nr">Noise Reduction</label>
                                    <select name="img_nr" id="img_nr" class="segment_menu"></select>
                                    <label data-i18n="cfg.brightness">Brightness</label>
                                    <div id="img_brightness" class="sliderSpinner"></div>
                                    <label data-i18n="cfg.constrast">Contrast</label>
                                    <div id="img_contrast" class="sliderSpinner"></div>
                                    <label data-i18n="cfg.sat">Saturation</label>
                                    <div id="img_sat" class="sliderSpinner"></div>
                                    <label data-i18n="cfg.hue">Hue</label>
                                    <div id="img_hue" class="sliderSpinner"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="ptz-ctrl" class="ui-corner-all ui-widget ui-widget-content">
                        <div id="ptz_tabs">
                            <div id="ul_tabs_ptz">
                                <ul>
                                    <li>
                                        <a href="#tabs-ptz" class="ui-tabs-anchor-custom">PTZ</a>
                                    </li>
                                    <li id="pt_limit_status" class="ui-state-disabled" data-i18n="cfg.ptz_limited">Limited</li>
                                    <li id="pt_slow_status" class="ui-state-disabled" data-i18n="cfg.ptz_slow" style="display:none">Slow</li>
                                </ul>
                            </div>
                            <div id="tabs-ptz" class="tabs-ptz">
                                <div id="pt-speed-ctrl" style="display:none">
                                    <button type="button" id="pt_speed_fast" class="pt_speed_fast" data-i18n="cfg.fast">Fast</button>
                                    <div id="pt_speed" class="slider_focus">
                                        <div id="pt_speed-handle" class="ui-slider-handle"></div>
                                    </div>
                                    <button type="button" id="pt_speed_slow" class="pt_speed_slow" data-i18n="cfg.slow">Slow</button>
                                    <input type="text" name="set_pt_speed" id="set_pt_speed" class="input_pt_speed" value="0.10" style="display:none">
                                    <div id="pt_speed_tips" class="speed_tips_vertical"></div>
                                </div>
                                <div id="pt-ctrl" style="display:none">
                                    <div style="display:inline-block">
                                        <div id="pt-ctrl-nav">
                                            <div id="pt-ctrl-leftup" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentcolor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L10.5 11.5607L6.53033 15.5303C6.31583 15.7448 5.99324 15.809 5.71299 15.6929C5.43273 15.5768 5.25 15.3033 5.25 15V6C5.25 5.58579 5.58579 5.25 6 5.25L15 5.25C15.3033 5.25 15.5768 5.43273 15.6929 5.71299C15.809 5.99324 15.7448 6.31583 15.5303 6.53033L11.5607 10.5L18.5303 17.4697Z"/>
                                                </svg>
                                            </div>
                                            <div id="pt-ctrl-up" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12.75 20C12.75 20.4142 12.4142 20.75 12 20.75C11.5858 20.75 11.25 20.4142 11.25 20L11.25 10.75H6.00002C5.69668 10.75 5.4232 10.5673 5.30711 10.287C5.19103 10.0068 5.25519 9.68417 5.46969 9.46967L11.4697 3.46967C11.6103 3.32902 11.8011 3.25 12 3.25C12.1989 3.25 12.3897 3.32902 12.5304 3.46967L18.5304 9.46967C18.7449 9.68417 18.809 10.0068 18.6929 10.287C18.5768 10.5673 18.3034 10.75 18 10.75H12.75L12.75 20Z"/>
                                                </svg>
                                            </div>
                                            <div id="pt-ctrl-rightup" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M5.46967 17.4697C5.17678 17.7626 5.17678 18.2374 5.46967 18.5303C5.76256 18.8232 6.23744 18.8232 6.53033 18.5303L13.5 11.5607L17.4697 15.5303C17.6842 15.7448 18.0068 15.809 18.287 15.6929C18.5673 15.5768 18.75 15.3033 18.75 15V6C18.75 5.58579 18.4142 5.25 18 5.25L9 5.25C8.69665 5.25 8.42318 5.43273 8.30709 5.71299C8.19101 5.99324 8.25517 6.31583 8.46967 6.53033L12.4393 10.5L5.46967 17.4697Z"/>
                                                </svg>
                                            </div>
                                            <div id="pt-ctrl-left" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentcolor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M20 11.25C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H10.75L10.75 18C10.75 18.3034 10.5673 18.5768 10.287 18.6929C10.0068 18.809 9.68417 18.7449 9.46967 18.5304L3.46967 12.5304C3.32902 12.3897 3.25 12.1989 3.25 12C3.25 11.8011 3.32902 11.6103 3.46967 11.4697L9.46967 5.46969C9.68417 5.25519 10.0068 5.19103 10.287 5.30711C10.5673 5.4232 10.75 5.69668 10.75 6.00002L10.75 11.25H20Z"/>
                                                </svg>
                                            </div>
                                            <div id="pt-ctrl-joystick" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentcolor" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="12" cy="12" r="10"/>
                                                </svg>
                                            </div>
                                            <div id="pt-ctrl-right" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M4 11.25C3.58579 11.25 3.25 11.5858 3.25 12C3.25 12.4142 3.58579 12.75 4 12.75H13.25V18C13.25 18.3034 13.4327 18.5768 13.713 18.6929C13.9932 18.809 14.3158 18.7449 14.5303 18.5304L20.5303 12.5304C20.671 12.3897 20.75 12.1989 20.75 12C20.75 11.8011 20.671 11.6103 20.5303 11.4697L14.5303 5.46969C14.3158 5.25519 13.9932 5.19103 13.713 5.30711C13.4327 5.4232 13.25 5.69668 13.25 6.00002V11.25H4Z"/>
                                                </svg>
                                            </div>
                                            <div id="pt-ctrl-leftdown" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentcolor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M18.5303 6.53033C18.8232 6.23744 18.8232 5.76256 18.5303 5.46967C18.2374 5.17678 17.7626 5.17678 17.4697 5.46967L10.5 12.4393L6.53033 8.46967C6.31583 8.25517 5.99324 8.191 5.71299 8.30709C5.43273 8.42318 5.25 8.69665 5.25 9V18C5.25 18.4142 5.58579 18.75 6 18.75L15 18.75C15.3033 18.75 15.5768 18.5673 15.6929 18.287C15.809 18.0068 15.7448 17.6842 15.5303 17.4697L11.5607 13.5L18.5303 6.53033Z"/>
                                                </svg>
                                            </div>
                                            <div id="pt-ctrl-down" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M12.75 4C12.75 3.58579 12.4142 3.25 12 3.25C11.5858 3.25 11.25 3.58579 11.25 4L11.25 13.25H6.00002C5.69668 13.25 5.4232 13.4327 5.30711 13.713C5.19103 13.9932 5.25519 14.3158 5.46969 14.5303L11.4697 20.5303C11.6103 20.671 11.8011 20.75 12 20.75C12.1989 20.75 12.3897 20.671 12.5304 20.5303L18.5304 14.5303C18.7449 14.3158 18.809 13.9932 18.6929 13.713C18.5768 13.4327 18.3034 13.25 18 13.25H12.75L12.75 4Z"/>
                                                </svg>
                                            </div>
                                            <div id="pt-ctrl-rightdown" class="pt-ctrl-nbtn">
                                                <svg viewBox="0 0 24 24" fill="currentcolor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L13.5 12.4393L17.4697 8.46967C17.6842 8.25517 18.0068 8.191 18.287 8.30709C18.5673 8.42318 18.75 8.69665 18.75 9V18C18.75 18.4142 18.4142 18.75 18 18.75L9 18.75C8.69665 18.75 8.42318 18.5673 8.30709 18.287C8.19101 18.0068 8.25517 17.6842 8.46967 17.4697L12.4393 13.5L5.46967 6.53033Z"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <div style="display:flex;justify-content:center">
                                            <div id="pt-ctrl-home" class="pt-ctrl-home">
                                                <svg viewBox="0 0 24 24" fill="currentcolor" stroke="currentcolor" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M6.5 20V11H3L12 5L21 11H17.5V20H14.5V16.5C14.5 15.6716 13.8284 15 13 15H11C10.1716 15 9.5 15.6716 9.5 16.5V20H6.5Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="tab-ctrl-af-f-pos" style="display:none">
                                    <span id="lens_focus_pos_minus" class="slider_icon_minus">
                                        <svg class="svg_focus_zoom" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
                                            <path d="M3,13A9,9 0 0,0 12,22A9,9 0 0,0 3,13M12,22A9,9 0 0,0 21,13A9,9 0 0,0 12,22M18,3V8A6,6 0 0,1 12,14A6,6 0 0,1 6,8V3C6.74,3 7.47,3.12 8.16,3.39C8.71,3.62 9.2,3.96 9.61,4.39L12,2L14.39,4.39C14.8,3.96 15.29,3.62 15.84,3.39C16.53,3.12 17.26,3 18,3Z"/>
                                        </svg>
                                    </span>
                                    <div id="lens_focus_pos" class="slider_focus">
                                        <div id="lens_focus_pos-handle" class="ui-slider-handle"></div>
                                    </div>
                                    <span id="lens_focus_pos_plus" class="slider_icon_plus">
                                        <svg class="svg_focus_zoom" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.18596 18.1957C3.10451 18.329 3.20043 18.5 3.35661 18.5H20.1935C20.3379 18.5 20.4347 18.3516 20.3766 18.2195L15.1918 6.43601C15.1205 6.27377 14.8889 6.2777 14.8231 6.44228L12.5803 12.0492C12.5538 12.1155 12.5646 12.1909 12.6087 12.2471L14.2863 14.3822C14.6275 14.8165 14.5521 15.4451 14.1178 15.7863C13.6835 16.1275 13.0549 16.0521 12.7137 15.6178L8.41831 10.151C8.33217 10.0414 8.1631 10.0513 8.09039 10.1703L3.18596 18.1957Z"/>
                                            <path d="M14.2863 14.3822L14.6795 14.0733L14.6795 14.0733L14.2863 14.3822ZM14.1178 15.7863L13.8089 15.3932L13.8089 15.3932L14.1178 15.7863ZM12.7137 15.6178L13.1068 15.3089L13.1068 15.3089L12.7137 15.6178ZM12.5803 12.0492L12.1161 11.8635L12.5803 12.0492ZM12.6087 12.2471L12.2156 12.556L12.6087 12.2471ZM8.41831 10.151L8.81147 9.84208L8.41831 10.151ZM8.09039 10.1703L7.66375 9.90954L8.09039 10.1703ZM15.1918 6.43601L15.6495 6.23464L15.1918 6.43601ZM14.8231 6.44228L15.2873 6.62798L14.8231 6.44228ZM3.18596 18.1957L2.75931 17.935L3.18596 18.1957ZM20.3766 18.2195L19.9189 18.4208L20.3766 18.2195ZM20.1935 19H3.35661V18H20.1935V19ZM15.6495 6.23464L20.8342 18.0181L19.9189 18.4208L14.7342 6.63738L15.6495 6.23464ZM12.1161 11.8635L14.3588 6.25659L15.2873 6.62798L13.0445 12.2349L12.1161 11.8635ZM13.0019 11.9382L14.6795 14.0733L13.8932 14.6911L12.2156 12.556L13.0019 11.9382ZM14.6795 14.0733C15.1913 14.7247 15.0781 15.6677 14.4267 16.1795L13.8089 15.3932C14.026 15.2226 14.0638 14.9082 13.8932 14.6911L14.6795 14.0733ZM14.4267 16.1795C13.7753 16.6913 12.8323 16.5781 12.3205 15.9267L13.1068 15.3089C13.2774 15.526 13.5918 15.5638 13.8089 15.3932L14.4267 16.1795ZM12.3205 15.9267L8.02516 10.4599L8.81147 9.84208L13.1068 15.3089L12.3205 15.9267ZM2.75931 17.935L7.66375 9.90954L8.51704 10.431L3.6126 18.4564L2.75931 17.935ZM13.0445 12.2349C13.0843 12.1355 13.0681 12.0224 13.0019 11.9382L12.2156 12.556C12.0612 12.3595 12.0233 12.0956 12.1161 11.8635L13.0445 12.2349ZM8.02516 10.4599C8.15437 10.6244 8.40798 10.6094 8.51704 10.431L7.66375 9.90954C7.91822 9.49313 8.50997 9.45835 8.81147 9.84208L8.02516 10.4599ZM14.7342 6.63738C14.8413 6.88075 15.1886 6.87485 15.2873 6.62798L14.3588 6.25659C14.5893 5.68056 15.3996 5.66679 15.6495 6.23464L14.7342 6.63738ZM3.35661 19C2.80996 19 2.47426 18.4014 2.75931 17.935L3.6126 18.4564C3.73476 18.2565 3.59089 18 3.35661 18V19ZM20.1935 18C19.9769 18 19.8317 18.2225 19.9189 18.4208L20.8342 18.0181C21.0378 18.4807 20.6989 19 20.1935 19V18Z"/>
                                        </svg>
                                    </span>
                                    <input type="text" name="lens_focus_pos_tips" id="lens_focus_pos_tips" value="" class="input_pos" style="display:none" readonly="readonly">
                                    <input type="text" name="set_lens_focus_pos" id="set_lens_focus_pos" class="input_pos">
                                    <input id="lens_focus_pos_min" type="hidden">
                                    <input id="lens_focus_pos_max" type="hidden">
                                </div>
                                <div id="tab-ctrl-af-zoom-pos" style="display:none">
                                    <span id="lens_zoom_pos_minus" class="slider_icon_wide">
                                        <svg class="svg_focus_zoom" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                                            <path fill-rule="evenodd" d="M9 4a5 5 0 100 10A5 5 0 009 4zM2 9a7 7 0 1112.6 4.2.999.999 0 01.107.093l3 3a1 1 0 01-1.414 1.414l-3-3a.999.999 0 01-.093-.107A7 7 0 012 9zm10.5 0a1 1 0 00-1-1h-5a1 1 0 100 2h5a1 1 0 001-1z"/>
                                        </svg>
                                    </span>
                                    <div id="lens_zoom_pos" class="slider_focus">
                                        <div id="lens_zoom_pos-handle" class="ui-slider-handle"></div>
                                    </div>
                                    <span id="lens_zoom_pos_plus" class="slider_icon_tele">
                                        <svg class="svg_focus_zoom" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4 9a5 5 0 1110 0A5 5 0 014 9zm5-7a7 7 0 104.2 12.6.999.999 0 00.093.107l3 3a1 1 0 001.414-1.414l-3-3a.999.999 0 00-.107-.093A7 7 0 009 2zM8 6.5a1 1 0 112 0V8h1.5a1 1 0 110 2H10v1.5a1 1 0 11-2 0V10H6.5a1 1 0 010-2H8V6.5z"/>
                                        </svg>
                                    </span>
                                    <input type="text" name="lens_zoom_pos_tips" id="lens_zoom_pos_tips" value="" class="input_pos" style="display:none" readonly="readonly">
                                    <input type="text" name="set_lens_zoom_pos" id="set_lens_zoom_pos" class="input_pos">
                                    <input id="lens_zoom_pos_min" type="hidden">
                                    <input id="lens_zoom_pos_max" type="hidden">
                                </div>
                                <div id="tab-ctrl-focus-zoom-speed" style="display:none">
                                    <button type="button" id="fz_speed_slow" class="fz_speed_slow" data-i18n="cfg.slow">Slow</button>
                                    <div id="fz_speed" class="slider_speed">
                                        <div id="fz_speed-handle" class="ui-slider-handle"></div>
                                    </div>
                                    <button type="button" id="fz_speed_fast" class="fz_speed_fast" data-i18n="cfg.fast">Fast</button>
                                    <div id="fz_speed_tips" class="fz_speed_tips"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="sys-info" class="ui-corner-all ui-widget ui-widget-content">
                        <div id="info_bar_stream">
                            <p class="info_stream" id="info_rtmp">RTMP : IDLE</p>
                            <p class="info_stream" id="info_srt">SRT : IDLE</p>
                        </div>
                        <p class="info" id="info_link_mode"></p>
                        <p class="info" id="info_link"></p>
                        <p class="info" id="info_card">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="currentcolor" width="14px" height="14px" viewBox="0 0 309.662 309.662">
                                <g>
                                    <path d="M30.701,309.662h23.214l6.333-8.709c1.722-2.372,5.494-4.288,8.419-4.288h172.331c2.92,0,6.695,1.916,8.419,4.288   l6.328,8.709h23.219c10.231,0,18.532-8.301,18.532-18.537V102.656l-22.24-23.48V18.538c0-10.237-8.3-18.538-18.537-18.538H30.701   C20.464,0,12.166,8.301,12.166,18.538v272.587C12.166,301.361,20.464,309.662,30.701,309.662z M213.823,24.133h22.535v52.488   h-22.535V24.133z M171.404,24.133h22.535v52.488h-22.535V24.133z M128.982,24.133h22.535v52.488h-22.535V24.133z M86.563,24.133   h22.535v52.488H86.563V24.133z M44.144,24.133h22.535v52.488H44.144V24.133z"/>
                                </g>
                            </svg>
                        </p>
                        <div id="info_bar_rec">
                            <div id="info_rec">
                                <p id="info_pre_roll">Pre Roll</p>
                                <div class="info_" id="info_rec_indicator"></div>
                                <p id="info_duration">00:00:00</p>
                            </div>
                            <p id="info_remain">00h00m</p>
                        </div>
                        <button id="btn_cap" type="button">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="19" viewBox="0 0 24 19" fill="currentcolor" class="snapshot">
                                <path id="snapshot" class="cls-1" d="M1841,1057h-16a4,4,0,0,1-4-4v-8a4,4,0,0,1,4-4h2a3,3,0,0,1,3-3h6a3,3,0,0,1,3,3h2a4,4,0,0,1,4,4v8A4,4,0,0,1,1841,1057Zm-8-15a6,6,0,1,1-6,6A6,6,0,0,1,1833,1042Zm0,1.61a4.39,4.39,0,1,1-4.39,4.39A4.4,4.4,0,0,1,1833,1043.61Z" transform="translate(-1821 -1038)"/>
                            </svg>
                        </button>
                        <button id="btn_rec" type="button">
                            <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="20" cy="20" r="18" fill="none" stroke="#e7e7ee" stroke-width="2"/>
                                <circle id="svg_record" cx="20" cy="20" r="13" fill="red" style="display:none"/>
                                <rect id="svg_recording" x="10" y="10" width="20" height="20" fill="red" rx="4" ry="4" style="display:none"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <div id="tabs-settings" class="unselectable">
                <div id="settings">
                    <div id="settings_tabs">
                        <ul id="settings_tabs_ul">
                            <li>
                                <a href="#tabs-settings-rec" data-i18n="tab.rec">Record</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-video" data-i18n="tab.video">Video</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-audio" data-i18n="tab.audio">Audio</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-exp" data-i18n="tab.exp">Exposure</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-af" data-i18n="tab.focus">Focus</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-pt" data-i18n="tab.pt">Pan Tilt</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-stream" data-i18n="tab.stream">Streaming</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-assit" data-i18n="tab.assist">Assist Tools</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-tc" data-i18n="tab.tc">Time Code</a>
                            </li>
                            <li style="display:none">
                                <a href="#tabs-settings-multicam" data-i18n="tab.multicam">Multi Camera</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-net" data-i18n="tab.network">Network</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-security" data-i18n="tab.security">Security</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-system" data-i18n="tab.system">System</a>
                            </li>
                            <li>
                                <a href="#tabs-settings-mt" data-i18n="tab.mt">Maintenance</a>
                            </li>
                        </ul>
                        <div id="settings_list">
                            <div id="tabs-settings-rec">
                                <label data-i18n="cfg.vfrctrl">VFR Control</label>
                                <select name="vfr_ctrl" id="vfr_ctrl" class="segment_menu"></select>
                                <label data-i18n="cfg.filefmt">File Format</label>
                                <select name="file_format" id="file_format" class="segment_menu"></select>
                                <div id="file_rot_ctrl" style="display:none">
                                    <label data-i18n="cfg.filerot">File Rotation</label>
                                    <select name="file_rot" id="file_rot" class="select_menu"></select>
                                </div>
                                <label data-i18n="cfg.splitdur">Split Duration</label>
                                <select name="split_duration" id="split_duration" class="select_menu"></select>
                                <label data-i18n="cfg.pbfr">Playback Framerate</label>
                                <select name="rec_fps" id="rec_fps" class="select_menu"></select>
                                <div id="pre_roll" style="display:none">
                                    <label data-i18n="cfg.preroll">Pre Roll</label>
                                    <select name="preroll" id="preroll" class="segment_menu"></select>
                                    <label data-i18n="cfg.prerolldur">Pre Roll Duration</label>
                                    <select name="preroll_duration" id="preroll_duration" class="select_menu"></select>
                                </div>
                                <label data-i18n="cfg.recfrmindica">Record Frame Indicator</label>
                                <select name="rec_frame_indicator" id="rec_frame_indicator" class="segment_menu"></select>
                                <br>
                                <br>
                                <button id="btn_meta_setup" type="button" data-i18n="cfg.metesetup">Meta Setup</button>
                                <br>
                                <br>
                            </div>
                            <div id="tabs-settings-video">
                                <div>
                                    <label data-i18n="cfg.encoder">Encoder</label>
                                    <select name="encoder" id="encoder" class="select_menu"></select>
                                </div>
                                <label data-i18n="cfg.br">Bitrate</label>
                                <select name="bitrate" id="bitrate" class="segment_menu"></select>
                                <div id="eis" style="display:none">
                                    <label data-i18n="cfg.eis">Image Stabilizer</label>
                                    <select name="eis_on_off" id="eis_on_off" class="segment_menu"></select>
                                </div>
                                <label data-i18n="cfg.luma_lvl">Luminance Level</label>
                                <select name="img_luma_level" id="img_luma_level" class="select_menu"></select>
                                <div id="video_rot_ctrl" style="display:none">
                                    <label data-i18n="cfg.vid_rot">Rotation</label>
                                    <select name="video_rot" id="video_rot" class="segment_menu"></select>
                                </div>
                                <label data-i18n="cfg.rec_mode">Record Mode</label>
                                <select name="video_mode" id="video_mode" class="select_menu"></select>
                                <div id="fast_readout" style="display:none">
                                    <label data-i18n="cfg.lowjellow">Low Jello</label>
                                    <select name="low_jello" id="low_jello" class="segment_menu"></select>
                                </div>
                                <label data-i18n="cfg.photoq">Photo Quality</label>
                                <select name="photo_q" id="photo_q" class="segment_menu"></select>
                                <label data-i18n="cfg.tlint">Timelapse Interval</label>
                                <div id="video_tl_interval" class="sliderSpinner"></div>
                                <br>
                            </div>
                            <div id="tabs-settings-audio">
                                <label data-i18n="cfg.encoder">Encoder</label>
                                <select name="audio_encoder" id="audio_encoder" class="segment_menu"></select>
                                <label data-i18n="cfg.input">Input</label>
                                <select name="audio_channel" id="audio_channel" class="select_menu"></select>
                                <div id="audio_line_in" style="display:none">
                                    <label data-i18n="cfg.inpu_lvl">Input Level</label>
                                    <select name="audio_input_level" id="audio_input_level" class="segment_menu"></select>
                                </div>
                                <div id="audio_phantom_pow" style="display:none">
                                    <label data-i18n="cfg.phantompwr">Phantom Power</label>
                                    <select name="audio_phantom_power" id="audio_phantom_power" class="segment_menu"></select>
                                </div>
                                <label data-i18n="cfg.lvldisp">Level Display</label>
                                <select name="audio_level_display" id="audio_level_display" class="segment_menu"></select>
                                <label data-i18n="cfg.nr">Noise Reduction</label>
                                <select name="audio_noise_reduction" id="audio_noise_reduction" class="segment_menu"></select>
                                <label data-i18n="cfg.in_gain_ctrl">Input Gain Control</label>
                                <select name="ain_gain_type" id="ain_gain_type" class="segment_menu"></select>
                                <div id="audio_in_gain_ctrl">
                                    <label data-i18n="cfg.l_in_g">Left In Gain (dB)</label>
                                    <div name="audio_in_l_gain" id="audio_in_l_gain" class="sliderSpinner"></div>
                                    <label data-i18n="cfg.r_in_g">Right In Gain (dB)</label>
                                    <div name="audio_in_r_gain" id="audio_in_r_gain" class="sliderSpinner"></div>
                                </div>
                                <div id="audio_output_gain_ctrl" style="display:none">
                                    <label data-i18n="cfg.o_g">Out Gain (dB)</label>
                                    <div name="audio_output_gain" id="audio_output_gain" class="sliderSpinner"></div>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-exp">
                                <label data-i18n="cfg.deflicker">Flicker Reduction</label>
                                <select name="sys_flicker" id="sys_flicker" class="segment_menu"></select>
                                <label data-i18n="cfg.exp_meter">Metering</label>
                                <select name="exp_meter" id="exp_meter" class="segment_menu"></select>
                                <div id="exp_iso_ctrl" style="display:none">
                                    <label data-i18n="cfg.isoctrl">ISO Control</label>
                                    <select name="exp_iso_control" id="exp_iso_control" class="segment_menu"></select>
                                </div>
                                <label data-i18n="cfg.isomin">Min ISO</label>
                                <input id="exp_min_iso" name="exp_min_iso" class="option_menu">
                                <label data-i18n="cfg.isomax">Max ISO</label>
                                <input id="exp_max_iso" name="exp_max_iso" class="option_menu">
                                <label data-i18n="cfg.shtop">Shutter Operation</label>
                                <select name="sys_sht_op" id="sys_sht_op" class="segment_menu"></select>
                                <label data-i18n="cfg.shtanglctrl">Shutter Angle Control</label>
                                <select name="exp_shr_angle_control" id="exp_shr_angle_control" class="segment_menu"></select>
                                <label id="max_epx_sht_info" data-i18n="cfg.expmaxspe">Max Shutter Speed</label>
                                <select name="max_exp_sht_time" id="max_exp_sht_time" class="select_menu"></select>
                                <label data-i18n="cfg.aespd">AE Speed</label>
                                <select name="exp_ae_speed" id="exp_ae_speed" class="segment_menu"></select>
                                <div id="bl_comp_info" style="display:none">
                                    <label data-i18n="cfg.exp_bl">Backlight Compensation</label>
                                    <select name="bl_comp" id="bl_comp" class="segment_menu"></select>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-af">
                                <div id="zoom_mode_support" style="display:none">
                                    <label data-i18n="cfg.zoom">Zoom</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.mode">Mode</label>
                                    <select name="zoom_mode" id="zoom_mode" class="segment_menu"></select>
                                </div>
                                <div>
                                    <label data-i18n="cfg.focus">Focus</label>
                                    <hr class="camui-border-red">
                                    <div id="focus_live_mf_ctrl" style="display:none">
                                        <label data-i18n="cfg.live_caf">Live CAF</label>
                                        <select name="focus_live_caf" id="focus_live_caf" class="segment_menu"></select>
                                        <label data-i18n="cfg.mf_assit_prev">MF Assist Preview</label>
                                        <select name="mf_assist_preview" id="mf_assist_preview" class="segment_menu"></select>
                                        <label data-i18n="cfg.mf_assit_rec">MF Assist Recording</label>
                                        <select name="mf_assist_recording" id="mf_assist_recording" class="segment_menu"></select>
                                    </div>
                                    <label data-i18n="cfg.afspd">AF Speed</label>
                                    <select name="focus_speed" id="focus_speed" class="segment_menu"></select>
                                    <div id="adjust_with_ptz" style="display:none">
                                        <label data-i18n="cfg.adjwithptz">Focus Adjust With PTZ</label>
                                        <select name="focus_adjust" id="focus_adjust" class="segment_menu"></select>
                                    </div>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-pt">
                                <div id="settings-ptz">
                                    <label data-i18n="cfg.pt">Pan Tilt</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.pt_speedmode">Speed Mode</label>
                                    <select name="ptz_speedmode" id="ptz_speedmode" class="segment_menu"></select>
                                    <label data-i18n="cfg.pt_spd_with_zoom_pos">Speed With Zoom Position</label>
                                    <select name="ptz_spd_with_zoom_pos" id="ptz_spd_with_zoom_pos" class="segment_menu"></select>
                                    <label data-i18n="cfg.pt_flip">Flip</label>
                                    <select name="ptz_flip" id="ptz_flip" class="segment_menu"></select>
                                    <label data-i18n="cfg.pt_poweron">Power On Position</label>
                                    <select name="ptz_power_on_pos" id="ptz_power_on_pos" class="select_menu"></select>
                                    <label data-i18n="cfg.pt_privacy">Privacy Mode</label>
                                    <select name="ptz_privacy_mode" id="ptz_privacy_mode" class="segment_menu"></select>
                                    <label data-i18n="cfg.pt_cur_pos">Current Position</label>
                                    <button id="ptz_cur_pos" type="button" class="ui-setting-fixw-btn" disabled="disabled"></button>
                                    <div style="display:none">
                                        <br>
                                        <button id="btn_ptz_reset" type="button" data-i18n="cfg.reset">Reset</button>
                                    </div>
                                </div>
                                <br>
                                <div id="settings-ptz-limit">
                                    <label data-i18n="cfg.pt_range">Range Limit</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.status">Status</label>
                                    <select name="ptz_limit" id="ptz_limit" class="segment_menu"></select>
                                    <br>
                                    <div id="ptz_limit_ctrl">
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.pt_u">Up</label>
                                            <div id="ptz_limit_up" class="sliderSpinner"></div>
                                            <button id="btn_up_current" type="button" data-i18n="cfg.pt_cur">Current</button>
                                            <button id="btn_up_reset" type="button" data-i18n="cfg.reset">Reset</button>
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.pt_d">Down</label>
                                            <div id="ptz_limit_down" class="sliderSpinner"></div>
                                            <button id="btn_down_current" type="button" data-i18n="cfg.pt_cur">Current</button>
                                            <button id="btn_down_reset" type="button" data-i18n="cfg.reset">Reset</button>
                                        </div>
                                        <div>
                                            <div class="inline-elements">
                                                <label data-i18n="cfg.pt_l">Left</label>
                                                <div id="ptz_limit_left" class="sliderSpinner"></div>
                                                <button id="btn_left_current" type="button" data-i18n="cfg.pt_cur">Current</button>
                                                <button id="btn_left_reset" type="button" data-i18n="cfg.reset">Reset</button>
                                            </div>
                                            <div class="inline-elements">
                                                <label data-i18n="cfg.pt_r">Right</label>
                                                <div id="ptz_limit_right" class="sliderSpinner"></div>
                                                <button id="btn_right_current" type="button" data-i18n="cfg.pt_cur">Current</button>
                                                <button id="btn_right_reset" type="button" data-i18n="cfg.reset">Reset</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br>
                                <div id="settings-ptz-preset">
                                    <label data-i18n="cfg.pt_preset">Preset</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.pt_recall_mode">Recall Mode</label>
                                        <select name="ptz_preset_mode" id="ptz_preset_mode" class="select_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.pt_preset_spd_mode">Speed Mode</label>
                                        <select name="ptz_speed_mode" id="ptz_speed_mode" class="segment_menu"></select>
                                    </div>
                                    <div id="freeze_during_preset_ctrl" style="display:none" class="inline-elements">
                                        <label data-i18n="cfg.pt_preset_freeze">Freeze During Preset</label>
                                        <select name="freeze_during_preset" id="freeze_during_preset" class="segment_menu"></select>
                                    </div>
                                    <div class="inline-elements" style="display:none">
                                        <label data-i18n="cfg.pt_adj_spd_with_zoom">Adjust Speed With Zoom</label>
                                        <select name="ptz_adj_spd_with_zoom" id="ptz_adj_spd_with_zoom" class="segment_menu"></select>
                                    </div>
                                    <div>
                                        <div id="ptz_speed_unit_ctrl" class="inline-elements" style="display:none">
                                            <label data-i18n="cfg.pt_preset_com_spd_unit">Common Speed Unit</label>
                                            <select name="ptz_speed_unit" id="ptz_speed_unit" class="segment_menu"></select>
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.pt_preset_com_spd">Common Speed</label>
                                            <div id="ptz_speed" class="sliderSpinner"></div>
                                        </div>
                                        <div id="ptz_time_ctrl" class="inline-elements" style="display:none">
                                            <label data-i18n="cfg.pt_preset_com_time">Common Time(s)</label>
                                            <div id="ptz_time" class="sliderSpinner"></div>
                                        </div>
                                    </div>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-stream">
                                <div id="stream_setting">
                                    <label data-i18n="cfg.setting">Setting</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.res">Resolution</label>
                                        <select name="stream_resolution" id="stream_resolution" class="select_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.encoder">Encoder</label>
                                        <select name="stream_video_encoder" id="stream_video_encoder" class="select_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.fps">FPS</label>
                                        <select name="stream_fps" id="stream_fps" class="select_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.target_br">Target Bitrate (Mbps)</label>
                                        <input readonly="readonly" id="stream_bitrate" name="stream_bitrate" value="3.0" class="ui-spinner-input">
                                    </div>
                                    <label data-i18n="cfg.restore_str_setting">Restore Stream Settings</label>
                                    <select name="stream_param_save" id="stream_param_save" class="segment_menu"></select>
                                </div>
                                <div id="stream_rtmp">
                                    <br>
                                    <label>RTMP</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label>URL</label>
                                        <input type="text" name="rtmp_url" id="rtmp_url" value="">
                                        <button id="btn_rtmp_start" type="button">Start</button>
                                    </div>
                                    <div class="inline-elements">
                                        <form>
                                            <label data-i18n="cfg.stream_key">Stream Key</label>
                                            <input type="password" name="rtmp_key" id="rtmp_key" value="" role="button" autocomplete="off">
                                            <button id="btn_show_rtmp_key" type="button" data-i18n="Show">Show</button>
                                        </form>
                                    </div>
                                    <label id="rtmp_bw">Network : 0 Mbps</label>
                                </div>
                                <div id="stream_srt">
                                    <br>
                                    <label>SRT</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.mode">Mode</label>
                                        <select name="srt_mode" id="srt_mode" class="segment_menu">
                                            <option value="Caller" data-i18n="cfg.caller">Caller</option>
                                            <option value="Listener" data-i18n="cfg.listener">Listener</option>
                                        </select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.psw">Passphrase</label>
                                        <input type="text" name="srt_passphrase" id="srt_passphrase" data-dialog-content="dlg.srt_psw_hint" value="">
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.encry">Encryption</label>
                                        <select name="srt_encryption" id="srt_encryption" class="select_menu">
                                            <option value="Off" data-i18n="cfg.off">Off</option>
                                            <option value="AES-128">AES-128</option>
                                            <option value="AES-192">AES-192</option>
                                            <option value="AES-256">AES-256</option>
                                        </select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.srt_late">Latency (millisecond)</label>
                                        <input type="text" name="srt_latency" id="srt_latency" data-dialog-content="dlg.srt_lat_hint" value="">
                                    </div>
                                    <div class="inline-elements">
                                        <label>TTL</label>
                                        <input type="text" name="srt_ttl" id="srt_ttl" data-dialog-content="dlg.srt_ttl_hint" value="">
                                    </div>
                                    <div class="wrap-button-elements">
                                        <button id="srt_save" type="button" data-i18n="cfg.save">Save</button>
                                        <button id="srt_cancel" type="button" data-i18n="cfg.cancel">Cancel</button>
                                    </div>
                                    <div id="srt_caller_info">
                                        <label>URL</label>
                                        <input type="text" name="srt_url" id="srt_url" value="">
                                        <button id="btn_srt_start" type="button">Start</button>
                                        <label id="srt_bw">Network : 0 Mbps</label>
                                    </div>
                                    <div id="srt_listener_info" class="flex-baseline">
                                        URL :&nbsp;<span class="user-select-text" id="srt_listener_url"></span>
                                    </div>
                                </div>
                                <div id="stream_ndi" style="display:none">
                                    <br>
                                    <label>
                                        NDI<sup>®</sup>
                                    </label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.ndi_mn">Machine Name</label>
                                        <input type="text" name="ndi_machine_name" id="ndi_machine_name" data-dialog-content="dlg.ndi_mname_hint" value="">
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.ndi_sn">Stream Name</label>
                                        <input type="text" name="ndi_stream_name" id="ndi_stream_name" data-dialog-content="dlg.ndi_sname_hint" value="">
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.ndi_gn">Group Name</label>
                                        <input type="text" name="ndi_group_name" id="ndi_group_name" data-dialog-content="dlg.ndi_gname_hint" value="">
                                    </div>
                                    <div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.ndi_ds1">Discovery Server 1</label>
                                            <input type="text" name="discovery_server1" id="discovery_server1" data-dialog-content="dlg.ndi_dissev_hiint" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.ndi_ds2">Discovery Server 2</label>
                                            <input type="text" name="discovery_server2" id="discovery_server2" data-dialog-content="dlg.ndi_dissev_hiint" value="">
                                        </div>
                                    </div>
                                    <div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.mcast">Multicast</label>
                                            <select name="ndi_multicast_enable" id="ndi_multicast_enable" class="segment_menu">
                                                <option value="Off" data-i18n="cfg.off">Off</option>
                                                <option value="On" data-i18n="cfg.on">On</option>
                                            </select>
                                        </div>
                                        <div class="inline-elements">
                                            <label>TTL</label>
                                            <input type="text" name="ndi_ttl" id="ndi_ttl" data-dialog-content="dlg.ndi_ttl_hint" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.mcast_nm">Multicast Netmask</label>
                                            <input type="text" name="ndi_multicast_netmask" id="ndi_multicast_netmask" data-dialog-content="dlg.ndi_mcast_mask_hint" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.mcast_pf">Multicast Prefix</label>
                                            <input type="text" name="ndi_multicast_prefix" id="ndi_multicast_prefix" data-dialog-content="dlg.ndi_mcast_pref_hint" value="">
                                        </div>
                                    </div>
                                    <div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.ndi_bn">Bridge Name</label>
                                            <input type="text" name="ndi_bridge_name" id="ndi_bridge_name" data-dialog-content="dlg.ndi_bname_hint" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.ndi_be">Bridge Encryption Key</label>
                                            <input type="text" name="ndi_bridge_encryption_key" id="ndi_bridge_encryption_key" data-dialog-content="dlg.ndi_bencry_hint" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.ndi_bip">Bridge IP</label>
                                            <input type="text" name="ndi_bridge_ip" id="ndi_bridge_ip" data-dialog-content="dlg.ndi_bip_hint" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.ndi_bp">Bridge Port</label>
                                            <input type="text" name="ndi_bridge_port" id="ndi_bridge_port" data-dialog-content="dlg.ndi_bport_hint" value="">
                                        </div>
                                    </div>
                                    <div class="wrap-button-elements">
                                        <button id="ndi_save" type="button" data-i18n="cfg.save">Save</button>
                                        <button id="ndi_cancel" type="button" data-i18n="cfg.cancel">Cancel</button>
                                    </div>
                                </div>
                                <div id="stream_rtsp">
                                    <br>
                                    <label>RTSP</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.auth">Authentication</label>
                                        <select name="rtsp_auth_enable" id="rtsp_auth_enable" class="segment_menu">
                                            <option value="Off" data-i18n="cfg.off">Off</option>
                                            <option value="On" data-i18n="cfg.on">On</option>
                                        </select>
                                    </div>
                                    <div class="flex-baseline">
                                        URL :&nbsp;<span class="user-select-text" id="rtsp_url"></span>
                                    </div>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-assit">
                                <label data-i18n="cfg.disp">Display</label>
                                <select name="assit_disp" id="assit_disp" class="segment_menu"></select>
                                <label data-i18n="cfg.frmline_ratio">Frame Line Ratio</label>
                                <select name="assit_frame_line" id="assit_frame_line" class="select_menu"></select>
                                <label data-i18n="cfg.frmline_color">Frame Line Color</label>
                                <select name="assit_frame_line_color" id="assit_frame_line_color" class="select_menu"></select>
                                <label data-i18n="cfg.center_mark">Center Mark</label>
                                <select name="assit_center_mark" id="assit_center_mark" class="segment_menu"></select>
                                <label data-i18n="cfg.gridline">Grid Line</label>
                                <select name="assit_grid_line" id="assit_grid_line" class="segment_menu"></select>
                                <label data-i18n="cfg.safearea">Safe Area</label>
                                <select name="assit_safe_area" id="assit_safe_area" class="segment_menu"></select>
                                <label data-i18n="cfg.focusarea">Focus Area</label>
                                <select name="assit_hdmi_focus_area" id="assit_hdmi_focus_area" class="segment_menu"></select>
                                <br>
                            </div>
                            <div id="tabs-settings-tc">
                                <label data-i18n="cfg.source">Source</label>
                                <select name="tc_source" id="tc_source" class="select_menu"></select>
                                <label data-i18n="cfg.tc_countup">Count Up</label>
                                <select name="tc_count_up" id="tc_count_up" class="segment_menu"></select>
                                <label data-i18n="cfg.mode">Mode</label>
                                <select name="tc_drop_frame" id="tc_drop_frame" class="segment_menu"></select>
                                <label data-i18n="cfg.hdmi_disp">HDMI Display</label>
                                <select name="tc_hdmi_dispaly" id="tc_hdmi_dispaly" class="segment_menu"></select>
                                <label id="tc_value" data-i18n="cfg.value">Value</label>
                                <button id="btn_tc_reset" type="button" data-i18n="cfg.reset">Reset</button>
                                <button id="btn_tc_time" type="button" data-i18n="cfg.tc_curtime">Current Time</button>
                                <button id="btn_tc_manual" type="button" data-i18n="cfg.manual">Manual</button>
                                <br>
                            </div>
                            <div id="tabs-settings-multicam" style="display:none">
                                <div id="pixel_link" style="display:none">
                                    <label>PixelLink</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.role">Role</label>
                                    <select name="pixel_link_mode" id="pixel_link_mode" class="select_menu"></select>
                                    <label data-i18n="cfg.co_exp">Co-Exposure</label>
                                    <select name="union_ae" id="union_ae" class="segment_menu"></select>
                                    <label data-i18n="cfg.co_wb">Co-WhiteBalance</label>
                                    <select name="union_awb" id="union_awb" class="segment_menu"></select>
                                    <br>
                                    <br>
                                </div>
                                <div id="ez_link" style="display:none">
                                    <label>EzLink</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.role">Role</label>
                                    <select name="ezlink_mode" id="ezlink_mode" class="select_menu"></select>
                                    <label data-i18n="cfg.trigger">Trigger</label>
                                    <select name="ezlink_trigger" id="ezlink_trigger" class="segment_menu"></select>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-net">
                                <div id="settings-wifi" style="display:none">
                                    <label data-i18n="cfg.wifi">WiFi</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.status">Status</label>
                                    <select name="sys_wifi_onoff" id="sys_wifi_onoff" class="segment_menu"></select>
                                    <div id="wifi_info">
                                        <label>SSID</label>
                                        <input type="text" name="sys_wifi_ssid" id="sys_wifi_ssid" value="" readonly="readonly">
                                        <label>IP</label>
                                        <input type="text" name="sys_wifi_ip" id="sys_wifi_ip" value="" readonly="readonly">
                                        <label data-i18n="cfg.wifi_chn">Channel</label>
                                        <select name="sys_wifi_channel" id="sys_wifi_channel" class="select_menu"></select>
                                    </div>
                                    <br>
                                    <br>
                                </div>
                                <label data-i18n="cfg.eth">Ethernet</label>
                                <hr class="camui-border-red">
                                <label data-i18n="cfg.ip_mode">IP Mode</label>
                                <select name="sys_eth_mode" id="sys_eth_mode" class="segment_menu"></select>
                                <div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.ip_addr">IP Address</label>
                                        <input type="text" name="sys_eth_ipaddr" id="sys_eth_ipaddr" value="">
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.netmask">Netmask</label>
                                        <input type="text" name="sys_eth_netmask" id="sys_eth_netmask" value="">
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.gateway">Gateway</label>
                                        <input type="text" name="sys_eth_gateway" id="sys_eth_gateway" value="">
                                    </div>
                                    <div class="inline-elements">
                                        <label>DNS</label>
                                        <input type="text" name="sys_eth_dns" id="sys_eth_dns" value="">
                                    </div>
                                </div>
                                <div class="sys_eth_info">
                                    <span id="sys_eth_mac" class="ui-setting-fixw-span"></span>
                                    <span id="sys_eth_speed"></span>
                                </div>
                                <button id="eth_save" type="button" data-i18n="cfg.save">Save</button>
                                <button id="eth_cancel" type="button" data-i18n="cfg.cancel">Cancel</button>
                                <div id="settings-snmp" style="display:none">
                                    <br>
                                    <label>SNMP</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.status">Status</label>
                                        <select name="snmp_status" id="snmp_status" class="segment_menu"></select>
                                    </div>
                                    <div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.auth">Authentication</label>
                                            <select name="snmp_authentication" id="snmp_authentication" class="segment_menu"></select>
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.encry">Encryption</label>
                                            <select name="snmp_encryption_method" id="snmp_encryption_method" class="segment_menu"></select>
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.usern">User Name</label>
                                            <input type="text" name="snmp_username" id="snmp_username" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.psw">Password</label>
                                            <input type="text" name="snmp_password" id="snmp_password" value="">
                                        </div>
                                        <button id="snmp_save" type="button" style="display:none" data-i18n="cfg.save">Save</button>
                                        <button id="snmp_cancel" type="button" style="display:none" data-i18n="cfg.cancel">Cancel</button>
                                    </div>
                                    <div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.sysn">System Name</label>
                                            <input type="text" name="snmp_system_name" id="snmp_system_name" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.loc">Location</label>
                                            <input type="text" name="snmp_location" id="snmp_location" value="">
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.contact">Contact</label>
                                            <input type="text" name="snmp_contact" id="snmp_contact" value="">
                                        </div>
                                    </div>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-security">
                                <div id="sys_https_ctrl">
                                    <label>HTTPS</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.status">Status</label>
                                    <select name="sys_https_on" id="sys_https_on" class="segment_menu"></select>
                                    <br>
                                    <br>
                                    <label>SSL</label>
                                    <hr class="camui-border-red">
                                    <label data-i18n="cfg.ssl">Certificate</label>
                                    <div class="flex-center">
                                        <select name="sys_https_cert_source" id="sys_https_cert_source" class="segment_menu"></select>
                                        <button id="bth_upload_certificate" type="button" data-i18n="cfg.import">Import</button>
                                        <button id="bth_generate_certificate" type="button" data-i18n="cfg.generate">Generate</button>
                                    </div>
                                    <div id="sys_ssl_info" style="display:none">
                                        <label data-i18n="cfg.status">Status</label>
                                        <button id="sys_ssl_cert_status" type="button" class="ui-setting-ssl-btn" disabled="disabled"></button>
                                        <label data-i18n="cfg.issurtDN">Issuer DN</label>
                                        <button id="sys_ssl_issuer" type="button" class="ui-setting-ssl-btn" disabled="disabled"></button>
                                        <label data-i18n="cfg.available_period">Available Period</label>
                                        <button id="sys_ssl_availble_period" type="button" class="ui-setting-ssl-btn" disabled="disabled"></button>
                                        <button id="btn_delete_certificate" type="button" data-i18n="cfg.delete">Delete</button>
                                    </div>
                                </div>
                                <div id="user_management">
                                    <br>
                                    <label data-i18n="cfg.usermgr">User Management</label>
                                    <hr class="camui-border-red">
                                    <div id="sys_auth_ctrl">
                                        <label data-i18n="cfg.auth">Authentication</label>
                                        <select name="sys_auth" id="sys_auth" class="segment_menu"></select>
                                    </div>
                                    <div id="sys_user" class="sys_user"></div>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-system">
                                <div id="display_ctrl" style="display:none">
                                    <label data-i18n="cfg.disp">Display</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.disp_desq">Desqueeze Display</label>
                                        <select name="sys_desqueeze" id="sys_desqueeze" class="select_menu"></select>
                                    </div>
                                    <div id="lcd" class="inline-elements" style="display:none">
                                        <label data-i18n="cfg.lcd_bri">LCD Brightness</label>
                                        <div id="lcd_backlight" name="lcd_backlight" class="sliderSpinner"></div>
                                    </div>
                                    <br>
                                </div>
                                <div>
                                    <label>HDMI</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.format">Format</label>
                                        <select name="sys_hdmi_res" id="sys_hdmi_res" class="select_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label>OSD</label>
                                        <select name="sys_hdmi_osd" id="sys_hdmi_osd" class="segment_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.layout">Layout</label>
                                        <select name="sys_hdmi_layout" id="sys_hdmi_layout" class="segment_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label>EDID</label>
                                        <select name="sys_hdmi_edid" id="sys_hdmi_edid" class="segment_menu"></select>
                                    </div>
                                </div>
                                <div id="sdi" style="display:none">
                                    <br>
                                    <label>SDI</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.output">Output</label>
                                        <select name="sdi_enable" id="sdi_enable" class="segment_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label>3G SDI</label>
                                        <select name="3g_sdi_mode" id="3g_sdi_mode" class="segment_menu"></select>
                                    </div>
                                </div>
                                <div id="genlock_ctrl" style="display:none">
                                    <br>
                                    <label data-i18n="cfg.genlock">Genlock</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.status">Status</label>
                                        <select name="genlock_status" id="genlock_status" class="segment_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.phase_cor">Phase Coarse</label>
                                        <div id="genlock_coarse" name="genlock_coarse" class="sliderSpinner"></div>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.phase_fine">Phase Fine</label>
                                        <div id="genlock_fine" name="genlock_fine" class="sliderSpinner"></div>
                                    </div>
                                </div>
                                <div id="free_d" style="display:none">
                                    <br>
                                    <label>FreeD</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.status">Status</label>
                                        <select name="freed_enable" id="freed_enable" class="segment_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.camid">Camera ID</label>
                                        <div id="freed_id" name="freed_id" class="sliderSpinner"></div>
                                    </div>
                                    <div class="inline-elements">
                                        <label>IP</label>
                                        <input type="text" name="freed_ip" id="freed_ip" value="">
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.port">Port</label>
                                        <input type="text" name="freed_port" id="freed_port" data-dialog-content="dlg.freed_hint" value="">
                                    </div>
                                    <button id="freed_save" type="button" data-i18n="cfg.save">Save</button>
                                    <button id="freed_cancel" type="button" data-i18n="cfg.cancel">Cancel</button>
                                </div>
                                <div id="visca" style="display:none">
                                    <br>
                                    <label>VISCA</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.status">Status</label>
                                        <select name="visca_enable" id="visca_enable" class="segment_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label>ID</label>
                                        <select name="visca_id" id="visca_id" class="select_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.bdr">Baud Rate</label>
                                        <select name="visca_baud_rate" id="visca_baud_rate" class="segment_menu"></select>
                                    </div>
                                </div>
                                <div>
                                    <br>
                                    <label data-i18n="cfg.indicator">Indicator</label>
                                    <hr class="camui-border-red">
                                    <div id="record_indicatot_ctrl" class="inline-elements" style="display:none">
                                        <label data-i18n="cfg.rec_indicator">Record Indicator</label>
                                        <select name="sys_led" id="sys_led" class="segment_menu"></select>
                                    </div>
                                    <div class="inline-elements">
                                        <label data-i18n="cfg.tally">Tally Lamp Brightness</label>
                                        <select name="tally_on" id="tally_on" class="segment_menu"></select>
                                    </div>
                                </div>
                                <div>
                                    <br>
                                    <label data-i18n="cfg.others">Others</label>
                                    <hr class="camui-border-red">
                                    <div class="inline-elements">
                                        <label>USB</label>
                                        <select name="usb_device_role" id="usb_device_role" class="select_menu"></select>
                                    </div>
                                    <div id="ir_service" class="inline-block" style="display:none">
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.irrecv">IR Receive</label>
                                            <select name="ir" id="ir" class="segment_menu"></select>
                                        </div>
                                        <div class="inline-elements">
                                            <label data-i18n="cfg.irid">IR ID</label>
                                            <select name="ir_id" id="ir_id" class="select_menu"></select>
                                        </div>
                                    </div>
                                    <div id="color_bar" class="inline-elements" style="display:none">
                                        <label data-i18n="cfg.colorb">Color Bar</label>
                                        <select name="color_bar_status" id="color_bar_status" class="segment_menu"></select>
                                    </div>
                                    <div>
                                        <label data-i18n="cfg.cam_name">Camera Name</label>
                                        <input name="nick_name" id="nick_name">
                                        <button id="nick_name_save" type="button" data-i18n="cfg.save">Save</button>
                                        <button id="nick_name_cancel" type="button" data-i18n="cfg.cancel">Cancel</button>
                                    </div>
                                </div>
                                <div>
                                    <br>
                                    <label data-i18n="cfg.info">Information</label>
                                    <hr class="camui-border-red">
                                    <div id="sys_info">
                                        <br>
                                        <div id="camera_name" style="display:none"></div>
                                        <div id="info"></div>
                                        <div id="sn"></div>
                                    </div>
                                </div>
                                <br>
                            </div>
                            <div id="tabs-settings-mt">
                                <label data-i18n="cfg.pwr">Power</label>
                                <hr class="camui-border-red">
                                <br>
                                <button id="btn_rst" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.reboot">Reboot</button>
                                <button id="btn_pwr_off" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.pwroff">Power Off</button>
                                <button id="btn_to_standby" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.stby">Standby</button>
                                <br>
                                <div class="inline-elements">
                                    <label data-i18n="cfg.autooff">Auto Power Off</label>
                                    <select name="sys_auto_off" id="sys_auto_off" class="select_menu"></select>
                                </div>
                                <div class="inline-elements">
                                    <label data-i18n="cfg.autostby">Auto Standby</label>
                                    <select name="sys_auto_stby" id="sys_auto_stby" class="select_menu"></select>
                                </div>
                                <br>
                                <br>
                                <label data-i18n="cfg.datetime">DateTime</label>
                                <hr class="camui-border-red">
                                <br>
                                <div id="datetime">
                                    <div class="datetime-container">
                                        <span id data-i18n="cfg.pctime">PC Time :&nbsp;</span>
                                        <span id="pc_datetime"></span>
                                    </div>
                                    <div class="datetime-container">
                                        <span data-i18n="cfg.devtime">Device Time :&nbsp;</span>
                                        <span id="device_datetime"></span>
                                    </div>
                                </div>
                                <button id="btn_sync_time" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.syncpc">Synchronize with PC</button>
                                <br>
                                <div class="inline-elements">
                                    <label data-i18n="cfg.ntp_sync">Synchronize with NTP</label>
                                    <select name="ntp_status" id="ntp_status" class="segment_menu"></select>
                                </div>
                                <div class="inline-elements">
                                    <label data-i18n="cfg.ntp_addr">NTP Server Address</label>
                                    <input id="ntp_server_addr" name="ntp_server_addr">
                                </div>
                                <div class="inline-elements">
                                    <label data-i18n="cfg.ntp_port">NTP Server Port</label>
                                    <input id="ntp_server_port" name="ntp_server_port">
                                </div>
                                <div class="inline-elements">
                                    <label data-i18n="cfg.ntp_int">NTP Adjustment Interval (H)</label>
                                    <input id="ntp_interval" name="ntp_interval" class="ui-spinner-input" readonly="readonly">
                                </div>
                                <button id="ntp_save" type="button" data-i18n="cfg.save">Save</button>
                                <button id="ntp_cancel" type="button" data-i18n="cfg.cancel">Cancel</button>
                                <br>
                                <br>
                                <label data-i18n="cfg.usrprf">User Profile</label>
                                <hr class="camui-border-red">
                                <br>
                                <button id="btn_userprofile_export" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.export">Export</button>
                                <button id="btn_userprofile_import" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.import">Import</button>
                                <br>
                                <br>
                                <label data-i18n="cfg.others">Others</label>
                                <hr class="camui-border-red">
                                <div id="mt_others">
                                    <button id="btn_fmt" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.formatcard">Format Card</button>
                                    <button id="btn_clr_settings" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.clearsetting">Clear Settings</button>
                                    <button id="btn_upgrade" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.upgrade">Upgrade</button>
                                    <button id="btn_license" style="display:none" type="button" class="ui-setting-fixw-btn" data-i18n="cfg.license">License</button>
                                </div>
                                <br>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="tabs-gallery">
                <div id="file_list_parent" class="ui-corner-all ui-widget ui-widget-content">
                    <ul id="file-list"></ul>
                </div>
            </div>
        </div>
        <div id="pb_mode_tips-dialog" class="dialog-confirm" title="Warning">
            <p>
                <span class="ui-icon ui-icon-alert dialog-br-span"></span>
                <span data-i18n="[html]dlg.pbmode_hint">
                    The video is playing in playback mode.<br>Failed to set the camera to video mode
                </span>
            </p>
        </div>
        <div id="common_warn-dialog" class="dialog-confirm" title="Warning">
            <p>
                <span class="ui-icon ui-icon-alert dialog-span"></span>
            </p>
        </div>
        <div id="tc_manual-dialog" class="dialog-confirm" title="Manual">
            <form>
                <label data-i18n="cfg.Hour">Hour</label>
                <input type="text" name="tc_hour" id="tc_hour" value="00">
                <label data-i18n="cfg.Minute">Minute</label>
                <input type="text" name="tc_min" id="tc_min" value="00">
                <label data-i18n="cfg.Second">Second</label>
                <input type="text" name="tc_sec" id="tc_sec" value="00">
                <label data-i18n="cfg.Frame">Frame</label>
                <input type="text" name="tc_frame" id="tc_frame" value="00">
            </form>
        </div>
        <div id="upgrade-dialog" class="dialog-confirm" title="Upload Firmware">
            <form id="upgrade-form" method="POST" action="/uploadfirmware" enctype="multipart/form-data">
                <input type="file" name="file">
                <div id="progressbar"></div>
            </form>
        </div>
        <div id="upload_certificate-dialog" class="dialog-confirm" title="Upload Certificate">
            <form id="upload_certificate-form" method="POST" action="/uploadcertificate" enctype="multipart/form-data">
                <input type="file" name="file">
                <div id="certificate_progressbar"></div>
            </form>
        </div>
        <div id="upload_userprofile-dialog" class="dialog-confirm" title="Import">
            <form id="upload_userprofile-form" method="POST" action="/prf/import" enctype="multipart/form-data">
                <input type="file" name="file">
                <div id="userprofile_progressbar"></div>
            </form>
        </div>
        <div id="upload_license-dialog" class="dialog-confirm" title="Import">
            <form id="upload_license-form" method="POST" action="/ctrl/license" enctype="multipart/form-data">
                <input type="file" name="file">
                <div id="license_progressbar"></div>
            </form>
        </div>
        <div id="meta_setup-dialog" class="dialog-confirm" title="Meta Setup">
            <form>
                <label data-i18n="cfg.camid">Camera ID</label>
                <input id="camera_id" name="camera_id" class="option_menu" onkeyup='value=value.replace(/[^A-Z]/g,"")'>
                <label data-i18n="cfg.reelname">Reelname</label>
                <input id="reelname" name="reelname" class="ui-spinner-input" oninput="this.value<1&&(this.value=1),999<this.value&&(this.value=999)">
            </form>
        </div>
        <div id="change_pswd-dialog" class="dialog-confirm" title="Change Password">
            <form>
                <label data-i18n="Name">Name</label>
                <input type="text" name="user" id="user" style="width:255px" disabled="disabled">
                <label id="user_tips" class="user_tips"></label>
                <div id="old_password_item">
                    <label data-i18n="old_psw">Old Password</label>
                    <input type="password" data-i18n="[placeholder]old_psw" name="old_password" id="old_password" style="width:255px" placeholder="Old password" oncopy="return!1" role="button" autocomplete="off">
                    <label id="old_password_tips" class="user_tips"></label>
                </div>
                <label data-i18n="new_psw">New Password</label>
                <input type="password" data-i18n="[placeholder]new_psw_ph" name="new_password" id="new_password" style="width:255px" placeholder="At least 8 characters" oncopy="return!1" role="button" autocomplete="off">
                <label id="new_password_tips" class="user_tips"></label>
                <label data-i18n="confirm_psw">Confirm New Password</label>
                <input type="password" data-i18n="[placeholder]same_psw_ph" name="confirm_new_password" id="confirm_new_password" style="width:255px" placeholder="Same as the new password" oncopy="return!1" role="button" autocomplete="off">
                <label id="confirm_password_tips" class="user_tips"></label>
                <label></label>
                <input type="checkbox" id="show_password" style="width:auto">
                <span data-i18n="show_psw">Show Password</span>
                <label style="font-size:10px" data-i18n="user_mgr_hiit_psw"></label>
                <label id="show_password_tips" class="user_error_tips"></label>
            </form>
        </div>
        <div id="user_add-dialog" class="dialog-confirm" title="Add User">
            <form>
                <label data-i18n="Name">Name</label>
                <input type="text" name="user_add_name" id="user_add_name" style="width:255px" placeholder="">
                <label id="user_add_name_tips" class="user_tips"></label>
                <label data-i18n="Password">Password</label>
                <input data-i18n="[placeholder]new_psw_ph" type="password" name="user_add_password" id="user_add_password" style="width:255px" placeholder="At least 8 characters" oncopy="return!1" role="button" autocomplete="off">
                <label id="user_add_password_tips" class="user_tips"></label>
                <label data-i18n="confirm_psw">Confirm Password</label>
                <input data-i18n="[placeholder]same_psw_ph" type="password" name="user_add_confirm_password" id="user_add_confirm_password" style="width:255px" placeholder="Same as the password" oncopy="return!1" role="button" autocomplete="off">
                <label id="user_add_confirm_password_tips" class="user_tips"></label>
                <label data-i18n="Permission">Permission</label>
                <select name="user_add_permission" id="user_add_permission" class="select_menu">
                    <option value="ctrl" data-i18n="Camera Controller">Camera Controller</option>
                    <option value="admin" data-i18n="Administrator">Administrator</option>
                </select>
                <label id="user_add_permission_tips" class="user_tips"></label>
                <label></label>
                <input type="checkbox" id="user_add_show_password" style="width:auto">
                <span data-i18n="show_psw">Show Password</span>
                <label style="font-size:10px" data-i18n="user_mgr_hiit_psw"></label>
                <label id="show_user_add_tips" class="user_error_tips"></label>
            </form>
        </div>
        <script>
            $(function() {
                initUI()
            })
        </script>
    </body>
</html>

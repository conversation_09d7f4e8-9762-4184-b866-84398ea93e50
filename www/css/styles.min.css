/*! jQuery UI - v1.12.1 - 2016-09-14
* http://jqueryui.com
* Includes: core.css, accordion.css, autocomplete.css, menu.css, button.css, controlgroup.css, checkboxradio.css, datepicker.css, dialog.css, draggable.css, resizable.css, progressbar.css, selectable.css, selectmenu.css, slider.css, sortable.css, spinner.css, tabs.css, tooltip.css, theme.css
* To view and modify this theme, visit http://jqueryui.com/themeroller/?bgShadowXPos=&bgOverlayXPos=&bgErrorXPos=&bgHighlightXPos=&bgContentXPos=&bgHeaderXPos=&bgActiveXPos=&bgHoverXPos=&bgDefaultXPos=&bgShadowYPos=&bgOverlayYPos=&bgErrorYPos=&bgHighlightYPos=&bgContentYPos=&bgHeaderYPos=&bgActiveYPos=&bgHoverYPos=&bgDefaultYPos=&bgShadowRepeat=&bgOverlayRepeat=&bgErrorRepeat=&bgHighlightRepeat=&bgContentRepeat=&bgHeaderRepeat=&bgActiveRepeat=&bgHoverRepeat=&bgDefaultRepeat=&iconsHover=url(%22images%2Fui-icons_555555_256x240.png%22)&iconsHighlight=url(%22images%2Fui-icons_777620_256x240.png%22)&iconsHeader=url(%22images%2Fui-icons_444444_256x240.png%22)&iconsError=url(%22images%2Fui-icons_cc0000_256x240.png%22)&iconsDefault=url(%22images%2Fui-icons_777777_256x240.png%22)&iconsContent=url(%22images%2Fui-icons_444444_256x240.png%22)&iconsActive=url(%22images%2Fui-icons_ffffff_256x240.png%22)&bgImgUrlShadow=&bgImgUrlOverlay=&bgImgUrlHover=&bgImgUrlHighlight=&bgImgUrlHeader=&bgImgUrlError=&bgImgUrlDefault=&bgImgUrlContent=&bgImgUrlActive=&opacityFilterShadow=Alpha(Opacity%3D30)&opacityFilterOverlay=Alpha(Opacity%3D30)&opacityShadowPerc=30&opacityOverlayPerc=30&iconColorHover=%23555555&iconColorHighlight=%23777620&iconColorHeader=%23444444&iconColorError=%23cc0000&iconColorDefault=%23777777&iconColorContent=%23444444&iconColorActive=%23ffffff&bgImgOpacityShadow=0&bgImgOpacityOverlay=0&bgImgOpacityError=95&bgImgOpacityHighlight=55&bgImgOpacityContent=75&bgImgOpacityHeader=75&bgImgOpacityActive=65&bgImgOpacityHover=75&bgImgOpacityDefault=75&bgTextureShadow=flat&bgTextureOverlay=flat&bgTextureError=flat&bgTextureHighlight=flat&bgTextureContent=flat&bgTextureHeader=flat&bgTextureActive=flat&bgTextureHover=flat&bgTextureDefault=flat&cornerRadius=3px&fwDefault=normal&ffDefault=Arial%2CHelvetica%2Csans-serif&fsDefault=1em&cornerRadiusShadow=8px&thicknessShadow=5px&offsetLeftShadow=0px&offsetTopShadow=0px&opacityShadow=.3&bgColorShadow=%23666666&opacityOverlay=.3&bgColorOverlay=%23aaaaaa&fcError=%235f3f3f&borderColorError=%23f1a899&bgColorError=%23fddfdf&fcHighlight=%23777620&borderColorHighlight=%23dad55e&bgColorHighlight=%23fffa90&fcContent=%23333333&borderColorContent=%23dddddd&bgColorContent=%23ffffff&fcHeader=%23333333&borderColorHeader=%23dddddd&bgColorHeader=%23e9e9e9&fcActive=%23ffffff&borderColorActive=%23003eff&bgColorActive=%23007fff&fcHover=%232b2b2b&borderColorHover=%23cccccc&bgColorHover=%23ededed&fcDefault=%23454545&borderColorDefault=%23c5c5c5&bgColorDefault=%23f6f6f6
* Copyright jQuery Foundation and other contributors; Licensed MIT */
.ui-helper-hidden {
    display: none
}

.ui-helper-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px
}

.ui-helper-reset {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    line-height: 1.3;
    text-decoration: none;
    font-size: 100%;
    list-style: none
}

.ui-helper-clearfix:after,.ui-helper-clearfix:before {
    content: "";
    display: table;
    border-collapse: collapse
}

.ui-helper-clearfix:after {
    clear: both
}

.ui-helper-zfix {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    opacity: 0;
    filter: Alpha(Opacity=0)
}

.ui-front {
    z-index: 100
}

.ui-state-disabled {
    cursor: default!important;
    pointer-events: none
}

.ui-icon {
    display: inline-block;
    vertical-align: middle;
    margin-top: -.25em;
    position: relative;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat
}

.ui-widget-icon-block {
    left: 50%;
    margin-left: -8px;
    display: block
}

.ui-widget-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.ui-accordion .ui-accordion-header {
    display: block;
    cursor: pointer;
    position: relative;
    margin: 2px 0 0 0;
    padding: .5em .5em .5em .7em;
    font-size: 100%
}

.ui-accordion .ui-accordion-content {
    padding: 1em 2.2em;
    border-top: 0;
    overflow: auto
}

.ui-autocomplete {
    position: absolute;
    top: 0;
    left: 0;
    cursor: default
}

.ui-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    display: block;
    outline: 0
}

.ui-menu .ui-menu {
    position: absolute
}

.ui-menu .ui-menu-item {
    margin: 0;
    cursor: pointer;
    list-style-image: url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)
}

.ui-menu .ui-menu-item-wrapper {
    position: relative;
    padding: 3px 1em 3px .4em
}

.ui-menu .ui-menu-divider {
    margin: 5px 0;
    height: 0;
    font-size: 0;
    line-height: 0;
    border-width: 1px 0 0 0
}

.ui-menu .ui-state-active,.ui-menu .ui-state-focus {
    margin: -1px
}

.ui-menu-icons {
    position: relative
}

.ui-menu-icons .ui-menu-item-wrapper {
    padding-left: 2em
}

.ui-menu .ui-icon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: .2em;
    margin: auto 0
}

.ui-menu .ui-menu-icon {
    left: auto;
    right: 0
}

.ui-button {
    padding: .4em 1em;
    display: inline-block;
    position: relative;
    line-height: normal;
    margin-right: .1em;
    cursor: pointer;
    vertical-align: middle;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: visible
}

.ui-button,.ui-button:active,.ui-button:hover,.ui-button:link,.ui-button:visited {
    text-decoration: none
}

.ui-button-icon-only {
    width: 2em;
    box-sizing: border-box;
    text-indent: -9999px;
    white-space: nowrap
}

input.ui-button.ui-button-icon-only {
    text-indent: 0
}

.ui-button-icon-only .ui-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -8px;
    margin-left: -8px
}

.ui-button.ui-icon-notext .ui-icon {
    padding: 0;
    width: 2.1em;
    height: 2.1em;
    text-indent: -9999px;
    white-space: nowrap
}

input.ui-button.ui-icon-notext .ui-icon {
    width: auto;
    height: auto;
    text-indent: 0;
    white-space: normal;
    padding: .4em 1em
}

button.ui-button::-moz-focus-inner,input.ui-button::-moz-focus-inner {
    border: 0;
    padding: 0
}

.ui-controlgroup {
    vertical-align: middle;
    display: inline-block
}

.ui-controlgroup>.ui-controlgroup-item {
    float: left;
    margin-left: 0;
    margin-right: 0
}

.ui-controlgroup>.ui-controlgroup-item.ui-visual-focus,.ui-controlgroup>.ui-controlgroup-item:focus {
    z-index: 9999
}

.ui-controlgroup-vertical>.ui-controlgroup-item {
    display: block;
    float: none;
    width: 100%;
    margin-top: 0;
    margin-bottom: 0;
    text-align: left
}

.ui-controlgroup-vertical .ui-controlgroup-item {
    box-sizing: border-box
}

.ui-controlgroup .ui-controlgroup-label {
    padding: .4em 1em
}

.ui-controlgroup .ui-controlgroup-label span {
    font-size: 80%
}

.ui-controlgroup-horizontal .ui-controlgroup-label+.ui-controlgroup-item {
    border-left: none
}

.ui-controlgroup-vertical .ui-controlgroup-label+.ui-controlgroup-item {
    border-top: none
}

.ui-controlgroup-horizontal .ui-controlgroup-label.ui-widget-content {
    border-right: none
}

.ui-controlgroup-vertical .ui-controlgroup-label.ui-widget-content {
    border-bottom: none
}

.ui-controlgroup-vertical .ui-spinner-input {
    width: 75%;
    width: calc(100% - 2.4em)
}

.ui-controlgroup-vertical .ui-spinner .ui-spinner-up {
    border-top-style: solid
}

.ui-checkboxradio-label .ui-icon-background {
    box-shadow: inset 1px 1px 1px #ccc;
    border-radius: .12em;
    border: none
}

.ui-checkboxradio-radio-label .ui-icon-background {
    width: 16px;
    height: 16px;
    border-radius: 1em;
    overflow: visible;
    border: none
}

.ui-checkboxradio-radio-label.ui-checkboxradio-checked .ui-icon,.ui-checkboxradio-radio-label.ui-checkboxradio-checked:hover .ui-icon {
    background-image: none;
    width: 8px;
    height: 8px;
    border-width: 4px;
    border-style: solid
}

.ui-checkboxradio-disabled {
    pointer-events: none
}

.ui-datepicker {
    width: 17em;
    padding: .2em .2em 0;
    display: none
}

.ui-datepicker .ui-datepicker-header {
    position: relative;
    padding: .2em 0
}

.ui-datepicker .ui-datepicker-next,.ui-datepicker .ui-datepicker-prev {
    position: absolute;
    top: 2px;
    width: 1.8em;
    height: 1.8em
}

.ui-datepicker .ui-datepicker-next-hover,.ui-datepicker .ui-datepicker-prev-hover {
    top: 1px
}

.ui-datepicker .ui-datepicker-prev {
    left: 2px
}

.ui-datepicker .ui-datepicker-next {
    right: 2px
}

.ui-datepicker .ui-datepicker-prev-hover {
    left: 1px
}

.ui-datepicker .ui-datepicker-next-hover {
    right: 1px
}

.ui-datepicker .ui-datepicker-next span,.ui-datepicker .ui-datepicker-prev span {
    display: block;
    position: absolute;
    left: 50%;
    margin-left: -8px;
    top: 50%;
    margin-top: -8px
}

.ui-datepicker .ui-datepicker-title {
    margin: 0 2.3em;
    line-height: 1.8em;
    text-align: center
}

.ui-datepicker .ui-datepicker-title select {
    font-size: 1em;
    margin: 1px 0
}

.ui-datepicker select.ui-datepicker-month,.ui-datepicker select.ui-datepicker-year {
    width: 45%
}

.ui-datepicker table {
    width: 100%;
    font-size: .9em;
    border-collapse: collapse;
    margin: 0 0 .4em
}

.ui-datepicker th {
    padding: .7em .3em;
    text-align: center;
    font-weight: 700;
    border: 0
}

.ui-datepicker td {
    border: 0;
    padding: 1px
}

.ui-datepicker td a,.ui-datepicker td span {
    display: block;
    padding: .2em;
    text-align: right;
    text-decoration: none
}

.ui-datepicker .ui-datepicker-buttonpane {
    background-image: none;
    margin: .7em 0 0 0;
    padding: 0 .2em;
    border-left: 0;
    border-right: 0;
    border-bottom: 0
}

.ui-datepicker .ui-datepicker-buttonpane button {
    float: right;
    margin: .5em .2em .4em;
    cursor: pointer;
    padding: .2em .6em .3em .6em;
    width: auto;
    overflow: visible
}

.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
    float: left
}

.ui-datepicker.ui-datepicker-multi {
    width: auto
}

.ui-datepicker-multi .ui-datepicker-group {
    float: left
}

.ui-datepicker-multi .ui-datepicker-group table {
    width: 95%;
    margin: 0 auto .4em
}

.ui-datepicker-multi-2 .ui-datepicker-group {
    width: 50%
}

.ui-datepicker-multi-3 .ui-datepicker-group {
    width: 33.3%
}

.ui-datepicker-multi-4 .ui-datepicker-group {
    width: 25%
}

.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
    border-left-width: 0
}

.ui-datepicker-multi .ui-datepicker-buttonpane {
    clear: left
}

.ui-datepicker-row-break {
    clear: both;
    width: 100%;
    font-size: 0
}

.ui-datepicker-rtl {
    direction: rtl
}

.ui-datepicker-rtl .ui-datepicker-prev {
    right: 2px;
    left: auto
}

.ui-datepicker-rtl .ui-datepicker-next {
    left: 2px;
    right: auto
}

.ui-datepicker-rtl .ui-datepicker-prev:hover {
    right: 1px;
    left: auto
}

.ui-datepicker-rtl .ui-datepicker-next:hover {
    left: 1px;
    right: auto
}

.ui-datepicker-rtl .ui-datepicker-buttonpane {
    clear: right
}

.ui-datepicker-rtl .ui-datepicker-buttonpane button {
    float: left
}

.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current,.ui-datepicker-rtl .ui-datepicker-group {
    float: right
}

.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header,.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
    border-right-width: 0;
    border-left-width: 1px
}

.ui-datepicker .ui-icon {
    display: block;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat;
    left: .5em;
    top: .3em
}

.ui-dialog {
    position: absolute;
    top: 0;
    left: 0;
    padding: .2em;
    outline: 0
}

.ui-dialog .ui-dialog-titlebar {
    padding: .4em 1em;
    position: relative
}

.ui-dialog .ui-dialog-title {
    float: left;
    margin: .1em 0;
    white-space: nowrap;
    width: 90%;
    overflow: hidden;
    text-overflow: ellipsis
}

.ui-dialog .ui-dialog-titlebar-close {
    position: absolute;
    right: .3em;
    top: 50%;
    width: 20px;
    margin: -10px 0 0 0;
    padding: 1px;
    height: 20px
}

.ui-dialog .ui-dialog-content {
    position: relative;
    border: 0;
    padding: .5em 1em;
    background: 0 0;
    overflow: auto
}

.ui-dialog .ui-dialog-buttonpane {
    text-align: left;
    border-width: 1px 0 0 0;
    background-image: none;
    margin-top: .5em;
    padding: .3em 1em .5em .4em
}

.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
    float: right
}

.ui-dialog .ui-dialog-buttonpane button {
    margin: .5em .4em .5em 0;
    cursor: pointer
}

.ui-dialog .ui-resizable-n {
    height: 2px;
    top: 0
}

.ui-dialog .ui-resizable-e {
    width: 2px;
    right: 0
}

.ui-dialog .ui-resizable-s {
    height: 2px;
    bottom: 0
}

.ui-dialog .ui-resizable-w {
    width: 2px;
    left: 0
}

.ui-dialog .ui-resizable-ne,.ui-dialog .ui-resizable-nw,.ui-dialog .ui-resizable-se,.ui-dialog .ui-resizable-sw {
    width: 7px;
    height: 7px
}

.ui-dialog .ui-resizable-se {
    right: 0;
    bottom: 0
}

.ui-dialog .ui-resizable-sw {
    left: 0;
    bottom: 0
}

.ui-dialog .ui-resizable-ne {
    right: 0;
    top: 0
}

.ui-dialog .ui-resizable-nw {
    left: 0;
    top: 0
}

.ui-draggable .ui-dialog-titlebar {
    cursor: move
}

.ui-draggable-handle {
    -ms-touch-action: none;
    touch-action: none
}

.ui-resizable {
    position: relative
}

.ui-resizable-handle {
    position: absolute;
    font-size: .1px;
    display: block;
    -ms-touch-action: none;
    touch-action: none
}

.ui-resizable-autohide .ui-resizable-handle,.ui-resizable-disabled .ui-resizable-handle {
    display: none
}

.ui-resizable-n {
    cursor: n-resize;
    height: 7px;
    width: 100%;
    top: -5px;
    left: 0
}

.ui-resizable-s {
    cursor: s-resize;
    height: 7px;
    width: 100%;
    bottom: -5px;
    left: 0
}

.ui-resizable-e {
    cursor: e-resize;
    width: 7px;
    right: -5px;
    top: 0;
    height: 100%
}

.ui-resizable-w {
    cursor: w-resize;
    width: 7px;
    left: -5px;
    top: 0;
    height: 100%
}

.ui-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px
}

.ui-resizable-sw {
    cursor: sw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    bottom: -5px
}

.ui-resizable-nw {
    cursor: nw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    top: -5px
}

.ui-resizable-ne {
    cursor: ne-resize;
    width: 9px;
    height: 9px;
    right: -5px;
    top: -5px
}

.ui-progressbar {
    height: 2em;
    text-align: left;
    overflow: hidden
}

.ui-progressbar .ui-progressbar-value {
    margin: -1px;
    height: 100%
}

.ui-progressbar .ui-progressbar-overlay {
    background: url(data:image/gif;base64,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);
    height: 100%;
    opacity: .25
}

.ui-progressbar-indeterminate .ui-progressbar-value {
    background-image: none
}

.ui-selectable {
    -ms-touch-action: none;
    touch-action: none
}

.ui-selectable-helper {
    position: absolute;
    z-index: 100;
    border: 1px dotted #000
}

.ui-selectmenu-menu {
    padding: 0;
    margin: 0;
    position: absolute;
    top: 0;
    left: 0;
    display: none
}

.ui-selectmenu-menu .ui-menu {
    overflow: auto;
    overflow-x: hidden;
    padding-bottom: 1px
}

.ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
    font-size: 1em;
    font-weight: 700;
    line-height: 1.5;
    padding: 2px .4em;
    margin: .5em 0 0 0;
    height: auto;
    border: 0
}

.ui-selectmenu-open {
    display: block
}

.ui-selectmenu-text {
    display: block;
    margin-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis
}

.ui-selectmenu-button.ui-button {
    text-align: left;
    white-space: nowrap;
    width: 14em
}

.ui-selectmenu-icon.ui-icon {
    float: right;
    margin-top: 0
}

.ui-slider {
    position: relative;
    text-align: left
}

.ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 1.2em;
    height: 1.2em;
    cursor: default;
    -ms-touch-action: none;
    touch-action: none
}

.ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size: .7em;
    display: block;
    border: 0;
    background-position: 0 0
}

.ui-slider.ui-state-disabled .ui-slider-handle,.ui-slider.ui-state-disabled .ui-slider-range {
    filter: inherit
}

.ui-slider-horizontal {
    height: .8em
}

.ui-slider-horizontal .ui-slider-handle {
    top: -.3em;
    margin-left: -.6em
}

.ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%
}

.ui-slider-horizontal .ui-slider-range-min {
    left: 0
}

.ui-slider-horizontal .ui-slider-range-max {
    right: 0
}

.ui-slider-vertical {
    width: .8em;
    height: 100px
}

.ui-slider-vertical .ui-slider-handle {
    left: -.3em;
    margin-left: 0;
    margin-bottom: -.6em
}

.ui-slider-vertical .ui-slider-range {
    left: 0;
    width: 100%
}

.ui-slider-vertical .ui-slider-range-min {
    bottom: 0
}

.ui-slider-vertical .ui-slider-range-max {
    top: 0
}

.ui-sortable-handle {
    -ms-touch-action: none;
    touch-action: none
}

.ui-spinner {
    position: relative;
    display: inline-block;
    overflow: hidden;
    padding: 0;
    vertical-align: middle
}

.ui-spinner-input {
    border: none;
    background: 0 0;
    color: inherit;
    padding: .222em 0;
    margin: .2em 0;
    vertical-align: middle;
    margin-left: .4em;
    margin-right: 2em
}

.ui-spinner-button {
    width: 1.6em;
    height: 50%;
    font-size: .5em;
    padding: 0;
    margin: 0;
    text-align: center;
    position: absolute;
    cursor: default;
    display: block;
    overflow: hidden;
    right: 0
}

.ui-spinner a.ui-spinner-button {
    border-top-style: none;
    border-bottom-style: none;
    border-right-style: none
}

.ui-spinner-up {
    top: 0
}

.ui-spinner-down {
    bottom: 0
}

.ui-tabs {
    position: relative;
    padding: .2em
}

.ui-tabs .ui-tabs-nav {
    margin: 0;
    padding: .2em .2em 0
}

.ui-tabs .ui-tabs-nav li {
    list-style: none;
    float: left;
    position: relative;
    top: 0;
    margin: 1px .2em 0 0;
    border-bottom-width: 0;
    padding: 0;
    white-space: nowrap
}

.ui-tabs .ui-tabs-nav .ui-tabs-anchor {
    float: left;
    padding: .5em 1em;
    text-decoration: none
}

.ui-tabs .ui-tabs-nav li.ui-tabs-active {
    margin-bottom: -1px;
    padding-bottom: 1px
}

.ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor,.ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor,.ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
    cursor: text
}

.ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor {
    cursor: pointer
}

.ui-tabs .ui-tabs-panel {
    display: block;
    border-width: 0;
    padding: 1em 1.4em;
    background: 0 0
}

.ui-tooltip {
    padding: 8px;
    position: absolute;
    z-index: 9999;
    max-width: 300px
}

body .ui-tooltip {
    border-width: 2px
}

.ui-widget {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em
}

.ui-widget .ui-widget {
    font-size: 1em
}

.ui-widget button,.ui-widget input,.ui-widget select,.ui-widget textarea {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em
}

.ui-widget.ui-widget-content {
    border: 1px solid #c5c5c5
}

.ui-widget-content {
    border: 1px solid #ddd;
    background: #fff;
    color: #333
}

.ui-widget-content a {
    color: #333
}

.ui-widget-header {
    border: 1px solid #ddd;
    background: #e9e9e9;
    color: #333;
    font-weight: 700
}

.ui-widget-header a {
    color: #333
}

.ui-button,.ui-state-default,.ui-widget-content .ui-state-default,.ui-widget-header .ui-state-default,html .ui-button.ui-state-disabled:active,html .ui-button.ui-state-disabled:hover {
    border: 1px solid #c5c5c5;
    background: #f6f6f6;
    font-weight: 400;
    color: #454545
}

.ui-button,.ui-state-default a,.ui-state-default a:link,.ui-state-default a:visited,a.ui-button,a:link.ui-button,a:visited.ui-button {
    color: #454545;
    text-decoration: none
}

.ui-button:focus,.ui-button:hover,.ui-state-focus,.ui-state-hover,.ui-widget-content .ui-state-focus,.ui-widget-content .ui-state-hover,.ui-widget-header .ui-state-focus,.ui-widget-header .ui-state-hover {
    border: 1px solid #ccc;
    background: #ededed;
    font-weight: 400;
    color: #2b2b2b
}

.ui-state-focus a,.ui-state-focus a:hover,.ui-state-focus a:link,.ui-state-focus a:visited,.ui-state-hover a,.ui-state-hover a:hover,.ui-state-hover a:link,.ui-state-hover a:visited,a.ui-button:focus,a.ui-button:hover {
    color: #2b2b2b;
    text-decoration: none
}

.ui-visual-focus {
    box-shadow: 0 0 3px 1px #5e9ed6
}

.ui-button.ui-state-active:hover,.ui-button:active,.ui-state-active,.ui-widget-content .ui-state-active,.ui-widget-header .ui-state-active,a.ui-button:active {
    border: 1px solid #003eff;
    background: #007fff;
    font-weight: 400;
    color: #fff
}

.ui-icon-background,.ui-state-active .ui-icon-background {
    border: #003eff;
    background-color: #fff
}

.ui-state-active a,.ui-state-active a:link,.ui-state-active a:visited {
    color: #fff;
    text-decoration: none
}

.ui-state-highlight,.ui-widget-content .ui-state-highlight,.ui-widget-header .ui-state-highlight {
    border: 1px solid #dad55e;
    background: #fffa90;
    color: #777620
}

.ui-state-checked {
    border: 1px solid #dad55e;
    background: #fffa90
}

.ui-state-highlight a,.ui-widget-content .ui-state-highlight a,.ui-widget-header .ui-state-highlight a {
    color: #777620
}

.ui-state-error,.ui-widget-content .ui-state-error,.ui-widget-header .ui-state-error {
    border: 1px solid #f1a899;
    background: #fddfdf;
    color: #5f3f3f
}

.ui-state-error a,.ui-widget-content .ui-state-error a,.ui-widget-header .ui-state-error a {
    color: #5f3f3f
}

.ui-state-error-text,.ui-widget-content .ui-state-error-text,.ui-widget-header .ui-state-error-text {
    color: #5f3f3f
}

.ui-priority-primary,.ui-widget-content .ui-priority-primary,.ui-widget-header .ui-priority-primary {
    font-weight: 700
}

.ui-priority-secondary,.ui-widget-content .ui-priority-secondary,.ui-widget-header .ui-priority-secondary {
    opacity: .7;
    filter: Alpha(Opacity=70);
    font-weight: 400
}

.ui-state-disabled,.ui-widget-content .ui-state-disabled,.ui-widget-header .ui-state-disabled {
    opacity: .35;
    filter: Alpha(Opacity=35);
    background-image: none
}

.ui-state-disabled .ui-icon {
    filter: Alpha(Opacity=35)
}

.ui-icon {
    width: 16px;
    height: 16px
}

.ui-icon,.ui-widget-content .ui-icon {
    background-image: url(images/ui-icons_444444_256x240.png)
}

.ui-widget-header .ui-icon {
    background-image: url(images/ui-icons_444444_256x240.png)
}

.ui-button:focus .ui-icon,.ui-button:hover .ui-icon,.ui-state-focus .ui-icon,.ui-state-hover .ui-icon {
    background-image: url(images/ui-icons_555555_256x240.png)
}

.ui-button:active .ui-icon,.ui-state-active .ui-icon {
    background-image: url(images/ui-icons_ffffff_256x240.png)
}

.ui-button .ui-state-highlight.ui-icon,.ui-state-highlight .ui-icon {
    background-image: url(images/ui-icons_777620_256x240.png)
}

.ui-state-error .ui-icon,.ui-state-error-text .ui-icon {
    background-image: url(images/ui-icons_cc0000_256x240.png)
}

.ui-button .ui-icon {
    background-image: url(images/ui-icons_777777_256x240.png)
}

.ui-icon-blank {
    background-position: 16px 16px
}

.ui-icon-caret-1-n {
    background-position: 0 0
}

.ui-icon-caret-1-ne {
    background-position: -16px 0
}

.ui-icon-caret-1-e {
    background-position: -32px 0
}

.ui-icon-caret-1-se {
    background-position: -48px 0
}

.ui-icon-caret-1-s {
    background-position: -65px 0
}

.ui-icon-caret-1-sw {
    background-position: -80px 0
}

.ui-icon-caret-1-w {
    background-position: -96px 0
}

.ui-icon-caret-1-nw {
    background-position: -112px 0
}

.ui-icon-caret-2-n-s {
    background-position: -128px 0
}

.ui-icon-caret-2-e-w {
    background-position: -144px 0
}

.ui-icon-triangle-1-n {
    background-position: 0 -16px
}

.ui-icon-triangle-1-ne {
    background-position: -16px -16px
}

.ui-icon-triangle-1-e {
    background-position: -32px -16px
}

.ui-icon-triangle-1-se {
    background-position: -48px -16px
}

.ui-icon-triangle-1-s {
    background-position: -65px -16px
}

.ui-icon-triangle-1-sw {
    background-position: -80px -16px
}

.ui-icon-triangle-1-w {
    background-position: -96px -16px
}

.ui-icon-triangle-1-nw {
    background-position: -112px -16px
}

.ui-icon-triangle-2-n-s {
    background-position: -128px -16px
}

.ui-icon-triangle-2-e-w {
    background-position: -144px -16px
}

.ui-icon-arrow-1-n {
    background-position: 0 -32px
}

.ui-icon-arrow-1-ne {
    background-position: -16px -32px
}

.ui-icon-arrow-1-e {
    background-position: -32px -32px
}

.ui-icon-arrow-1-se {
    background-position: -48px -32px
}

.ui-icon-arrow-1-s {
    background-position: -65px -32px
}

.ui-icon-arrow-1-sw {
    background-position: -80px -32px
}

.ui-icon-arrow-1-w {
    background-position: -96px -32px
}

.ui-icon-arrow-1-nw {
    background-position: -112px -32px
}

.ui-icon-arrow-2-n-s {
    background-position: -128px -32px
}

.ui-icon-arrow-2-ne-sw {
    background-position: -144px -32px
}

.ui-icon-arrow-2-e-w {
    background-position: -160px -32px
}

.ui-icon-arrow-2-se-nw {
    background-position: -176px -32px
}

.ui-icon-arrowstop-1-n {
    background-position: -192px -32px
}

.ui-icon-arrowstop-1-e {
    background-position: -208px -32px
}

.ui-icon-arrowstop-1-s {
    background-position: -224px -32px
}

.ui-icon-arrowstop-1-w {
    background-position: -240px -32px
}

.ui-icon-arrowthick-1-n {
    background-position: 1px -48px
}

.ui-icon-arrowthick-1-ne {
    background-position: -16px -48px
}

.ui-icon-arrowthick-1-e {
    background-position: -32px -48px
}

.ui-icon-arrowthick-1-se {
    background-position: -48px -48px
}

.ui-icon-arrowthick-1-s {
    background-position: -64px -48px
}

.ui-icon-arrowthick-1-sw {
    background-position: -80px -48px
}

.ui-icon-arrowthick-1-w {
    background-position: -96px -48px
}

.ui-icon-arrowthick-1-nw {
    background-position: -112px -48px
}

.ui-icon-arrowthick-2-n-s {
    background-position: -128px -48px
}

.ui-icon-arrowthick-2-ne-sw {
    background-position: -144px -48px
}

.ui-icon-arrowthick-2-e-w {
    background-position: -160px -48px
}

.ui-icon-arrowthick-2-se-nw {
    background-position: -176px -48px
}

.ui-icon-arrowthickstop-1-n {
    background-position: -192px -48px
}

.ui-icon-arrowthickstop-1-e {
    background-position: -208px -48px
}

.ui-icon-arrowthickstop-1-s {
    background-position: -224px -48px
}

.ui-icon-arrowthickstop-1-w {
    background-position: -240px -48px
}

.ui-icon-arrowreturnthick-1-w {
    background-position: 0 -64px
}

.ui-icon-arrowreturnthick-1-n {
    background-position: -16px -64px
}

.ui-icon-arrowreturnthick-1-e {
    background-position: -32px -64px
}

.ui-icon-arrowreturnthick-1-s {
    background-position: -48px -64px
}

.ui-icon-arrowreturn-1-w {
    background-position: -64px -64px
}

.ui-icon-arrowreturn-1-n {
    background-position: -80px -64px
}

.ui-icon-arrowreturn-1-e {
    background-position: -96px -64px
}

.ui-icon-arrowreturn-1-s {
    background-position: -112px -64px
}

.ui-icon-arrowrefresh-1-w {
    background-position: -128px -64px
}

.ui-icon-arrowrefresh-1-n {
    background-position: -144px -64px
}

.ui-icon-arrowrefresh-1-e {
    background-position: -160px -64px
}

.ui-icon-arrowrefresh-1-s {
    background-position: -176px -64px
}

.ui-icon-arrow-4 {
    background-position: 0 -80px
}

.ui-icon-arrow-4-diag {
    background-position: -16px -80px
}

.ui-icon-extlink {
    background-position: -32px -80px
}

.ui-icon-newwin {
    background-position: -48px -80px
}

.ui-icon-refresh {
    background-position: -64px -80px
}

.ui-icon-shuffle {
    background-position: -80px -80px
}

.ui-icon-transfer-e-w {
    background-position: -96px -80px
}

.ui-icon-transferthick-e-w {
    background-position: -112px -80px
}

.ui-icon-folder-collapsed {
    background-position: 0 -96px
}

.ui-icon-folder-open {
    background-position: -16px -96px
}

.ui-icon-document {
    background-position: -32px -96px
}

.ui-icon-document-b {
    background-position: -48px -96px
}

.ui-icon-note {
    background-position: -64px -96px
}

.ui-icon-mail-closed {
    background-position: -80px -96px
}

.ui-icon-mail-open {
    background-position: -96px -96px
}

.ui-icon-suitcase {
    background-position: -112px -96px
}

.ui-icon-comment {
    background-position: -128px -96px
}

.ui-icon-person {
    background-position: -144px -96px
}

.ui-icon-print {
    background-position: -160px -96px
}

.ui-icon-trash {
    background-position: -176px -96px
}

.ui-icon-locked {
    background-position: -192px -96px
}

.ui-icon-unlocked {
    background-position: -208px -96px
}

.ui-icon-bookmark {
    background-position: -224px -96px
}

.ui-icon-tag {
    background-position: -240px -96px
}

.ui-icon-home {
    background-position: 0 -112px
}

.ui-icon-flag {
    background-position: -16px -112px
}

.ui-icon-calendar {
    background-position: -32px -112px
}

.ui-icon-cart {
    background-position: -48px -112px
}

.ui-icon-pencil {
    background-position: -64px -112px
}

.ui-icon-clock {
    background-position: -80px -112px
}

.ui-icon-disk {
    background-position: -96px -112px
}

.ui-icon-calculator {
    background-position: -112px -112px
}

.ui-icon-zoomin {
    background-position: -128px -112px
}

.ui-icon-zoomout {
    background-position: -144px -112px
}

.ui-icon-search {
    background-position: -160px -112px
}

.ui-icon-wrench {
    background-position: -176px -112px
}

.ui-icon-gear {
    background-position: -192px -112px
}

.ui-icon-heart {
    background-position: -208px -112px
}

.ui-icon-star {
    background-position: -224px -112px
}

.ui-icon-link {
    background-position: -240px -112px
}

.ui-icon-cancel {
    background-position: 0 -128px
}

.ui-icon-plus {
    background-position: -16px -128px
}

.ui-icon-plusthick {
    background-position: -32px -128px
}

.ui-icon-minus {
    background-position: -48px -128px
}

.ui-icon-minusthick {
    background-position: -64px -128px
}

.ui-icon-close {
    background-position: -80px -128px
}

.ui-icon-closethick {
    background-position: -96px -128px
}

.ui-icon-key {
    background-position: -112px -128px
}

.ui-icon-lightbulb {
    background-position: -128px -128px
}

.ui-icon-scissors {
    background-position: -144px -128px
}

.ui-icon-clipboard {
    background-position: -160px -128px
}

.ui-icon-copy {
    background-position: -176px -128px
}

.ui-icon-contact {
    background-position: -192px -128px
}

.ui-icon-image {
    background-position: -208px -128px
}

.ui-icon-video {
    background-position: -224px -128px
}

.ui-icon-script {
    background-position: -240px -128px
}

.ui-icon-alert {
    background-position: 0 -144px
}

.ui-icon-info {
    background-position: -16px -144px
}

.ui-icon-notice {
    background-position: -32px -144px
}

.ui-icon-help {
    background-position: -48px -144px
}

.ui-icon-check {
    background-position: -64px -144px
}

.ui-icon-bullet {
    background-position: -80px -144px
}

.ui-icon-radio-on {
    background-position: -96px -144px
}

.ui-icon-radio-off {
    background-position: -112px -144px
}

.ui-icon-pin-w {
    background-position: -128px -144px
}

.ui-icon-pin-s {
    background-position: -144px -144px
}

.ui-icon-play {
    background-position: 0 -160px
}

.ui-icon-pause {
    background-position: -16px -160px
}

.ui-icon-seek-next {
    background-position: -32px -160px
}

.ui-icon-seek-prev {
    background-position: -48px -160px
}

.ui-icon-seek-end {
    background-position: -64px -160px
}

.ui-icon-seek-start {
    background-position: -80px -160px
}

.ui-icon-seek-first {
    background-position: -80px -160px
}

.ui-icon-stop {
    background-position: -96px -160px
}

.ui-icon-eject {
    background-position: -112px -160px
}

.ui-icon-volume-off {
    background-position: -128px -160px
}

.ui-icon-volume-on {
    background-position: -144px -160px
}

.ui-icon-power {
    background-position: 0 -176px
}

.ui-icon-signal-diag {
    background-position: -16px -176px
}

.ui-icon-signal {
    background-position: -32px -176px
}

.ui-icon-battery-0 {
    background-position: -48px -176px
}

.ui-icon-battery-1 {
    background-position: -64px -176px
}

.ui-icon-battery-2 {
    background-position: -80px -176px
}

.ui-icon-battery-3 {
    background-position: -96px -176px
}

.ui-icon-circle-plus {
    background-position: 0 -192px
}

.ui-icon-circle-minus {
    background-position: -16px -192px
}

.ui-icon-circle-close {
    background-position: -32px -192px
}

.ui-icon-circle-triangle-e {
    background-position: -48px -192px
}

.ui-icon-circle-triangle-s {
    background-position: -64px -192px
}

.ui-icon-circle-triangle-w {
    background-position: -80px -192px
}

.ui-icon-circle-triangle-n {
    background-position: -96px -192px
}

.ui-icon-circle-arrow-e {
    background-position: -112px -192px
}

.ui-icon-circle-arrow-s {
    background-position: -128px -192px
}

.ui-icon-circle-arrow-w {
    background-position: -144px -192px
}

.ui-icon-circle-arrow-n {
    background-position: -160px -192px
}

.ui-icon-circle-zoomin {
    background-position: -176px -192px
}

.ui-icon-circle-zoomout {
    background-position: -192px -192px
}

.ui-icon-circle-check {
    background-position: -208px -192px
}

.ui-icon-circlesmall-plus {
    background-position: 0 -208px
}

.ui-icon-circlesmall-minus {
    background-position: -16px -208px
}

.ui-icon-circlesmall-close {
    background-position: -32px -208px
}

.ui-icon-squaresmall-plus {
    background-position: -48px -208px
}

.ui-icon-squaresmall-minus {
    background-position: -64px -208px
}

.ui-icon-squaresmall-close {
    background-position: -80px -208px
}

.ui-icon-grip-dotted-vertical {
    background-position: 0 -224px
}

.ui-icon-grip-dotted-horizontal {
    background-position: -16px -224px
}

.ui-icon-grip-solid-vertical {
    background-position: -32px -224px
}

.ui-icon-grip-solid-horizontal {
    background-position: -48px -224px
}

.ui-icon-gripsmall-diagonal-se {
    background-position: -64px -224px
}

.ui-icon-grip-diagonal-se {
    background-position: -80px -224px
}

.ui-corner-all,.ui-corner-left,.ui-corner-tl,.ui-corner-top {
    border-top-left-radius: 3px
}

.ui-corner-all,.ui-corner-right,.ui-corner-top,.ui-corner-tr {
    border-top-right-radius: 3px
}

.ui-corner-all,.ui-corner-bl,.ui-corner-bottom,.ui-corner-left {
    border-bottom-left-radius: 3px
}

.ui-corner-all,.ui-corner-bottom,.ui-corner-br,.ui-corner-right {
    border-bottom-right-radius: 3px
}

.ui-widget-overlay {
    background: #aaa;
    opacity: .003;
    filter: Alpha(Opacity=.3)
}

.ui-widget-shadow {
    -webkit-box-shadow: 0 0 5px #666;
    box-shadow: 0 0 5px #666
}

.switch-button-label {
    float: left;
    font-size: 10pt;
    cursor: pointer
}

.switch-button-label.off {
    color: #adadad
}

.switch-button-label.on {
    color: #08c
}

.switch-button-background {
    float: left;
    position: relative;
    background: #ccc;
    border: 1px solid #aaa;
    margin: 1px 10px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    cursor: pointer
}

.switch-button-button {
    position: absolute;
    left: -1px;
    top: -1px;
    background: #fafafa;
    border: 1px solid #aaa;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px
}

:root {
    --main-tabs-min-width: var(--live-tabs-min-width);
    --main-tabs-min-height: 540px;
    --settings-tabs-min-width: 920px;
    --live-tabs-min-width: 1110px;
    --live-tabs-info-min-width: 480px;
    --live-tabs-info-left-margin: 10.5em;
    --live-tabs-preset-min-height: 493px;
    --control-tabs-min-width: 470px;
    --ptz-tabs-min-width: 478px;
    --sys-info-min-width: 478px;
    --main-hl-color: #ff4e05;
    --select-menu-bg-color: #232323;
    --text-default-color: #e7e7e7;
    --widget-bg-color: #171717;
    --widget-header-bg-color: #262626;
    --slider-handle-bg-color: #4f4d4d;
    --button-bg-color: #2f2f2f;
    --setting-dividor-bg-color: #bf4e05;
    --dialog-title-bg-color: #2f2f2f;
    --dialog-content-bg-color: #232323
}

.ui-tabs-panel input[type=text] {
    text-align: left
}

.ui-widget.ui-widget-content {
    border: 1px solid var(--widget-bg-color)
}

.ui-widget-content {
    border: 1px solid #3a3a3a;
    background: var(--widget-bg-color);
    color: var(--text-default-color)
}

.ui-widget-content a {
    color: #ccc
}

.ui-widget-header {
    border: 0 solid var(--widget-bg-color);
    background: var(--widget-header-bg-color);
    color: var(--text-default-color);
    font-weight: 700
}

.ui-button,.ui-state-default,.ui-widget-content .ui-state-default,.ui-widget-header .ui-state-default,html .ui-button.ui-state-disabled:active,html .ui-button.ui-state-disabled:hover {
    border: 1px solid var(--button-bg-color);
    background: var(--button-bg-color);
    font-weight: 400;
    color: var(--text-default-color)
}

.ui-button,.ui-state-default a,.ui-state-default a:link,.ui-state-default a:visited,a.ui-button,a:link.ui-button,a:visited.ui-button {
    color: var(--text-default-color);
    text-decoration: none
}

.ui-button:focus,.ui-button:hover,.ui-state-focus,.ui-state-hover,.ui-widget-content .ui-state-focus,.ui-widget-content .ui-state-hover,.ui-widget-header .ui-state-focus,.ui-widget-header .ui-state-hover {
    border: 1px solid #3a3a3a;
    background: #3a3a3a;
    font-weight: 400;
    color: var(--text-default-color)
}

.ui-button.ui-state-active:hover,.ui-button:active,.ui-state-active,.ui-widget-content .ui-state-active,.ui-widget-header .ui-state-active,a.ui-button:active {
    border: 1px solid var(--main-hl-color);
    background: var(--main-hl-color);
    font-weight: 400;
    color: var(--text-default-color)
}

.ui-widget.ui-widget-content.ui-slider {
    background: var(--button-bg-color);
    border: 0;
    max-width: 30em
}

.ui-dialog .ui-dialog-titlebar {
    background: var(--dialog-title-bg-color)
}

.ui-dialog .ui-dialog-content {
    background: var(--dialog-content-bg-color);
    padding: .5em 1em
}

.ui-dialog .ui-dialog-buttonpane {
    background: var(--dialog-content-bg-color);
    margin-top: 0;
    padding: .3em .3em .1em .3em
}

.ui-dialog .ui-dialog-buttonpane button {
    margin: .2em .4em .2em 0
}

.ui-dialog.ui-widget {
    border-color: var(--dialog-content-bg-color);
    background: var(--dialog-content-bg-color)
}

.ui-spinner {
    position: relative;
    display: inline-block;
    overflow: hidden;
    padding: 0;
    border: 1px solid var(--widget-bg-color)!important;
    vertical-align: middle;
    border-radius: 0;
    cursor: pointer
}

.ui-spinner-input {
    width: 15.55em!important;
    padding: 0;
    margin: 0;
    height: 2.15em;
    background-color: var(--button-bg-color)!important;
    border: 0 solid var(--button-bg-color)!important
}

.ui-spinner-up {
    width: 1.7em;
    height: 100%;
    padding: 0;
    text-align: center;
    position: absolute;
    cursor: pointer;
    display: block;
    overflow: hidden;
    right: 0;
    border-left: 1.5px solid var(--widget-bg-color)!important;
    background: var(--button-bg-color);
    border-radius: 0
}

.ui-spinner-down {
    width: 1.7em;
    height: 100%;
    padding: 0;
    text-align: center;
    position: absolute;
    cursor: pointer;
    display: block;
    overflow: hidden;
    left: 0;
    border-right: 1.5px solid var(--widget-bg-color)!important;
    background: var(--button-bg-color);
    border-radius: 0
}

.ui-selectmenu-button.ui-button {
    text-align: left;
    white-space: nowrap;
    width: 13.5em;
    height: 17px;
    line-height: 17px
}

.framing-selectmenu {
    text-align: left;
    white-space: nowrap;
    width: 9em!important;
    height: 15px!important;
    line-height: 15px!important
}

.framing-segment {
    width: 11em!important;
    height: 28px!important
}

.framing-slider-spinner {
    width: 11em!important;
    height: 28px!important
}

.framing-slider {
    width: 45%!important
}

.framing-optionmenu {
    width: 7.5em!important
}

.framing_target_num_tips {
    position: relative;
    z-index: 5;
    width: 4em;
    height: 1.5em;
    margin-top: -3em;
    margin-left: 12em;
    text-align: end
}

.input_button {
    width: 13.5em
}

hr {
    height: 0;
    line-height: 0;
    margin: 10px 0;
    padding: 0;
    border: none!important;
    border-bottom: 1px solid #eee!important;
    clear: both;
    overflow: hidden;
    background: 0 0
}

.camui-border-red {
    width: 80%;
    border-width: 1px;
    border-style: solid;
    border-color: var(--setting-dividor-bg-color)!important;
    color: var(--setting-dividor-bg-color)!important;
    margin-bottom: 0
}

.ui-selectmenu-menu .ui-menu {
    max-height: 22em;
    background: var(--select-menu-bg-color)
}

:focus {
    outline: 0
}

body,html {
    margin: 0;
    height: 100%;
    font-size: 14px;
    font-family: Arial,Helvetica,sans-serif;
    background: #000
}

#main_tabs {
    position: relative;
    width: 100%;
    height: 100%;
    background: #000;
    border: 0;
    padding: 0;
    min-width: var(--main-tabs-min-width);
    min-height: var(--main-tabs-min-height)
}

#control {
    position: relative;
    height: 100%;
    float: right;
    margin-left: 2px;
    width: 478px
}

#control_tabs {
    border: 0;
    min-width: 470px
}

#tabs-ctrl-af,#tabs-ctrl-exp,#tabs-ctrl-img,#tabs-ctrl-project,#tabs-ctrl-wb {
    min-height: 110px;
    padding: 1em .9em
}

#preview {
    position: relative;
    height: 100%;
    min-height: 300px;
    float: left;
    background-color: #000;
    margin: 0 auto
}

#stream {
    position: relative;
    width: 100%
}

#rec_tabs {
    position: absolute;
    min-width: var(--live-tabs-info-min-width);
    height: 4em;
    bottom: 2em
}

#shortcut_tools_bar {
    display: flex;
    align-items: center;
    position: absolute;
    z-index: 4;
    min-width: var(--live-tabs-info-min-width);
    height: 2em;
    bottom: 0;
    background: var(--widget-bg-color);
    margin: 0 .1em .5em .1em
}

#info_bar {
    display: flex;
    position: absolute;
    z-index: 4;
    top: 0;
    color: var(--text-default-color);
    margin-top: 3.5em
}

#info_bar :not(:nth-child(1)) {
    margin-left: 1em
}

#info_bar :first-child {
    margin-left: 1em
}

#info_iris {
    width: 2.2em
}

#info_sht {
    width: 3em
}

#info_iso {
    width: 5.5em
}

#info_ev_bar {
    width: 3em
}

#info_ev_bar>svg {
    width: 1em;
    height: 1em;
    margin-left: 0!important
}

#info_ev {
    margin-left: 0!important
}

#info_pre_roll {
    display: none;
    color: red;
    margin: .6em .6em 0 0
}

#info_duration {
    visibility: hidden
}

#info_remain {
    visibility: hidden
}

.pixelLink_mode {
    float: right;
    position: absolute;
    right: 18em!important;
    visibility: hidden;
    margin-top: 2.8em;
    font-size: .9em!important
}

.ezLink_mode {
    float: right;
    position: absolute;
    right: 17.2em!important;
    visibility: hidden;
    margin-top: 2.8em;
    font-size: .9em!important
}

.link_mode {
    float: right;
    position: absolute;
    right: 17.7em!important;
    visibility: hidden;
    margin-top: 2.8em;
    font-size: .9em!important
}

.master_mode {
    background-color: #0f0;
    color: #000
}

.slave_mode {
    background-color: #ff0;
    color: #000
}

#info_link {
    float: right;
    position: absolute;
    right: 13.6em!important;
    visibility: hidden;
    color: #ff0;
    margin-top: 2.8em;
    font-size: .9em
}

#info_link.linked {
    color: green
}

#info_temp {
    float: right;
    position: absolute;
    z-index: 4;
    right: 70px
}

#info_bat {
    float: right;
    position: absolute;
    z-index: 4;
    right: 1em
}

#info_card {
    float: right;
    position: absolute;
    z-index: 4;
    right: 150px;
    color: red;
    margin: 1.3em 0 0 0;
    visibility: hidden
}

#info_bar_bottom {
    display: flex;
    position: absolute;
    z-index: 4;
    color: var(--text-default-color);
    align-items: flex-end
}

#info_bar_bottom :not(:nth-child(1)) {
    margin-left: 1em
}

#info_bar_bottom :first-child {
    margin-left: 1em
}

.info_stream {
    font-size: 12px;
    margin: 10px;
    color: var(--text-default-color)
}

#info_res {
    min-width: 5em
}

#info_vfr {
    min-width: 4em
}

#info_vencoder {
    float: right;
    position: absolute;
    z-index: 4;
    right: 130px
}

#info_img_profile {
    float: right;
    position: absolute;
    z-index: 4;
    right: 1em
}

#info_video_stream_status {
    position: absolute;
    z-index: 4;
    top: 50%;
    left: 50%;
    width: 425px;
    transform: translate(-50%,-50%);
    color: var(--text-default-color)
}

#info_bar_rec {
    display: block;
    float: right;
    position: absolute;
    z-index: 4;
    right: 105px
}

#info_rec {
    display: flex
}

#info_rec_indicator {
    background: red;
    border-radius: 50%;
    width: 1em;
    height: 1em;
    margin-top: 10px;
    visibility: hidden
}

#info_duration {
    font-size: 12px;
    margin: 10px 0 0 5px
}

#info_remain {
    float: right;
    font-size: 12px;
    margin-top: 10px
}

.info {
    display: flex;
    font-size: medium;
    align-items: center
}

#btn_rec {
    display: flex;
    align-items: center;
    float: right;
    border: none;
    margin: 0 0 0 auto;
    background: var(--widget-bg-color);
    padding: .4em
}

#btn_cap {
    display: flex;
    align-items: center;
    position: absolute;
    border: none;
    right: 55px;
    background: var(--widget-bg-color);
    padding: .4em
}

#btn_cap>svg {
    fill: var(--text-default-color);
    fill-rule: evenodd
}

.svg_press {
    fill: var(--main-hl-color)!important;
    fill-rule: evenodd
}

label {
    display: block;
    margin: 10px 0 4px 0
}

.slider {
    margin: 10px 0 0 0;
    margin-left: 1.25em;
    margin-right: 1.25em
}

.slider .ui-slider-handle {
    width: 2.5em;
    height: 1.6em;
    top: 50%;
    margin-top: -.8em;
    margin-left: -1.25em;
    text-align: center;
    line-height: 1.6em
}

.ui-slider-handle {
    cursor: pointer!important
}

.slider_focus {
    display: flex;
    flex: 1;
    margin: .8em 0 0 0
}

.slider_focus .ui-slider-handle {
    width: 1.2em;
    height: 1.2em;
    top: 50%;
    margin-top: -.65em;
    margin-left: -.6em;
    text-align: center;
    line-height: 1.6em;
    background: var(--slider-handle-bg-color)!important
}

.slider_focus .ui-slider-handle:focus {
    background-color: var(--main-hl-color)!important
}

#wb_kelvin-handle {
    width: 4em;
    margin-left: -2em
}

#lens_focus_pos-handle #lens_focal_length-handle {
    width: 3.5em;
    margin-left: -1.75em
}

.slider_container {
    display: flex
}

#zoom_range {
    margin-top: .65em
}

#focus_range {
    margin-top: .65em
}

.slider_icon_minus {
    cursor: pointer;
    font-size: 1.4em;
    padding: .3em 1.1em 0 .4em
}

.slider_icon_plus {
    cursor: pointer;
    font-size: 1.4em;
    padding: .3em .4em 0 1.1em
}

.slider_icon_tele {
    cursor: pointer;
    font-size: 1.4em;
    padding: .3em .4em 0 1.1em
}

.slider_icon_wide {
    cursor: pointer;
    font-size: 1.4em;
    padding: .3em 1.1em 0 .4em
}

.input_pos {
    padding: 0;
    width: 3.3em;
    height: 1.6em;
    margin-left: 10px;
    margin-top: .4em;
    text-align: center!important
}

.pt_speed_fast {
    padding: 0;
    width: 3.2em;
    height: 1.5em;
    margin: .75em 0 0 0;
    text-align: center!important
}

.pt_speed_slow {
    padding: 0;
    width: 3.2em;
    height: 1.5em;
    margin: .45em 0 0 0;
    text-align: center!important
}

.input_pt_speed {
    padding: 0;
    width: 3.2em;
    height: 1.6em;
    margin: .83em 0 0 0;
    text-align: center!important
}

.input_pos_focus_vertical {
    padding: 0;
    width: 3.5em;
    height: 1.6em;
    margin-top: .96em;
    text-align: center!important
}

.input_pos_zoom_vertical {
    padding: 0;
    width: 3.5em;
    height: 1.6em;
    margin-top: .8em;
    text-align: center!important
}

.svg_focus_zoom {
    width: 20px;
    height: 20px
}

.tab_focus_zoom_vertical {
    display: inline-grid;
    vertical-align: top;
    margin: 0 5px 0 5px;
    justify-items: center;
    margin-bottom: 0!important;
    overflow: hidden!important;
    float: right
}

.pt_speed_ctrl_vertical {
    display: inline-grid!important;
    vertical-align: top;
    margin: 0 10px 0 5px;
    justify-items: center;
    margin-bottom: 0!important;
    float: left
}

.slider_focus_vertical {
    display: flex;
    flex: 1;
    margin: 10px 0 10px 0
}

.slider_zoom_vertical {
    display: flex;
    flex: 1;
    margin: 8px 0 10px 0
}

.slider_pt_speed_vertical {
    display: flex;
    flex: 1;
    margin: 10px 0 10px 0
}

.slider_handle_vertical {
    width: 1.3em!important;
    height: 1em!important;
    background: var(--slider-handle-bg-color)!important
}

.slider_handle_vertical:focus {
    background-color: var(--main-hl-color)!important
}

.slider_icon_focus_vertical {
    cursor: pointer;
    font-size: 1.4em;
    padding: .3em .2em 0 0
}

.slider_icon_zoom_vertical {
    cursor: pointer;
    font-size: 1.4em;
    padding: .4em 0 0 0
}

button {
    display: block;
    margin: 0
}

#tc_manual-dialog input {
    display: inline
}

#canvas {
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 1
}

.grey-button {
    color: #bebaba;
    cursor: not-allowed
}

.black-button {
    color: #2d2c2c
}

li.ui-tabs-tab.ui-corner-top.ui-state-default.ui-tab.customized-tabs.ui-tabs-active.ui-state-active {
    background: var(--button-bg-color);
    border: 0
}

li.ui-tabs-tab.ui-corner-top.ui-state-default.ui-tab.customized-tabs {
    background: var(--button-bg-color);
    border: 0
}

.customized-tabs-indicator {
    height: 6%;
    width: 100%;
    position: absolute;
    bottom: 0
}

.customized-tabs.ui-tabs-active.ui-state-active .customized-tabs-indicator {
    background: var(--main-hl-color)
}

.customized-tabs.ui-state-default a {
    color: #b7b7b7
}

.customized-tabs.ui-tabs-active.ui-state-active a {
    color: var(--text-default-color)
}

.ui-tabs-vertical {
    width: 10%
}

.ui-tabs-vertical .ui-tabs-nav {
    padding: .2em .2em .2em .2em;
    float: left;
    width: 8em
}

.ui-tabs-vertical .ui-tabs-nav li {
    clear: left;
    width: 100%;
    border-bottom-width: 1px!important;
    border-right-width: 0!important;
    margin: 0 -1px .2em 0!important
}

.ui-tabs-vertical .ui-tabs-nav li a {
    display: block;
    width: 80%
}

.ui-tabs-vertical .ui-tabs-nav li.ui-tabs-active {
    padding-bottom: 0;
    padding-right: .1em;
    border-right-width: 1px;
    margin-bottom: 0
}

.ui-tabs-vertical .ui-tabs-panel {
    padding: 0;
    float: left;
    width: 100%
}

.ui-tabs-vertical .ui-tabs-nav .customized-tabs.ui-state-active .customized-tabs-indicator {
    width: 4%;
    height: 100%;
    left: 0
}

.ui-tabs {
    padding: .1em
}

.ui-tabs-anchor-custom {
    float: left;
    padding: .3em 1em!important;
    text-decoration: none
}

.ui-tabs .ui-tabs-nav {
    margin: 0;
    padding: 0 .1em 0
}

.ui-tabs .ui-tabs-nav li.ui-tabs-active {
    margin-bottom: 0;
    padding-bottom: 0
}

#settings_tabs ul li:last-child {
    margin: 0 -1px 0 0!important
}

#tabs-live {
    display: flex;
    padding: .45em 0;
    min-width: var(--live-tabs-min-width)
}

#settings {
    position: relative;
    width: 100%;
    height: 100%;
    float: left;
    min-width: var(--settings-tabs-min-width)
}

#tabs-settings {
    padding: .45em
}

#tabs-gallery {
    padding: .45em
}

#settings_tabs {
    position: relative;
    padding: 0;
    border: 0;
    width: 100%;
    height: 100%;
    overflow: auto
}

#settings_tabs_ul {
    margin-right: 2em
}

#settings_list {
    overflow: auto;
    height: 100%
}

#settings_list>* {
    height: 100%
}

#left-top-ctrl {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    margin-left: 5px
}

#right-top-ctrl {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    right: 0;
    margin-right: 5px
}

#lock_ctrl {
    display: flex;
    float: right
}

#change_language {
    margin-top: 8px;
    margin-right: 10px;
    cursor: pointer
}

#power_on_off {
    margin-top: 8px;
    margin-right: 12px;
    cursor: pointer
}

#power_on {
    color: #0f0!important
}

#pow_to_standy {
    color: #ff0!important
}

.yellow_color {
    color: #ff0!important
}

.green_color {
    color: #0f0!important
}

#power_ctrl {
    position: absolute;
    z-index: 9;
    background-color: var(--select-menu-bg-color);
    margin-top: 2px;
    padding: 6px
}

#language_list {
    position: absolute;
    z-index: 9;
    background-color: var(--select-menu-bg-color);
    margin-top: 2px;
    padding: 6px
}

.li_item {
    padding: 4px
}

#info_camera_name {
    font-size: 1.1em;
    margin-top: .5em;
    margin-left: .2em;
    font-weight: 700
}

#nick_name {
    margin-right: 20px;
    text-align: left
}

.ui-setting-fixw-btn {
    width: 218px;
    margin-right: 20px
}

.ui-setting-fixw-span {
    width: 218px;
    margin-right: 25px
}

.ui-setting-ssl-btn {
    width: 30em;
    height: 31px;
    text-align: start
}

.ic-lock-fill {
    margin-top: 5px;
    width: 24px;
    height: 24px;
    color: var(--text-default-color)
}

.ic-lock-fill.checked {
    color: var(--main-hl-color)
}

div#sys_info>div {
    margin-bottom: 6px
}

.switch-button-background {
    border: 0;
    margin-top: 11px
}

.switch-button-background.checked {
    background: var(--main-hl-color)
}

.switch-button-button {
    top: -.6px
}

.long_press_color,.pressed {
    color: var(--main-hl-color)
}

#ptz-ctrl {
    padding: 0;
    margin-top: 4px;
    display: none;
    border: 0;
    color: var(--text-default-color);
    min-width: var(--ptz-tabs-min-width)
}

.unselectable {
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -moz-user-select: none;
    -ms-user-select: none
}

.selectable {
    user-select: text!important;
    -webkit-user-select: text!important;
    -webkit-touch-callout: text!important;
    -moz-user-select: text!important;
    -ms-user-select: text!important
}

#sys-info {
    display: flex;
    align-items: center;
    padding: 0;
    margin-top: 4px;
    border: 0;
    color: var(--text-default-color);
    height: 50px;
    min-width: var(--sys-info-min-width);
    position: relative
}

.pt-ctrl_vertical {
    padding: 0;
    display: inline-grid!important;
    border: 0;
    color: var(--text-default-color);
    align-items: center;
    justify-items: center;
    margin: 10px 10px 0 10px
}

#pt-ctrl-nav {
    display: grid;
    width: 160px;
    grid-template-columns: repeat(3,50px);
    grid-template-rows: repeat(3,50px);
    grid-gap: 2px 2px;
    padding: 1.2em
}

#pt-ctrl-nav.jstick {
    cursor: move
}

.pt-ctrl-nbtn {
    background: #333;
    border-radius: 2px;
    cursor: pointer
}

#pt-ctrl-leftup {
    border-top-left-radius: 10px
}

#pt-ctrl-rightup {
    border-top-right-radius: 10px
}

#pt-ctrl-leftdown {
    border-bottom-left-radius: 10px
}

#pt-ctrl-rightdown {
    border-bottom-right-radius: 10px
}

#pt-ctrl-joystick {
    background: #333;
    border-radius: 50%
}

.pt-ctrl-nbtn>svg {
    width: 70%;
    height: 70%;
    margin-top: 15%;
    margin-left: 15%
}

.pt-ctrl-home {
    background: #333;
    border-radius: 2px;
    width: 24px;
    height: 24px;
    margin: -.2em 0 0 -.4em;
    flex-shrink: 0;
    cursor: pointer
}

.pt-ctrl-home>svg {
    width: 70%;
    height: 70%;
    margin-top: 15%;
    margin-left: 15%
}

#ptz_tabs {
    border: 0
}

#tabs-gallery {
    overflow: hidden!important
}

ul {
    list-style-type: none
}

.tooltip {
    position: absolute;
    z-index: 99999;
    padding: 5px;
    background: #333;
    color: var(--text-default-color);
    font-size: 14px;
    border-radius: 4px;
    display: none
}

.tooltip-preview {
    margin: 0;
    padding: 0;
    width: 210px;
    height: 120px;
    background: #000
}

.file-download-item {
    display: flex;
    padding: 8px 12px;
    cursor: pointer;
    justify-content: space-between;
    background-color: var(--button-bg-color)
}

#file_list_parent {
    height: 100%;
    border: 0;
    overflow-y: auto
}

#file-list {
    padding-left: 1em
}

#file-list li {
    margin-top: .5em;
    margin-bottom: .5em
}

.file-list-item {
    margin-left: 2em
}

.inline-elements {
    display: inline-block;
    margin-right: 20px
}

.inline-block {
    display: inline-block
}

.flex-center {
    display: flex;
    align-items: center
}

@keyframes spinner-line-fade-more {
    0%,100% {
        opacity: 0
    }

    1% {
        opacity: 1
    }
}

.tabs-ptz {
    position: relative;
    border: 0;
    height: 100%!important;
    overflow: hidden!important;
    padding: .5em .5em!important;
    color: var(--text-default-color)
}

.tabs-ptz_vertical {
    position: relative;
    border: 0;
    width: 455px;
    height: 223px!important;
    overflow: hidden!important;
    padding: .5em .5em!important;
    color: var(--text-default-color)
}

#preset_trace_settings {
    position: relative;
    height: 100%;
    float: left;
    width: 10.45em;
    min-height: var(--live-tabs-preset-min-height);
    border: 0
}

.preset_trace_settings_preset {
    margin-left: calc(-1* var(--live-tabs-info-left-margin))
}

.info_bar_preset {
    margin-left: var(--live-tabs-info-left-margin)
}

.rec_tabs_preset {
    left: var(--live-tabs-info-left-margin)
}

.shortcut_tools_bar_preset {
    left: var(--live-tabs-info-left-margin)
}

.auto_frame_preset {
    left: var(--live-tabs-info-left-margin)
}

.tabs-preset {
    position: relative;
    border: 0;
    overflow: hidden!important;
    padding: .5em .5em!important;
    color: var(--text-default-color)
}

.tabs-preset_vertical {
    position: relative;
    border: 0;
    overflow: hidden;
    padding: 0!important;
    color: var(--text-default-color);
    min-height: 460px
}

#preset-ctrl {
    display: flex;
    padding: 0 .4em
}

.preset-box {
    display: flex;
    flex-direction: column;
    height: 100%
}

.preset-index {
    overflow-y: auto;
    flex: 1;
    padding: .2em 0 0 .3em
}

.preset-crl-bottom {
    margin-top: auto
}

.preset-index-framing {
    overflow-y: auto;
    width: 135px;
    max-height: 670px
}

.preset-page-button {
    padding: .4em 0
}

.preset-button-space {
    margin-bottom: 5px
}

.preset_trace {
    display: block;
    margin-bottom: 0
}

.preset_trace_segment {
    margin-top: .3em;
    width: 10em!important
}

.preset_page_index {
    width: 27px;
    height: 25px;
    padding: 0;
    margin: 0 1px 0 0;
    font-size: 12px!important
}

.pt-ctrl-preset {
    max-width: 100%
}

.preset_page_index:focus {
    border: 1.5px solid var(--main-hl-color)!important
}

.pt-ctrl-preset-index {
    display: inline-block;
    width: 120px;
    height: 85px;
    margin: 3px 2px;
    border: 2px solid var(--button-bg-color);
    cursor: pointer
}

.preset_page_selected {
    border: 1.5px solid var(--main-hl-color)!important
}

.preset_image {
    display: block;
    width: 120px;
    height: 67px
}

.preset_text {
    width: 120px;
    height: 18px;
    font-size: 12px!important;
    display: inline-block;
    text-align: left;
    background-color: var(--button-bg-color);
    line-height: 18px;
    padding: 0;
    border: 0;
    color: var(--text-default-color);
    opacity: 10!important;
    border-radius: 0
}

.preset_text_rename {
    color: var(--main-hl-color)!important
}

.preset_menu {
    position: relative;
    z-index: 2;
    width: 35px;
    height: 35px;
    margin-left: 86px;
    margin-top: -86px;
    background-color: rgb(47,47,47,.8);
    pointer-events: auto;
    cursor: pointer
}

.preset_menu_svg {
    margin: 1em .5em
}

.custom-context-menu {
    position: absolute;
    z-index: 9;
    padding: 0;
    margin: 0;
    background-color: var(--widget-bg-color)
}

.custom-context-menu-active {
    background-color: var(--button-bg-color)
}

.custom-context-menu-item {
    display: flex;
    width: 91px;
    padding: 8px 12px;
    cursor: pointer;
    justify-content: space-between
}

.divider {
    height: 1px;
    background-color: grey;
    margin: 1px 5px
}

.preset-speed-select-list,.preset-speed-unit-select-list,.preset-time-select-list {
    position: absolute;
    z-index: 9;
    width: 70px;
    list-style-type: none;
    padding: 0;
    margin: 0;
    max-height: 140px;
    overflow-y: auto;
    background-color: var(--widget-bg-color)
}

.preset-speed-option {
    display: flex;
    padding: 5px;
    cursor: pointer;
    justify-content: space-between
}

.preset-speed-option-active {
    background-color: var(--button-bg-color)
}

.preset-ctrl-nbtn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 12px 70px 0 0;
    width: 30px;
    height: 30px;
    font-size: 24px!important
}

.preset-ctrl-nbtn_recall {
    margin: .9em .95em 0 0;
    padding: .4em .5em;
    height: 25px;
    width: 57px;
    font-size: 12px!important;
    cursor: pointer
}

.preset-ctrl-nbtn_trace {
    margin: .89em .95em 0 0;
    padding: .4em .5em;
    height: 25px;
    width: 57px;
    font-size: 12px!important;
    cursor: pointer
}

.face_info_selected,.preset_selected {
    outline: 2px solid var(--main-hl-color)!important
}

.pt-ctrl-store {
    background: #333;
    border-radius: 2px;
    width: 25px;
    height: 25px;
    margin: .73em .7em 0 0;
    flex-shrink: 0;
    cursor: pointer
}

.pt-ctrl-recall {
    background: #333;
    border-radius: 2px;
    width: 25px;
    height: 25px;
    margin: .73em .7em 0 0;
    flex-shrink: 0;
    cursor: pointer
}

#pt-ctrl-recall>svg {
    margin-top: .5em;
    margin-left: .6em
}

.pt-ctrl-record {
    background: #333;
    border-radius: 2px;
    width: 25px;
    height: 25px;
    margin: .73em .77em 0 0;
    flex-shrink: 0;
    cursor: pointer
}

.pt-ctrl-delete {
    background: #333;
    border-radius: 2px;
    width: 25px;
    height: 25px;
    margin: .73em 0 0 0;
    flex-shrink: 0;
    cursor: pointer
}

.pt-ctrl-delete>svg {
    width: 55%;
    height: 55%;
    margin: 5.6px
}

.pt_preset_trace_tips {
    display: flex;
    position: relative;
    height: 20px;
    color: #8b8000;
    justify-content: center;
    margin-left: .2em;
    margin-top: .2em;
    font-size: .95em
}

#preset_trace-button {
    width: 5em;
    padding: .4em 0 0 .5em;
    margin-top: 1.1em;
    font-size: 12px
}

#preset_trace_btn {
    width: 22px;
    height: 18px;
    margin-left: .9em;
    cursor: pointer
}

#auto_framing_status {
    display: flex;
    flex: auto;
    flex-direction: row-reverse;
    align-items: center
}

#auto_framing_status>* {
    margin-left: 1em;
    cursor: pointer
}

.tab-ctrl-focus-zoom-speed {
    margin-top: 1em
}

.fz_speed_fast {
    padding: 0;
    width: 2.5em;
    height: 2em;
    margin: .3em 1.2em 0 1.3em;
    text-align: center!important;
    font-size: 12px!important
}

.fz_speed_slow {
    padding: 0;
    width: 2.5em;
    height: 2em;
    margin: .3em 1.2em 0 .3em;
    text-align: center!important;
    font-size: 12px!important
}

.fz_speed_tips {
    color: var(--text-default-color);
    font-size: 12px;
    width: 3.3em;
    margin-left: 10px
}

.speed_tips_vertical {
    color: var(--text-default-color);
    font-size: 12px;
    margin: 1.2em 0 0 .3em
}

.slider_speed {
    display: flex;
    flex: 1;
    margin: .7em 0 0 0
}

.slider_speed .ui-slider-handle {
    width: 1.2em;
    height: 1.2em;
    top: 50%;
    margin-top: -.65em;
    margin-left: -.6em;
    text-align: center;
    line-height: 1.6em;
    background: var(--slider-handle-bg-color)!important
}

.slider_speed .ui-slider-handle:focus {
    background-color: var(--main-hl-color)!important
}

#main_ui ::-webkit-scrollbar {
    width: 4px;
    height: 4px
}

#main_ui ::-webkit-scrollbar-thumb {
    background: var(--button-bg-color)
}

#main_ui ::-webkit-scrollbar-corner {
    background: var(--button-bg-color)
}

.sys_user {
    margin-top: 20px;
    width: 55%
}

.user-list {
    list-style: none;
    padding: 0;
    margin: 0;
    min-width: 760px
}

.user-item {
    display: flex;
    border-bottom: 1px solid var(--button-bg-color);
    padding: .3em;
    justify-content: flex-start
}

.user-tips {
    display: flex;
    width: 25%;
    height: 25px;
    margin: 0 23% 0 0;
    align-items: center
}

.user-permission-tips {
    display: flex;
    width: 19%;
    height: 25px;
    margin: 0 2% 0 0;
    align-items: center
}

.user-operation-tips {
    display: flex;
    width: 25%;
    height: 25px;
    margin: 0;
    align-items: center
}

.user-name {
    display: flex;
    color: var(--text-default-color);
    width: 46%;
    height: 25px;
    margin: 0 2% 0 0;
    align-items: center
}

.user-role {
    display: flex;
    color: var(--text-default-color);
    width: 17%;
    height: 25px;
    margin: 0 4% 0 0;
    align-items: center
}

.user-button {
    background-color: var(--button-bg-color);
    color: var(--text-default-color);
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    margin-right: 5px
}

.user-add {
    background-color: var(--button-bg-color);
    color: var(--text-default-color);
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    margin: .3em .3em 0 0
}

#user_add_permission-button {
    width: 255px
}

label {
    user-select: none
}

.dialog-span {
    float: left;
    margin: 0 15px 0 0
}

.dialog-br-span {
    float: left;
    margin: 10px 15px 10px 0
}

.dialog-brbr-span {
    float: left;
    margin: 20px 15px 20px 0
}

.user_tips {
    font-size: 12px;
    color: red;
    height: 12px;
    margin: 2px
}

.user_error_tips {
    color: red;
    height: 12px;
    margin: 2px
}

.ui-optionmenu {
    display: flex;
    height: 29px;
    width: 15.6em;
    margin-right: 15px;
    border: 1px solid var(--widget-bg-color)!important
}

.ui-optionmenu-input {
    width: 13em;
    pointer-events: none;
    border-radius: 0;
    padding: 0;
    border: 0!important;
    height: 100%;
    margin-right: 1px
}

.ui-optionmenu-direction {
    padding: 0;
    width: 24px;
    margin-right: 1px
}

.ui-optionmenu-up {
    margin-right: 0
}

input:-internal-autofill-selected {
    box-shadow: inset 0 0 0 1000px var(--dialog-title-bg-color)!important;
    -webkit-text-fill-color: var(--text-default-color);
    caret-color: var(--text-default-color)
}

#exp_iso_input,#shutter-angle-coarse,#shutter-angle-fine,#shutter-time {
    display: flex
}

.auto_button_highlight {
    background-color: var(--button-bg-color)!important;
    color: var(--main-hl-color)!important
}

#shutter_angle_fine_auto {
    margin-left: 15px
}

#mt_others {
    margin-top: 10px
}

.ui-segment {
    height: 2.1em;
    width: 220px;
    background-color: #242424;
    color: grey;
    display: inline-flex
}

.ui-segment span.option.active {
    background-color: var(--button-bg-color);
    color: var(--text-default-color)
}

.ui-segment span.option {
    display: flex;
    height: 100%;
    width: 100%;
    text-align: center;
    justify-content: center;
    line-height: 2.1em;
    margin: 0;
    cursor: pointer
}

.ui-segment span.option:last-child {
    border-right: none
}

.segment-select {
    display: none
}

.wrap-button-elements {
    display: block;
    margin-top: 10px
}

.segment_menu {
    display: none
}

.auto_frame {
    height: 230px;
    position: absolute;
    z-index: 4;
    bottom: 7px;
    background: var(--widget-bg-color);
    color: var(--text-default-color);
    margin: 0 .1em
}

#aframe_tips {
    display: flex;
    height: 2em;
    background-color: #2a2a2a;
    align-items: center;
    justify-content: center;
    margin: .5% .9em 0 .9em
}

.aframe_track_button {
    display: grid;
    margin: .6em .8em 0 1.5em;
    width: 7em
}

.aframe_track_button>* {
    margin-bottom: .8em
}

.aframe_track_button>:nth-child(1) {
    height: 2em
}

.aframe_track_button>:nth-child(2) {
    height: 6em
}

.aframe_track_button>:nth-child(3) {
    height: 2em;
    margin-bottom: 0
}

.aframe_ctrl {
    display: flex;
    width: 100%;
    justify-content: space-between
}

.aframe_left_layout {
    display: flex;
    width: 100%;
    margin-left: .9em;
    overflow: auto;
    justify-content: space-between
}

.aframe_track_segment {
    width: 11.2em;
    margin-left: 1.5em
}

.aframe_block {
    width: 11.2em;
    margin-left: 1.5em
}

.aframe_block1 {
    width: 11.2em
}

.aframe_left {
    display: flex
}

.aframe_right {
    display: flex
}

#framingCustomContextMenu {
    display: none;
    position: absolute;
    z-index: 1000;
    background: var(--widget-bg-color);
    color: var(--text-default-color);
    border: 1px solid var(--widget-bg-color);
    border-radius: 5px;
    box-shadow: 1px 1px 2px var(--button-bg-color);
    padding: 0
}

#framingCustomContextMenu li {
    list-style: none;
    padding: 8px 12px;
    cursor: pointer
}

#framingCustomContextMenu li:hover {
    background-color: var(--main-hl-color)
}

.framing_face_reg {
    height: 100%;
    position: absolute;
    z-index: 4;
    bottom: 0;
    background: var(--widget-bg-color);
    color: var(--text-default-color);
    margin-right: 2px
}

.face_reg_container {
    margin: 0 .9em
}

.face_reg_title {
    display: flex;
    text-align: left;
    justify-content: space-between;
    align-items: center
}

.face_reg_ctrl {
    display: flex;
    width: 100%;
    margin: .5em 0;
    overflow: auto
}

.face_reg_left_container {
    width: 32em;
    margin-right: .9em
}

.face_reg_right_container {
    display: flex;
    width: 100%;
    justify-content: space-around
}

.face_reg_info_container {
    display: grid;
    border: 2px solid #2f2f2f;
    overflow: auto;
    grid-template-columns: repeat(10,minmax(83px,1fr));
    max-height: 190px;
    gap: 7px 5px;
    padding: 4px
}

.face_reg_button {
    margin-top: 31px;
    width: 11em;
    height: 29px;
    line-height: 17px
}

.face_close_reg_btn {
    border-radius: 2px;
    width: 25px;
    height: 25px;
    flex-shrink: 0;
    cursor: pointer
}

.face_close_reg_btn>svg {
    margin-top: 5px;
    margin-left: 9px
}

.framing_face_image {
    display: inline-block;
    width: 130px;
    height: 130px;
    margin-left: 1.6em;
    margin-right: 2.2em;
    border: 1px solid #2f2f2f
}

.face_image {
    width: 100%;
    height: 100%
}

.face_reg_top {
    display: flex
}

#face_reg_tips {
    width: 100%;
    height: 20px;
    font-size: 11px;
    text-align: center;
    color: olive;
    margin-top: .5em
}

.face_reg_bottom {
    display: flex;
    margin-top: 2em
}

.framing_face_name {
    width: 11em;
    text-align: center;
    background-color: var(--button-bg-color);
    border: 0;
    color: var(--text-default-color);
    margin-right: 1.4em;
    margin-left: .6em
}

.face_priority {
    width: 11em;
    height: 29px;
    text-align: center;
    background-color: var(--button-bg-color);
    border: 0;
    color: var(--text-default-color)
}

.select_face_svg {
    width: 80%;
    height: 100%;
    margin-left: 10%
}

.face_svg {
    fill: var(--text-default-color);
    fill-rule: evenodd
}

.framing_face_add {
    width: 11em
}

.face-segment {
    width: 11em!important
}

.face-reg-info {
    display: inline-block;
    width: 80px;
    height: 80px;
    border: 2px solid #2f2f2f
}

.face-reg-info-image {
    margin: 0 11%;
    width: 77%;
    height: 77%
}

.face-reg-info-name {
    width: 100%;
    height: 15px;
    color: #e7e7e7;
    background-color: var(--button-bg-color);
    border: 0;
    padding: 0;
    text-align: left;
    border-radius: 0
}

.face-reg-info-name-active {
    opacity: 10!important
}

.face-reg-info-menu {
    position: relative;
    z-index: 5;
    width: 30px;
    height: 30px;
    margin-left: 50px;
    margin-top: -31px;
    background-color: rgb(47,47,47,.8);
    pointer-events: auto;
    cursor: pointer
}

.face-reg-info-priority {
    position: relative;
    font-size: 14px;
    z-index: 5;
    width: 30px;
    height: 30px;
    margin-top: -82px;
    pointer-events: auto;
    cursor: pointer
}

.face-info-activate-list,.face-info-priority-list {
    position: absolute;
    z-index: 9;
    width: 70px;
    list-style-type: none;
    padding: 0;
    margin: 0;
    max-height: 128px;
    overflow-y: auto;
    background-color: var(--widget-bg-color)
}

.framing_face_name_dialog {
    width: 11em;
    text-align: center;
    background-color: var(--button-bg-color);
    border: 0;
    color: var(--text-default-color);
    margin-right: 1.4em;
    margin-left: .6em;
    height: 30px;
    margin-top: 1em
}

#one_push_af_button,#one_push_wb_button {
    height: 2.1em;
    width: 120px;
    background-color: var(--button-bg-color);
    color: var(--text-default-color);
    line-height: 1.2em;
    margin-left: 20px;
    margin-top: 1px;
    border-radius: 2px
}

.ul_image {
    background-color: #242424;
    padding: 0!important
}

.image_tabs {
    width: 99%
}

.image_padding {
    padding-left: .5em
}

.image_saturation_lable {
    margin-left: 142px
}

.image_hue_lable {
    margin-left: 125px
}

.row-elements {
    display: flex;
    align-items: baseline;
    padding-bottom: 5px;
    padding-top: 5px
}

.row-elements-center {
    display: flex;
    padding-bottom: 5px;
    padding-top: 5px;
    align-items: center;
    justify-content: space-between
}

.row-elements-end {
    display: flex;
    padding-bottom: 5px;
    padding-top: 5px;
    align-items: flex-end
}

.row_seg_lable {
    margin-right: 40px
}

.color_correct_segment {
    width: 13.4em!important
}

.row_lable {
    position: relative;
    width: 35px;
    top: -9px;
    margin-right: 5px
}

#matrix_reset {
    margin-left: 20px
}

.hue_spinner {
    margin-right: 13px
}

.ui-spinner-cc-input {
    width: 13.4em!important;
    padding: 0;
    margin: 0;
    height: 27px
}

.color_correction_r {
    color: red
}

.color_correction_r_y {
    color: #f70
}

.color_correction_y {
    color: #ff0
}

.color_correction_y_g {
    color: #7f0
}

.color_correction_g {
    color: #0f0
}

.color_correction_g_c {
    color: #0f7
}

.color_correction_c {
    color: #0ff
}

.color_correction_c_b {
    color: #07f
}

.color_correction_b {
    color: #00f
}

.color_correction_b_m {
    color: #70f
}

.color_correction_m {
    color: #f0f
}

.color_correction_m_r {
    color: #f07
}

#ul_tabs_ptz ul {
    display: flex;
    align-items: center
}

#pt_limit_status {
    margin-left: 55px;
    visibility: hidden
}

#pt_slow_status {
    margin-left: 13px
}

#info_assitool {
    display: flex;
    flex-direction: row-reverse;
    flex: auto;
    height: 18px
}

#assitool_safe_center_btn {
    margin-right: 1em;
    cursor: pointer
}

#assitool_grid_btn {
    margin-right: .9em;
    cursor: pointer
}

.colored-svg path {
    fill: var(--text-default-color)!important
}

.colored-svg-default path {
    fill: grey
}

.colored-svg-detecting path {
    fill: #ff0!important
}

.colored-svg-tracking path {
    fill: green!important
}

.ezframing-colored-svg path {
    fill: var(--main-hl-color)!important
}

.svg_style {
    fill: var(--text-default-color);
    fill-rule: evenodd
}

.ui_slider_spinner {
    text-align: center;
    height: 29px;
    width: 15.4em!important
}

.slider_spinner {
    position: absolute;
    width: 60.7%;
    height: 100%;
    margin-top: 0;
    max-width: 100%!important;
    background: #242424!important
}

.slider_spinner_handle {
    height: 1.9em!important;
    line-height: 1.9!important;
    background: var(--button-bg-color)!important;
    margin-top: -14.5px!important;
    margin-left: -18.6px!important;
    border: 1px solid var(--button-bg-color)!important
}

.sliderSpinner {
    display: flex;
    justify-content: space-around;
    background-color: #242424!important
}

.hue_sat_slider {
    width: 54%
}

.hue_sat_slider_handle {
    margin-left: -18.9px!important
}

.wb_kelvin_spinner {
    width: 30.1em!important
}

.wb_kelvin_slider {
    width: 76.6%
}

.wb_kelvin_slider_handle {
    width: 3.5em!important;
    margin-left: -25.5px!important
}

.wb_tint_slider {
    width: 80%
}

.wb_tint_slider_handle {
    margin-left: -18.3px!important
}

.sys_eth_info {
    display: flex;
    margin-top: 10px;
    color: grey;
    align-items: baseline
}

#eth_cancel,#eth_save {
    margin-top: 10px
}

.framing_ctrl {
    position: relative;
    width: 18em;
    padding-left: 1.5em;
    padding-top: 1em
}

.framing-ctrl-nbtn-up {
    background: var(--button-bg-color);
    border-radius: 2px;
    width: 210px;
    height: 20px;
    padding: 0;
    margin-left: 1.42em
}

.framing-ctrl-nbtn-down {
    background: var(--button-bg-color);
    border-radius: 2px;
    width: 210px;
    height: 20px;
    padding: 0;
    margin-left: 1.42em
}

.framing-ctrl-nbtn-down>svg,.framing-ctrl-nbtn-up>svg {
    width: 50%;
    height: 90%;
    margin-top: .5%;
    margin-left: 3%
}

.framing-ctrl-nbtn-left {
    background: var(--button-bg-color);
    border-radius: 2px;
    width: 20px;
    height: 118px;
    padding: 0;
    margin: 0
}

.framing-ctrl-nbtn-right {
    background: var(--button-bg-color);
    border-radius: 2px;
    width: 20px;
    height: 118px;
    padding: 0;
    margin: 0
}

.framing-ctrl-nbtn-left>svg,.framing-ctrl-nbtn-right>svg {
    width: 90%;
    height: 90%;
    margin-top: 10%;
    margin-left: 13%
}

.framing-preview {
    display: flex;
    flex-direction: column
}

.framing-preview-mid {
    display: flex
}

.top_layout {
    display: flex;
    justify-content: center
}

.user-select-text {
    user-select: text
}

.flex-baseline {
    display: flex;
    align-items: baseline;
    margin: 10px 0 4px 0
}

#datetime {
    display: flex;
    margin-bottom: 4px
}

.datetime-container {
    display: flex;
    width: 219px;
    margin-right: 25px;
    color: grey;
    align-items: baseline
}

.margin_right {
    margin-right: 20px
}

#btn_delete_certificate {
    display: block;
    margin-top: 20px
}

#bth_generate_certificate,#bth_upload_certificate {
    margin-left: 20px
}

/*# sourceMappingURL=styles.min.css.map */

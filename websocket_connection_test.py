#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于验证iOS应用WebSocket连接改进的效果
"""

import asyncio
import websockets
import json
import time
from datetime import datetime

class WebSocketTester:
    def __init__(self, host="*************", port=81):
        self.host = host
        self.port = port
        self.ws_url = f"ws://{host}:{port}/"
        self.connection_attempts = 0
        self.successful_connections = 0
        self.heartbeat_count = 0
        
    async def test_connection(self, timeout=15):
        """测试WebSocket连接"""
        self.connection_attempts += 1
        print(f"\n🔌 尝试连接 #{self.connection_attempts}: {self.ws_url}")
        print(f"⏰ 连接超时设置: {timeout}秒")
        
        try:
            # 模拟iOS应用的连接头部
            headers = {
                "Origin": f"http://{self.host}",
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_0 like Mac OS X) AppleWebKit/605.1.15",
                "Sec-WebSocket-Protocol": "chat"
            }
            
            start_time = time.time()
            
            async with websockets.connect(
                self.ws_url,
                timeout=timeout,
                extra_headers=headers,
                ping_interval=None,  # 禁用自动ping，使用P2-R1的心跳机制
                ping_timeout=None
            ) as websocket:
                
                connection_time = time.time() - start_time
                self.successful_connections += 1
                
                print(f"✅ 连接成功! 耗时: {connection_time:.2f}秒")
                print(f"📊 成功率: {self.successful_connections}/{self.connection_attempts} ({self.successful_connections/self.connection_attempts*100:.1f}%)")
                
                # 监听消息
                await self.listen_for_messages(websocket)
                
        except asyncio.TimeoutError:
            print(f"❌ 连接超时 (>{timeout}秒)")
            print("💡 建议: 增加iOS应用的连接超时时间")
            
        except websockets.exceptions.ConnectionClosed as e:
            print(f"❌ 连接被关闭: {e}")
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            print(f"🔍 错误类型: {type(e).__name__}")
    
    async def listen_for_messages(self, websocket, duration=30):
        """监听WebSocket消息"""
        print(f"👂 开始监听消息 (持续{duration}秒)...")
        
        start_time = time.time()
        last_heartbeat = start_time
        
        try:
            while time.time() - start_time < duration:
                try:
                    # 设置较短的超时来定期检查
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    
                    current_time = time.time()
                    
                    try:
                        data = json.loads(message)
                        message_type = data.get('what', 'unknown')
                        
                        if message_type == 'hb':
                            self.heartbeat_count += 1
                            heartbeat_interval = current_time - last_heartbeat
                            last_heartbeat = current_time
                            print(f"💓 心跳 #{self.heartbeat_count} (间隔: {heartbeat_interval:.1f}s)")
                        else:
                            print(f"📨 收到消息: {message_type}")
                            
                    except json.JSONDecodeError:
                        print(f"📨 收到非JSON消息: {message[:100]}...")
                        
                except asyncio.TimeoutError:
                    # 检查心跳超时
                    if time.time() - last_heartbeat > 12:  # 11秒 + 1秒容差
                        print("⚠️ 心跳超时! (>11秒)")
                        print("💡 建议: 检查iOS应用的心跳超时处理")
                        break
                    continue
                    
        except websockets.exceptions.ConnectionClosed:
            print("🔌 连接已关闭")
        except Exception as e:
            print(f"❌ 监听错误: {e}")
            
        print(f"📊 总共收到 {self.heartbeat_count} 个心跳")
    
    async def run_connection_tests(self, num_tests=3, delay_between_tests=5):
        """运行多次连接测试"""
        print(f"🧪 开始WebSocket连接测试")
        print(f"📍 目标: {self.ws_url}")
        print(f"🔢 测试次数: {num_tests}")
        print(f"⏱️ 测试间隔: {delay_between_tests}秒")
        print("=" * 50)
        
        for i in range(num_tests):
            if i > 0:
                print(f"\n⏳ 等待 {delay_between_tests} 秒...")
                await asyncio.sleep(delay_between_tests)
                
            await self.test_connection()
            
        print("\n" + "=" * 50)
        print(f"📊 测试总结:")
        print(f"   总尝试次数: {self.connection_attempts}")
        print(f"   成功连接: {self.successful_connections}")
        print(f"   成功率: {self.successful_connections/self.connection_attempts*100:.1f}%")
        print(f"   总心跳数: {self.heartbeat_count}")
        
        if self.successful_connections < self.connection_attempts:
            print("\n💡 iOS应用优化建议:")
            print("   1. 增加WebSocket连接超时时间至15-20秒")
            print("   2. 添加网络可达性预检查")
            print("   3. 实现指数退避重连机制")
            print("   4. 优化心跳超时处理(11秒)")

async def main():
    tester = WebSocketTester()
    await tester.run_connection_tests(num_tests=3, delay_between_tests=3)

if __name__ == "__main__":
    print(f"🚀 WebSocket连接测试开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
    print(f"\n🏁 测试结束 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")